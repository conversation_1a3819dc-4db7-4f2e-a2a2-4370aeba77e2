#!/usr/bin/env python3
"""
Real Kudosity Webhook Testing

Test the SMS chatbot system with actual Kudosity webhook payloads
to validate the complete conversation flow in a production-like scenario.
"""

import os
import sys
import json
import requests
import time
from datetime import datetime
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

class KudosityWebhookTester:
    """Test the system with real Kudosity webhook scenarios"""
    
    def __init__(self):
        self.api_base_url = "http://localhost:8000/api/v1"
        self.webhook_endpoint = f"{self.api_base_url}/sms/kudosity/sms-inbound"
        
        # Test phone numbers and scenarios
        self.test_scenarios = [
            {
                "name": "Interested Lead - Complete Flow",
                "phone": "+61-400-555-001",
                "messages": [
                    "Yes, this is a good time to chat",
                    "$150,000",
                    "Yes, I understand the royalty payments",
                    "I have some areas in mind",
                    "Yes, suburban areas are perfect",
                    "I can commit to the training",
                    "Some landscaping experience",
                    "Management experience in retail",
                    "This will be my primary focus",
                    "I want comprehensive support",
                    "Build a sustainable business",
                    "What kind of training do you provide?",
                    "I'd like to schedule a meeting"
                ]
            },
            {
                "name": "Cautious Lead - Questions First",
                "phone": "+**************", 
                "messages": [
                    "I have some questions first",
                    "What are the startup costs?",
                    "How much can I expect to earn?",
                    "What ongoing support do you provide?",
                    "Yes, I'm interested in learning more"
                ]
            },
            {
                "name": "Quick Qualifier",
                "phone": "+**************",
                "messages": [
                    "Sure, let's talk",
                    "$200,000+",
                    "Absolutely",
                    "Yes, I have identified an area",
                    "Definitely",
                    "I can commit to that",
                    "Yes, extensive experience",
                    "Yes, I own a business",
                    "Yes, full-time commitment",
                    "Comprehensive ongoing support",
                    "Expand to multiple territories",
                    "Let's schedule a call"
                ]
            }
        ]
    
    def check_server_status(self):
        """Check if the FastAPI server is running"""
        
        print("🌐 Checking server status...")
        
        try:
            response = requests.get(f"{self.api_base_url}/", timeout=5)
            if response.status_code == 200:
                print("   ✅ FastAPI server is running")
                return True
            else:
                print(f"   ❌ Server returned status: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"   ❌ Server not accessible: {e}")
            print("   💡 Start the server with: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload")
            return False
    
    def create_kudosity_webhook_payload(self, phone_number: str, message_text: str, message_id: str = None) -> dict:
        """Create a realistic Kudosity webhook payload"""
        
        if not message_id:
            message_id = f"kudosity_{int(time.time())}_{hash(phone_number) % 10000}"
        
        return {
            "event_type": "SMS_INBOUND",
            "message_id": message_id,
            "from_number": phone_number,
            "to_number": "+61-400-COMPANY",
            "message_text": message_text,
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "metadata": {
                "carrier": "Telstra",
                "country": "AU",
                "region": "NSW",
                "message_encoding": "UTF-8",
                "webhook_version": "2.1"
            }
        }
    
    def send_webhook_message(self, phone_number: str, message_text: str) -> dict:
        """Send a webhook message and return the response"""
        
        payload = self.create_kudosity_webhook_payload(phone_number, message_text)
        
        print(f"📤 Sending: \"{message_text}\"")
        print(f"   From: {phone_number}")
        print(f"   Message ID: {payload['message_id']}")
        
        try:
            response = requests.post(
                self.webhook_endpoint,
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ Webhook processed successfully")
                print(f"   🤖 Agent response: {result.get('agent_response', 'No response')}")
                print(f"   📊 Stage: {result.get('conversation_stage', 'Unknown')}")
                return result
            else:
                print(f"   ❌ Webhook failed: {response.status_code}")
                print(f"   Error: {response.text}")
                return {"success": False, "error": response.text}
                
        except Exception as e:
            print(f"   ❌ Request failed: {e}")
            return {"success": False, "error": str(e)}
    
    def test_conversation_scenario(self, scenario: dict):
        """Test a complete conversation scenario"""
        
        print(f"\n🎭 Testing Scenario: {scenario['name']}")
        print("=" * 60)
        print(f"📱 Phone: {scenario['phone']}")
        print(f"💬 Messages: {len(scenario['messages'])}")
        
        conversation_results = []
        
        for i, message in enumerate(scenario['messages'], 1):
            print(f"\n📍 Message {i}/{len(scenario['messages'])}")
            
            # Send webhook message
            result = self.send_webhook_message(scenario['phone'], message)
            conversation_results.append(result)
            
            # Wait between messages to simulate real conversation timing
            time.sleep(2)
            
            # Check if conversation failed
            if not result.get('success', True):
                print(f"   ⚠️  Conversation failed, stopping scenario")
                break
        
        # Get final conversation status
        self.check_conversation_status(scenario['phone'])
        
        return conversation_results
    
    def check_conversation_status(self, phone_number: str):
        """Check the final conversation status"""
        
        print(f"\n📊 Checking conversation status for {phone_number}...")
        
        try:
            response = requests.get(
                f"{self.api_base_url}/sms/conversation/{phone_number}",
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('exists'):
                    print(f"   ✅ Conversation found")
                    print(f"   📊 Current stage: {result.get('current_stage')}")
                    print(f"   ⏰ Last message: {result.get('last_message_at')}")
                    
                    messages = result.get('recent_messages', [])
                    print(f"   💬 Total messages: {len(messages)}")
                    
                    # Show last few messages
                    for msg in messages[-3:]:
                        direction = "📤" if msg['message_type'] == 'outbound' else "📥"
                        print(f"      {direction} {msg['message_text'][:50]}...")
                else:
                    print(f"   ❌ No conversation found")
            else:
                print(f"   ❌ Status check failed: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error checking status: {e}")
    
    def test_webhook_error_handling(self):
        """Test webhook error handling scenarios"""
        
        print(f"\n🚨 Testing Error Handling Scenarios")
        print("=" * 50)
        
        error_scenarios = [
            {
                "name": "Invalid JSON",
                "payload": "invalid json",
                "content_type": "application/json"
            },
            {
                "name": "Missing Required Fields",
                "payload": {"event_type": "SMS_INBOUND"},
                "content_type": "application/json"
            },
            {
                "name": "Wrong Event Type",
                "payload": {
                    "event_type": "SMS_OUTBOUND",
                    "message_id": "test_123",
                    "from_number": "+61-***********",
                    "to_number": "+61-400-COMPANY",
                    "message_text": "Test message",
                    "timestamp": datetime.utcnow().isoformat()
                },
                "content_type": "application/json"
            }
        ]
        
        for scenario in error_scenarios:
            print(f"\n🧪 Testing: {scenario['name']}")
            
            try:
                if isinstance(scenario['payload'], str):
                    response = requests.post(
                        self.webhook_endpoint,
                        data=scenario['payload'],
                        headers={"Content-Type": scenario['content_type']},
                        timeout=10
                    )
                else:
                    response = requests.post(
                        self.webhook_endpoint,
                        json=scenario['payload'],
                        headers={"Content-Type": scenario['content_type']},
                        timeout=10
                    )
                
                print(f"   Status: {response.status_code}")
                print(f"   Response: {response.text[:100]}...")
                
            except Exception as e:
                print(f"   Exception: {e}")
    
    def test_concurrent_conversations(self):
        """Test multiple concurrent conversations"""
        
        print(f"\n👥 Testing Concurrent Conversations")
        print("=" * 50)
        
        concurrent_phones = ["+61-400-555-101", "+61-400-555-102", "+61-400-555-103"]
        
        for phone in concurrent_phones:
            print(f"\n📱 Starting conversation: {phone}")
            result = self.send_webhook_message(phone, "Hello, I'm interested in your franchise")
            
            if result.get('success', True):
                print(f"   ✅ Conversation started successfully")
            else:
                print(f"   ❌ Failed to start conversation")
        
        # Send follow-up messages
        for phone in concurrent_phones:
            print(f"\n📱 Follow-up message: {phone}")
            result = self.send_webhook_message(phone, "Yes, this is a good time to chat")
    
    def run_comprehensive_test(self):
        """Run comprehensive Kudosity webhook testing"""
        
        print("🚀 KUDOSITY WEBHOOK COMPREHENSIVE TESTING")
        print("=" * 80)
        print("Testing real SMS chatbot scenarios with Kudosity webhook integration")
        
        # Check server status
        if not self.check_server_status():
            print("\n❌ Cannot proceed without running server")
            return False
        
        # Test main conversation scenarios
        print(f"\n🎭 TESTING CONVERSATION SCENARIOS")
        print("=" * 50)
        
        scenario_results = []
        
        for scenario in self.test_scenarios:
            try:
                results = self.test_conversation_scenario(scenario)
                scenario_results.append({
                    "scenario": scenario['name'],
                    "success": all(r.get('success', True) for r in results),
                    "message_count": len(results)
                })
            except Exception as e:
                print(f"❌ Scenario failed: {e}")
                scenario_results.append({
                    "scenario": scenario['name'],
                    "success": False,
                    "error": str(e)
                })
        
        # Test error handling
        self.test_webhook_error_handling()
        
        # Test concurrent conversations
        self.test_concurrent_conversations()
        
        # Summary
        print(f"\n🎉 TESTING SUMMARY")
        print("=" * 50)
        
        successful_scenarios = sum(1 for r in scenario_results if r['success'])
        total_scenarios = len(scenario_results)
        
        print(f"Conversation scenarios: {successful_scenarios}/{total_scenarios} successful")
        
        for result in scenario_results:
            status = "✅" if result['success'] else "❌"
            print(f"   {status} {result['scenario']}")
            if 'message_count' in result:
                print(f"      Messages processed: {result['message_count']}")
            if 'error' in result:
                print(f"      Error: {result['error']}")
        
        if successful_scenarios == total_scenarios:
            print(f"\n🎯 ALL TESTS PASSED!")
            print(f"\n✅ Kudosity Integration Ready:")
            print(f"   • Webhook endpoint working correctly")
            print(f"   • AI agent processing messages")
            print(f"   • Conversation flow management")
            print(f"   • Lead qualification integration")
            print(f"   • Error handling implemented")
            print(f"   • Concurrent conversations supported")
            
            print(f"\n📱 Production Webhook URL:")
            print(f"   {self.webhook_endpoint}")
            print(f"\n🔧 Configure in Kudosity:")
            print(f"   Event Type: SMS_INBOUND")
            print(f"   Method: POST")
            print(f"   Content-Type: application/json")
        else:
            print(f"\n⚠️  Some tests failed. Review errors above.")
        
        return successful_scenarios == total_scenarios

def main():
    """Main test function"""
    
    try:
        tester = KudosityWebhookTester()
        success = tester.run_comprehensive_test()
        
        if success:
            print(f"\n🎉 KUDOSITY WEBHOOK TESTING SUCCESSFUL!")
            print(f"Your SMS chatbot system is ready for production!")
        else:
            print(f"\n⚠️  TESTING INCOMPLETE")
            print(f"Please address the issues before connecting to Kudosity.")
            
    except Exception as e:
        print(f"❌ Testing failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
