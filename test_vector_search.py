#!/usr/bin/env python3
"""
Test vector search directly to debug the issue
"""

import asyncio
import os
from dotenv import load_dotenv
from openai import AsyncOpenAI
import psycopg
from psycopg.rows import dict_row

# Load environment variables
load_dotenv()

async def test_vector_search():
    """Test vector search directly"""
    
    # Initialize OpenAI client
    client = AsyncOpenAI(api_key=os.getenv("OPENAI_API_KEY"))
    
    # Test question
    question = "What are the startup costs for a Coochie Hydrogreen franchise?"
    
    print(f"Testing question: {question}")
    
    # Generate embedding for the question
    print("Generating embedding for question...")
    response = await client.embeddings.create(
        model="text-embedding-3-small",
        input=question
    )
    
    query_embedding = response.data[0].embedding
    print(f"Generated embedding with {len(query_embedding)} dimensions")
    
    # Connect to database
    database_url = "postgresql://postgres:root@localhost:5432/growthhive"
    
    with psycopg.connect(database_url, row_factory=dict_row) as conn:
        with conn.cursor() as cur:
            # Test 1: Check if franchisor has embedding
            print("\n=== Test 1: Check franchisor embedding ===")
            cur.execute("""
                SELECT id, name, embedding IS NOT NULL as has_embedding 
                FROM franchisors 
                WHERE id = '569976f2-d845-4615-8a91-96e18086adbe'
            """)
            
            franchisor = cur.fetchone()
            if franchisor:
                print(f"Franchisor: {franchisor['name']}")
                print(f"Has embedding: {franchisor['has_embedding']}")
            else:
                print("Franchisor not found!")
                return
            
            # Test 2: Try vector search with very low threshold
            print("\n=== Test 2: Vector search with low threshold ===")
            cur.execute("""
                SELECT
                    id,
                    name,
                    1 - (embedding <=> %s::vector) as similarity_score
                FROM franchisors
                WHERE embedding IS NOT NULL
                    AND is_active = true
                    AND is_deleted = false
                ORDER BY embedding <=> %s::vector
                LIMIT 5
            """, (query_embedding, query_embedding))
            
            results = cur.fetchall()
            print(f"Found {len(results)} results:")
            for result in results:
                print(f"  - {result['name']}: similarity = {result['similarity_score']:.4f}")
            
            # Test 3: Try with different similarity thresholds
            print("\n=== Test 3: Testing different thresholds ===")
            thresholds = [0.1, 0.3, 0.5, 0.7, 0.9]
            
            for threshold in thresholds:
                cur.execute("""
                    SELECT COUNT(*) as count
                    FROM franchisors
                    WHERE embedding IS NOT NULL
                        AND is_active = true
                        AND is_deleted = false
                        AND (1 - (embedding <=> %s::vector)) >= %s
                """, (query_embedding, threshold))
                
                count = cur.fetchone()['count']
                print(f"  Threshold {threshold}: {count} results")

if __name__ == "__main__":
    asyncio.run(test_vector_search())
