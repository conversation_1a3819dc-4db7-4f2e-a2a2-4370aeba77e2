#!/usr/bin/env python3
"""
Test Complete SMS Conversation Flow

End-to-end testing of the dynamic sales script system with AI agent
conversation management from Kudosity webhook to appointment booking.
"""

import os
import sys
import json
import requests
import time
from datetime import datetime
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import sqlalchemy as sa
from sqlalchemy import create_engine, text

def get_database_url():
    """Get database URL from environment"""
    db_url = os.getenv('DATABASE_URL')
    if db_url and 'asyncpg' in db_url:
        db_url = db_url.replace('postgresql+asyncpg://', 'postgresql://')
    return db_url or "postgresql://postgres:root@localhost:5432/growthhive"

class SMSConversationTester:
    """Test the complete SMS conversation flow"""
    
    def __init__(self):
        self.engine = create_engine(get_database_url())
        
        # API configuration
        self.api_base_url = "http://localhost:8000/api/v1"
        
        # Test data
        self.test_phone = "+61-400-TEST-123"
        self.test_franchisor_id = None
        
    def setup_test_data(self):
        """Setup test data for conversation flow"""
        
        print("🔧 Setting up test data...")
        
        with self.engine.connect() as conn:
            # Get first available franchisor
            result = conn.execute(text("SELECT id, name FROM franchisors WHERE is_active = true LIMIT 1"))
            franchisor_row = result.fetchone()
            
            if not franchisor_row:
                raise ValueError("No active franchisor found")
            
            self.test_franchisor_id = str(franchisor_row[0])
            franchisor_name = franchisor_row[1]
            
            print(f"   ✅ Using franchisor: {franchisor_name} ({self.test_franchisor_id})")
            
            # Verify sales scripts exist
            result = conn.execute(text("""
                SELECT script_title, script_type FROM sales_scripts 
                WHERE franchisor_id = :franchisor_id AND is_active = true
            """), {"franchisor_id": self.test_franchisor_id})
            
            scripts = result.fetchall()
            print(f"   📝 Available scripts: {len(scripts)}")
            for script in scripts:
                print(f"      • {script[0]} ({script[1]})")
    
    def test_api_connectivity(self):
        """Test API server connectivity"""
        
        print("\n🌐 Testing API connectivity...")
        
        try:
            response = requests.get(f"{self.api_base_url}/", timeout=5)
            if response.status_code == 200:
                print("   ✅ API server is accessible")
                return True
            else:
                print(f"   ❌ API returned status: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"   ❌ API not accessible: {e}")
            print("   💡 Make sure your FastAPI server is running:")
            print("      uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload")
            return False
    
    def test_sales_script_rendering(self):
        """Test sales script rendering API"""
        
        print("\n📝 Testing sales script rendering...")
        
        try:
            # Test script rendering
            render_data = {
                "franchisor_id": self.test_franchisor_id,
                "script_title": "Initial Greeting",
                "context": {
                    "name_of_cust_rep": "John Smith",
                    "lead_first_name": "Sarah"
                }
            }
            
            response = requests.post(
                f"{self.api_base_url}/sales-scripts/render",
                json=render_data,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                print("   ✅ Script rendering successful")
                print(f"   📄 Rendered script:")
                print(f"      {result['rendered_script']}")
                return True
            else:
                print(f"   ❌ Script rendering failed: {response.status_code}")
                print(f"      {response.text}")
                return False
                
        except Exception as e:
            print(f"   ❌ Error testing script rendering: {e}")
            return False
    
    def test_conversation_initialization(self):
        """Test conversation initialization"""
        
        print("\n👋 Testing conversation initialization...")
        
        try:
            # Initialize conversation
            init_data = {
                "phone_number": self.test_phone,
                "franchisor_id": self.test_franchisor_id,
                "lead_name": "Sarah Johnson",
                "rep_name": "John Smith"
            }
            
            response = requests.post(
                f"{self.api_base_url}/sms/conversation/init",
                json=init_data,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                print("   ✅ Conversation initialized successfully")
                print(f"   📱 Phone: {result['phone_number']}")
                print(f"   💬 Greeting: {result['greeting_message']}")
                print(f"   🆔 Conversation ID: {result['conversation_id']}")
                return result['conversation_id']
            else:
                print(f"   ❌ Conversation initialization failed: {response.status_code}")
                print(f"      {response.text}")
                return None
                
        except Exception as e:
            print(f"   ❌ Error testing conversation initialization: {e}")
            return None
    
    def test_sms_webhook_simulation(self, conversation_id: str):
        """Test SMS webhook with simulated conversation"""
        
        print("\n📱 Testing SMS webhook simulation...")
        
        # Simulate conversation flow
        conversation_steps = [
            {
                "message": "Yes, this is a good time to chat",
                "expected_stage": "qualification",
                "description": "Positive response to greeting"
            },
            {
                "message": "$150,000",
                "expected_stage": "qualification", 
                "description": "Answer to investment budget question"
            },
            {
                "message": "Yes, I understand the royalty payments",
                "expected_stage": "qualification",
                "description": "Answer to royalty question"
            },
            {
                "message": "What kind of training do you provide?",
                "expected_stage": "qa",
                "description": "Question about franchise details"
            },
            {
                "message": "I'd like to schedule a meeting",
                "expected_stage": "scheduling",
                "description": "Request for appointment"
            }
        ]
        
        for i, step in enumerate(conversation_steps, 1):
            print(f"\n   📨 Step {i}: {step['description']}")
            print(f"      Message: \"{step['message']}\"")
            
            # Simulate Kudosity webhook payload
            webhook_data = {
                "event_type": "SMS_INBOUND",
                "message_id": f"test_msg_{i}_{int(time.time())}",
                "from_number": self.test_phone,
                "to_number": "+61-400-COMPANY",
                "message_text": step["message"],
                "timestamp": datetime.utcnow().isoformat(),
                "metadata": {"test": True}
            }
            
            try:
                response = requests.post(
                    f"{self.api_base_url}/sms/kudosity/sms-inbound",
                    json=webhook_data,
                    timeout=15
                )
                
                if response.status_code == 200:
                    result = response.json()
                    print(f"      ✅ Webhook processed successfully")
                    print(f"      🤖 Agent response: {result.get('agent_response', 'No response')}")
                    print(f"      📊 Stage: {result.get('conversation_stage', 'Unknown')}")
                    
                    # Verify conversation stage progression
                    if result.get('conversation_stage') == step['expected_stage']:
                        print(f"      ✅ Stage progression correct")
                    else:
                        print(f"      ⚠️  Expected stage: {step['expected_stage']}, got: {result.get('conversation_stage')}")
                else:
                    print(f"      ❌ Webhook failed: {response.status_code}")
                    print(f"         {response.text}")
                
                # Wait between messages
                time.sleep(2)
                
            except Exception as e:
                print(f"      ❌ Error processing webhook: {e}")
    
    def test_conversation_status(self):
        """Test conversation status retrieval"""
        
        print("\n📊 Testing conversation status...")
        
        try:
            response = requests.get(
                f"{self.api_base_url}/sms/conversation/{self.test_phone}",
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('exists'):
                    print("   ✅ Conversation status retrieved")
                    print(f"   📱 Phone: {self.test_phone}")
                    print(f"   📊 Stage: {result.get('current_stage')}")
                    print(f"   ⏰ Last message: {result.get('last_message_at')}")
                    print(f"   💬 Recent messages: {len(result.get('recent_messages', []))}")
                    
                    # Show recent messages
                    for msg in result.get('recent_messages', [])[-3:]:
                        msg_type = "📤" if msg['message_type'] == 'outbound' else "📥"
                        print(f"      {msg_type} {msg['message_text'][:50]}...")
                else:
                    print("   ❌ No conversation found")
                    
            else:
                print(f"   ❌ Status retrieval failed: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error testing conversation status: {e}")
    
    def verify_database_state(self):
        """Verify database state after conversation"""
        
        print("\n🗄️  Verifying database state...")
        
        with self.engine.connect() as conn:
            # Check conversation state
            result = conn.execute(text("""
                SELECT current_stage, context_data, is_active
                FROM conversation_states 
                WHERE phone_number = :phone
            """), {"phone": self.test_phone})
            
            conv_row = result.fetchone()
            if conv_row:
                print(f"   ✅ Conversation state: {conv_row[0]}")
                print(f"   📊 Active: {conv_row[2]}")
                if conv_row[1]:
                    context = conv_row[1]
                    print(f"   🎯 Context: {context.get('rep_name', 'N/A')} → {context.get('lead_first_name', 'N/A')}")
            
            # Check messages
            result = conn.execute(text("""
                SELECT COUNT(*) as total,
                       COUNT(CASE WHEN message_type = 'inbound' THEN 1 END) as inbound,
                       COUNT(CASE WHEN message_type = 'outbound' THEN 1 END) as outbound
                FROM conversation_messages cm
                JOIN conversation_states cs ON cm.conversation_state_id = cs.id
                WHERE cs.phone_number = :phone
            """), {"phone": self.test_phone})
            
            msg_row = result.fetchone()
            if msg_row:
                print(f"   💬 Messages: {msg_row[0]} total ({msg_row[1]} inbound, {msg_row[2]} outbound)")
            
            # Check lead creation
            result = conn.execute(text("""
                SELECT id, lead_source, qualification_status
                FROM leads 
                WHERE phone = :phone
            """), {"phone": self.test_phone})
            
            lead_row = result.fetchone()
            if lead_row:
                print(f"   👤 Lead created: {lead_row[1]} source, {lead_row[2]} status")
    
    def run_complete_test(self):
        """Run the complete SMS conversation flow test"""
        
        print("🚀 SMS CONVERSATION FLOW TESTING")
        print("=" * 80)
        print("Testing dynamic sales script system with AI agent conversation management")
        
        # Setup
        try:
            self.setup_test_data()
        except Exception as e:
            print(f"❌ Setup failed: {e}")
            return False
        
        # Test API connectivity
        if not self.test_api_connectivity():
            return False
        
        # Test script rendering
        if not self.test_sales_script_rendering():
            return False
        
        # Test conversation initialization
        conversation_id = self.test_conversation_initialization()
        if not conversation_id:
            return False
        
        # Test SMS webhook simulation
        self.test_sms_webhook_simulation(conversation_id)
        
        # Test conversation status
        self.test_conversation_status()
        
        # Verify database state
        self.verify_database_state()
        
        print(f"\n🎉 SMS CONVERSATION FLOW TESTING COMPLETED!")
        print(f"\n✅ System Features Tested:")
        print(f"   • Dynamic sales script rendering with Jinja2")
        print(f"   • AI agent conversation flow management")
        print(f"   • SMS webhook handling (Kudosity simulation)")
        print(f"   • Conversation state tracking")
        print(f"   • Lead qualification integration")
        print(f"   • Database persistence")
        
        print(f"\n📱 Ready for Production:")
        print(f"   • Kudosity webhook endpoint: /api/v1/sms/kudosity/sms-inbound")
        print(f"   • Conversation management: /api/v1/sms/conversation/*")
        print(f"   • Sales script management: /api/v1/sales-scripts/*")
        print(f"   • Complete SMS → qualification → Q&A → scheduling workflow")
        
        return True

def main():
    """Main test function"""
    
    try:
        tester = SMSConversationTester()
        success = tester.run_complete_test()
        
        if success:
            print(f"\n🎯 TESTING SUCCESSFUL!")
            print(f"Your dynamic sales script system is ready for SMS lead qualification!")
        else:
            print(f"\n⚠️  TESTING INCOMPLETE")
            print(f"Some components may need attention before production use.")
            
    except Exception as e:
        print(f"❌ Testing failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
