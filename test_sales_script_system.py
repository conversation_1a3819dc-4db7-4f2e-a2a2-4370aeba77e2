#!/usr/bin/env python3
"""
Test Sales Script System

Direct testing of the dynamic sales script system components
without requiring the full FastAPI server.
"""

import os
import sys
import json
import uuid
from datetime import datetime
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import sqlalchemy as sa
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Import our services
from app.services.sales_script_service import script_renderer, SalesScriptTemplates

def get_database_url():
    """Get database URL from environment"""
    db_url = os.getenv('DATABASE_URL')
    if db_url and 'asyncpg' in db_url:
        db_url = db_url.replace('postgresql+asyncpg://', 'postgresql://')
    return db_url or "postgresql://postgres:root@localhost:5432/growthhive"

class SalesScriptSystemTester:
    """Test the sales script system components"""
    
    def __init__(self):
        self.engine = create_engine(get_database_url())
        self.SessionLocal = sessionmaker(bind=self.engine)
    
    def test_script_rendering_engine(self):
        """Test the Jinja2 script rendering engine"""
        
        print("🎨 Testing Script Rendering Engine")
        print("-" * 50)
        
        # Test basic rendering
        script_template = SalesScriptTemplates.INITIAL_GREETING
        context = {
            "name_of_cust_rep": "John Smith",
            "company_name": "Coffee Masters Australia",
            "lead_first_name": "Sarah"
        }
        
        print(f"📝 Template:")
        print(f"   {script_template}")
        print(f"\n🎯 Context:")
        for key, value in context.items():
            print(f"   {key}: {value}")
        
        # Render script
        rendered = script_renderer.render_sales_script(script_template, context)
        
        print(f"\n✨ Rendered Script:")
        print(f"   {rendered}")
        
        # Test time-based greeting
        greeting = script_renderer.get_time_based_greeting()
        print(f"\n⏰ Time-based greeting: {greeting}")
        
        # Test placeholder extraction
        placeholders = script_renderer.extract_placeholders(script_template)
        print(f"\n🔍 Placeholders found: {placeholders}")
        
        # Test validation
        validation = script_renderer.validate_context(script_template, context)
        print(f"\n✅ Validation result:")
        print(f"   Valid: {validation['is_valid']}")
        print(f"   Missing: {validation['missing_variables']}")
        print(f"   Available: {validation['available_variables']}")
        
        return True
    
    def test_database_integration(self):
        """Test database integration for sales scripts"""
        
        print("\n🗄️  Testing Database Integration")
        print("-" * 50)
        
        with self.engine.connect() as conn:
            # Check sales scripts table
            result = conn.execute(text("SELECT COUNT(*) FROM sales_scripts"))
            script_count = result.fetchone()[0]
            print(f"📝 Sales scripts in database: {script_count}")
            
            # Get sample script
            result = conn.execute(text("""
                SELECT ss.script_title, ss.script_body, ss.script_type, f.name as franchisor_name
                FROM sales_scripts ss
                JOIN franchisors f ON ss.franchisor_id = f.id
                WHERE ss.is_active = true
                LIMIT 1
            """))
            
            script_row = result.fetchone()
            if script_row:
                title, body, script_type, franchisor = script_row
                print(f"\n📋 Sample Script:")
                print(f"   Title: {title}")
                print(f"   Type: {script_type}")
                print(f"   Franchisor: {franchisor}")
                print(f"   Body: {body[:100]}...")
                
                # Test rendering with database script
                context = {
                    "name_of_cust_rep": "Jane Doe",
                    "company_name": franchisor,
                    "lead_first_name": "Michael"
                }
                
                rendered = script_renderer.render_sales_script(body, context)
                print(f"\n✨ Rendered from DB:")
                print(f"   {rendered}")
                
                return True
            else:
                print("❌ No scripts found in database")
                return False
    
    def test_conversation_state_tables(self):
        """Test conversation state table structure"""
        
        print("\n💬 Testing Conversation State Tables")
        print("-" * 50)
        
        with self.engine.connect() as conn:
            # Check table existence
            tables = ['conversation_states', 'conversation_messages']
            
            for table in tables:
                result = conn.execute(text(f"SELECT COUNT(*) FROM {table}"))
                count = result.fetchone()[0]
                print(f"📊 {table}: {count} records")
            
            # Test creating a conversation state
            test_phone = "+61-400-TEST-999"
            
            # Get a franchisor ID
            result = conn.execute(text("SELECT id FROM franchisors WHERE is_active = true LIMIT 1"))
            franchisor_row = result.fetchone()
            
            if franchisor_row:
                franchisor_id = franchisor_row[0]
                
                # Create test conversation state
                conversation_id = str(uuid.uuid4())
                lead_id = str(uuid.uuid4())
                
                # First create a test lead
                conn.execute(text("""
                    INSERT INTO leads (id, phone, lead_source, qualification_status, status, is_active, is_deleted, created_at, updated_at)
                    VALUES (:id, :phone, :source, :qual_status, :status, :is_active, :is_deleted, :created_at, :updated_at)
                    ON CONFLICT (id) DO NOTHING
                """), {
                    "id": lead_id,
                    "phone": test_phone,
                    "source": "test",
                    "qual_status": "new",
                    "status": "new",
                    "is_active": True,
                    "is_deleted": False,
                    "created_at": datetime.utcnow(),
                    "updated_at": datetime.utcnow()
                })
                
                # Create conversation state
                conn.execute(text("""
                    INSERT INTO conversation_states 
                    (id, lead_id, phone_number, franchisor_id, current_stage, context_data, is_active, created_at, updated_at)
                    VALUES (:id, :lead_id, :phone, :franchisor_id, :stage, :context, :is_active, :created_at, :updated_at)
                    ON CONFLICT (id) DO NOTHING
                """), {
                    "id": conversation_id,
                    "lead_id": lead_id,
                    "phone": test_phone,
                    "franchisor_id": franchisor_id,
                    "stage": "greeting",
                    "context": json.dumps({"rep_name": "Test Rep", "lead_first_name": "Test Lead"}),
                    "is_active": True,
                    "created_at": datetime.utcnow(),
                    "updated_at": datetime.utcnow()
                })
                
                # Create test message
                message_id = str(uuid.uuid4())
                conn.execute(text("""
                    INSERT INTO conversation_messages
                    (id, conversation_state_id, message_text, message_type, message_source, created_at)
                    VALUES (:id, :conv_id, :text, :type, :source, :created_at)
                    ON CONFLICT (id) DO NOTHING
                """), {
                    "id": message_id,
                    "conv_id": conversation_id,
                    "text": "Hello, this is a test message",
                    "type": "outbound",
                    "source": "system",
                    "created_at": datetime.utcnow()
                })
                
                conn.commit()
                
                print(f"✅ Created test conversation state: {conversation_id[:8]}...")
                print(f"✅ Created test message: {message_id[:8]}...")
                
                return True
            else:
                print("❌ No franchisor found for testing")
                return False
    
    def test_script_templates(self):
        """Test predefined script templates"""
        
        print("\n📋 Testing Script Templates")
        print("-" * 50)
        
        templates = {
            "Initial Greeting": SalesScriptTemplates.INITIAL_GREETING,
            "Follow-up Message": SalesScriptTemplates.FOLLOW_UP_MESSAGE,
            "Qualification Intro": SalesScriptTemplates.QUALIFICATION_INTRO,
            "Appointment Scheduling": SalesScriptTemplates.APPOINTMENT_SCHEDULING,
            "Document Q&A Intro": SalesScriptTemplates.DOCUMENT_QA_INTRO
        }
        
        context = {
            "greeting": "Good afternoon",
            "lead_first_name": "Alex",
            "name_of_cust_rep": "Sarah Wilson",
            "company_name": "Coffee Masters Australia"
        }
        
        for name, template in templates.items():
            print(f"\n📝 {name}:")
            
            # Extract placeholders
            placeholders = script_renderer.extract_placeholders(template)
            print(f"   Placeholders: {placeholders}")
            
            # Render template
            try:
                rendered = script_renderer.render_sales_script(template, context)
                print(f"   ✅ Rendered successfully")
                print(f"   Preview: {rendered[:80]}...")
            except Exception as e:
                print(f"   ❌ Rendering failed: {e}")
        
        return True
    
    def test_webhook_payload_structure(self):
        """Test webhook payload structure"""
        
        print("\n📡 Testing Webhook Payload Structure")
        print("-" * 50)
        
        # Sample Kudosity webhook payload
        sample_payload = {
            "event_type": "SMS_INBOUND",
            "message_id": f"test_msg_{int(datetime.now().timestamp())}",
            "from_number": "+61-***********",
            "to_number": "+61-400-COMPANY",
            "message_text": "Yes, this is a good time to chat",
            "timestamp": datetime.utcnow().isoformat(),
            "metadata": {
                "test": True,
                "source": "webhook_test"
            }
        }
        
        print(f"📨 Sample webhook payload:")
        for key, value in sample_payload.items():
            print(f"   {key}: {value}")
        
        # Test message analysis
        message = sample_payload["message_text"]
        print(f"\n🔍 Message Analysis:")
        print(f"   Message: \"{message}\"")
        
        # Simple intent detection
        positive_words = ["yes", "sure", "okay", "good", "great", "absolutely"]
        negative_words = ["no", "not", "busy", "later", "never"]
        question_words = ["what", "how", "when", "where", "why", "who"]
        
        is_positive = any(word in message.lower() for word in positive_words)
        is_negative = any(word in message.lower() for word in negative_words)
        is_question = any(word in message.lower() for word in question_words) or "?" in message
        
        print(f"   Positive response: {is_positive}")
        print(f"   Negative response: {is_negative}")
        print(f"   Contains question: {is_question}")
        
        return True
    
    def run_all_tests(self):
        """Run all sales script system tests"""
        
        print("🚀 SALES SCRIPT SYSTEM TESTING")
        print("=" * 80)
        print("Testing dynamic sales script system components")
        
        tests = [
            ("Script Rendering Engine", self.test_script_rendering_engine),
            ("Database Integration", self.test_database_integration),
            ("Conversation State Tables", self.test_conversation_state_tables),
            ("Script Templates", self.test_script_templates),
            ("Webhook Payload Structure", self.test_webhook_payload_structure)
        ]
        
        results = []
        
        for test_name, test_func in tests:
            try:
                print(f"\n{'='*20} {test_name} {'='*20}")
                result = test_func()
                results.append((test_name, result))
                
                if result:
                    print(f"✅ {test_name} PASSED")
                else:
                    print(f"❌ {test_name} FAILED")
                    
            except Exception as e:
                print(f"❌ {test_name} ERROR: {e}")
                results.append((test_name, False))
        
        # Summary
        print(f"\n🎉 TESTING SUMMARY")
        print("=" * 50)
        
        passed = sum(1 for _, result in results if result)
        total = len(results)
        
        print(f"Tests passed: {passed}/{total}")
        
        for test_name, result in results:
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"   {status} {test_name}")
        
        if passed == total:
            print(f"\n🎯 ALL TESTS PASSED!")
            print(f"\n✅ System Components Ready:")
            print(f"   • Dynamic sales script rendering with Jinja2")
            print(f"   • Database integration for script storage")
            print(f"   • Conversation state management")
            print(f"   • Webhook payload handling")
            print(f"   • Template system for common scenarios")
            
            print(f"\n📱 Ready for SMS Integration:")
            print(f"   • Kudosity webhook endpoint ready")
            print(f"   • AI agent conversation flow prepared")
            print(f"   • Lead qualification integration available")
            print(f"   • Appointment scheduling workflow ready")
        else:
            print(f"\n⚠️  Some tests failed. Please review the errors above.")
        
        return passed == total

def main():
    """Main test function"""
    
    try:
        tester = SalesScriptSystemTester()
        success = tester.run_all_tests()
        
        if success:
            print(f"\n🎉 SALES SCRIPT SYSTEM READY!")
            print(f"Your dynamic sales script system is fully functional!")
        else:
            print(f"\n⚠️  SYSTEM NEEDS ATTENTION")
            print(f"Please address the failed tests before production use.")
            
    except Exception as e:
        print(f"❌ Testing failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
