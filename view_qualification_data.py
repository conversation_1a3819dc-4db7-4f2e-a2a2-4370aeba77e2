#!/usr/bin/env python3
"""
View Lead Qualification Data

Display all the populated pre-qualification data in a readable format.
"""

import os
import sys
import json
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import sqlalchemy as sa
from sqlalchemy import create_engine, text

def get_database_url():
    """Get database URL from environment"""
    db_url = os.getenv('DATABASE_URL')
    if db_url and 'asyncpg' in db_url:
        db_url = db_url.replace('postgresql+asyncpg://', 'postgresql://')
    return db_url or "postgresql://postgres:root@localhost:5432/growthhive"

def view_qualification_data():
    """Display all qualification data"""
    
    print("📊 LEAD QUALIFICATION DATA OVERVIEW")
    print("=" * 80)
    
    engine = create_engine(get_database_url())
    
    with engine.connect() as conn:
        # 1. Lead Summary
        print("\n👥 LEAD SUMMARY")
        print("-" * 50)
        
        result = conn.execute(text("""
            SELECT 
                full_name,
                phone,
                email,
                location,
                qualification_percentage,
                qualification_level,
                total_qualification_score,
                max_possible_score,
                is_fully_qualified,
                qualified_at
            FROM leads 
            WHERE lead_source = 'sms_campaign'
            ORDER BY qualification_percentage DESC
        """))
        
        for row in result.fetchall():
            name, phone, email, location, percentage, level, total, max_score, qualified, qualified_at = row
            status_icon = "✅" if qualified else "❌"
            print(f"{status_icon} {name}")
            print(f"   📞 {phone} | 📧 {email}")
            print(f"   📍 {location}")
            print(f"   📊 Score: {total}/{max_score} ({percentage:.1f}%)")
            print(f"   🎯 Level: {level.replace('_', ' ').title()}")
            if qualified_at:
                print(f"   ⏰ Qualified: {qualified_at.strftime('%Y-%m-%d %H:%M')}")
            print()
        
        # 2. Response Analysis
        print("\n💬 RESPONSE ANALYSIS")
        print("-" * 50)
        
        result = conn.execute(text("""
            SELECT 
                q.category,
                COUNT(lr.id) as total_responses,
                COUNT(CASE WHEN lr.is_qualified THEN 1 END) as qualified_responses,
                AVG(lr.score_awarded) as avg_score,
                AVG(lr.confidence_score) as avg_confidence,
                AVG(lr.response_time_seconds) as avg_response_time
            FROM lead_responses lr
            JOIN questions q ON lr.question_id = q.id
            JOIN leads l ON lr.lead_id = l.id
            WHERE l.lead_source = 'sms_campaign'
            GROUP BY q.category
            ORDER BY avg_score DESC
        """))
        
        for row in result.fetchall():
            category, total, qualified, avg_score, avg_confidence, avg_time = row
            qualification_rate = (qualified / total * 100) if total > 0 else 0
            
            print(f"📝 {category}")
            print(f"   Responses: {total} | Qualified: {qualified} ({qualification_rate:.1f}%)")
            print(f"   Avg Score: {avg_score:.1f} | Avg Confidence: {avg_confidence:.2f}")
            if avg_time:
                print(f"   Avg Response Time: {avg_time:.0f} seconds")
            print()
        
        # 3. Sample Conversations
        print("\n📱 SAMPLE SMS CONVERSATIONS")
        print("-" * 50)
        
        # Get top and bottom performers
        result = conn.execute(text("""
            SELECT id, full_name, qualification_percentage
            FROM leads 
            WHERE lead_source = 'sms_campaign'
            ORDER BY qualification_percentage DESC
            LIMIT 1
        """))
        
        top_lead = result.fetchone()
        
        result = conn.execute(text("""
            SELECT id, full_name, qualification_percentage
            FROM leads 
            WHERE lead_source = 'sms_campaign'
            ORDER BY qualification_percentage ASC
            LIMIT 1
        """))
        
        bottom_lead = result.fetchone()
        
        for lead_data in [top_lead, bottom_lead]:
            if not lead_data:
                continue
                
            lead_id, name, percentage = lead_data
            print(f"\n🎯 {name} ({percentage:.1f}%)")
            print("-" * 30)
            
            result = conn.execute(text("""
                SELECT 
                    q.question_text,
                    lr.response_text,
                    lr.score_awarded,
                    q.score_weight,
                    lr.is_qualified,
                    lr.confidence_score,
                    lr.response_time_seconds,
                    q.category
                FROM lead_responses lr
                JOIN questions q ON lr.question_id = q.id
                WHERE lr.lead_id = :lead_id
                ORDER BY q.order_sequence
                LIMIT 5
            """), {"lead_id": lead_id})
            
            for i, row in enumerate(result.fetchall(), 1):
                question, response, score, max_score, qualified, confidence, time, category = row
                status_icon = "✅" if qualified else "❌"
                
                print(f"{status_icon} Q{i} ({category}): {score}/{max_score} pts")
                print(f"   ❓ {question[:60]}...")
                print(f"   💬 SMS: \"{response}\"")
                if time:
                    print(f"   ⏱️  Response time: {time}s | Confidence: {confidence:.2f}")
                print()
        
        # 4. Session Information
        print("\n🔄 QUALIFICATION SESSIONS")
        print("-" * 50)
        
        result = conn.execute(text("""
            SELECT 
                qs.session_token,
                l.full_name,
                qs.status,
                qs.started_at,
                qs.completed_at,
                (qs.completed_at - qs.started_at) as duration
            FROM qualification_sessions qs
            JOIN leads l ON qs.lead_id = l.id
            WHERE qs.session_token LIKE 'sms_session_%'
            ORDER BY qs.started_at
        """))
        
        for row in result.fetchall():
            token, name, status, started, completed, duration = row
            
            print(f"📱 {token}")
            print(f"   👤 {name}")
            print(f"   📊 Status: {status.upper()}")
            print(f"   ⏰ Started: {started.strftime('%Y-%m-%d %H:%M:%S')}")
            if completed:
                print(f"   ✅ Completed: {completed.strftime('%Y-%m-%d %H:%M:%S')}")
                if duration:
                    print(f"   ⏱️  Duration: {duration.total_seconds():.1f} seconds")
            print()
        
        # 5. Statistics Summary
        print("\n📈 STATISTICS SUMMARY")
        print("-" * 50)
        
        result = conn.execute(text("""
            SELECT 
                COUNT(*) as total_leads,
                COUNT(CASE WHEN is_fully_qualified THEN 1 END) as qualified_leads,
                AVG(qualification_percentage) as avg_percentage,
                MAX(qualification_percentage) as max_percentage,
                MIN(qualification_percentage) as min_percentage
            FROM leads 
            WHERE lead_source = 'sms_campaign'
        """))
        
        stats = result.fetchone()
        total, qualified, avg_pct, max_pct, min_pct = stats
        
        print(f"📊 Total SMS Leads: {total}")
        print(f"✅ Qualified Leads: {qualified} ({qualified/total*100:.1f}%)")
        print(f"📈 Average Score: {avg_pct:.1f}%")
        print(f"🏆 Highest Score: {max_pct:.1f}%")
        print(f"📉 Lowest Score: {min_pct:.1f}%")
        
        # Response count
        result = conn.execute(text("""
            SELECT COUNT(*) 
            FROM lead_responses lr
            JOIN leads l ON lr.lead_id = l.id
            WHERE l.lead_source = 'sms_campaign'
        """))
        
        response_count = result.fetchone()[0]
        print(f"💬 Total SMS Responses: {response_count}")
        
        # Session count
        result = conn.execute(text("""
            SELECT COUNT(*) 
            FROM qualification_sessions 
            WHERE session_token LIKE 'sms_session_%'
        """))
        
        session_count = result.fetchone()[0]
        print(f"🔄 SMS Sessions: {session_count}")

def main():
    """Main function"""
    
    try:
        view_qualification_data()
        
        print(f"\n🎉 DATA REVIEW COMPLETED!")
        print(f"\n✅ Your pre-qualification tables are fully populated with:")
        print(f"   • SMS lead qualification data")
        print(f"   • Scored responses with confidence levels")
        print(f"   • Session management for SMS workflows")
        print(f"   • Response time tracking")
        print(f"   • Complete qualification analytics")
        
        print(f"\n📱 Ready for SMS Integration:")
        print(f"   • Database schema supports real SMS workflows")
        print(f"   • AI agent can process responses and score them")
        print(f"   • Human interaction tracking with response times")
        print(f"   • Qualification results for lead routing")
        
    except Exception as e:
        print(f"❌ Error viewing data: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
