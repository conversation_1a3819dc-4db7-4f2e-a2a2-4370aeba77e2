#!/usr/bin/env python3
"""
Test Lead Qualification with Real Answers

Interactive test that demonstrates the complete lead qualification workflow
with actual database integration and answer processing.
"""

import asyncio
import os
import sys
import uuid
import json
from datetime import datetime, timedelta
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import sqlalchemy as sa
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

def get_database_url():
    """Get database URL from environment"""
    db_url = os.getenv('DATABASE_URL')
    if db_url and 'asyncpg' in db_url:
        db_url = db_url.replace('postgresql+asyncpg://', 'postgresql://')
    return db_url or "postgresql://postgres:root@localhost:5432/growthhive"

class LeadQualificationTester:
    """Test the lead qualification system with real database integration"""
    
    def __init__(self):
        self.engine = create_engine(get_database_url())
        self.SessionLocal = sessionmaker(bind=self.engine)
        
    def create_test_lead(self, email: str, full_name: str) -> str:
        """Create a test lead in the database"""

        with self.SessionLocal() as db:
            lead_id = str(uuid.uuid4())

            # Insert lead
            db.execute(sa.text("""
                INSERT INTO leads (id, email, full_name, status, is_deleted, created_at, updated_at)
                VALUES (:id, :email, :full_name, :status, :is_deleted, :created_at, :updated_at)
            """), {
                "id": lead_id,
                "email": email,
                "full_name": full_name,
                "status": "new",
                "is_deleted": False,
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            })

            db.commit()
            print(f"✅ Created lead: {full_name} ({email})")
            return lead_id
    
    def start_qualification_session(self, lead_id: str) -> tuple[str, list]:
        """Start a qualification session and return session token and questions"""
        
        with self.SessionLocal() as db:
            # Get questions from template
            result = db.execute(sa.text("""
                SELECT qt.question_ids 
                FROM question_templates qt 
                WHERE qt.template_name = 'coochie_lawn_care' AND qt.is_active = true
            """))
            
            template_row = result.fetchone()
            if not template_row:
                raise ValueError("No active template found")
            
            question_ids = json.loads(template_row[0]) if isinstance(template_row[0], str) else template_row[0]
            
            # Get question details
            questions = []
            for q_id in question_ids:
                result = db.execute(sa.text("""
                    SELECT id, question_id, category, question_text, expected_answers, score_weight
                    FROM pre_qualification_questions 
                    WHERE id = :id AND is_active = true
                """), {"id": q_id})
                
                q_row = result.fetchone()
                if q_row:
                    questions.append({
                        "id": str(q_row[0]),
                        "question_id": q_row[1],
                        "category": q_row[2],
                        "question_text": q_row[3],
                        "expected_answers": json.loads(q_row[4]) if isinstance(q_row[4], str) else q_row[4],
                        "score_weight": q_row[5]
                    })
            
            # Create session
            session_token = str(uuid.uuid4())
            expires_at = datetime.utcnow() + timedelta(hours=2)
            
            db.execute(sa.text("""
                INSERT INTO qualification_sessions 
                (id, lead_id, session_token, status, current_question_index, questions_order, started_at, expires_at)
                VALUES (:id, :lead_id, :token, :status, :index, :order, :started, :expires)
            """), {
                "id": str(uuid.uuid4()),
                "lead_id": lead_id,
                "token": session_token,
                "status": "active",
                "index": 0,
                "order": json.dumps([q["id"] for q in questions]),
                "started": datetime.utcnow(),
                "expires": expires_at
            })
            
            # Update lead status
            db.execute(sa.text("""
                UPDATE leads SET status = 'qualifying' WHERE id = :id
            """), {"id": lead_id})
            
            db.commit()
            
            print(f"✅ Started qualification session: {session_token[:8]}...")
            return session_token, questions
    
    def submit_answer(self, session_token: str, question_id: str, answer: str) -> dict:
        """Submit an answer and get evaluation results"""
        
        with self.SessionLocal() as db:
            # Get session
            result = db.execute(sa.text("""
                SELECT lead_id, current_question_index, questions_order, status
                FROM qualification_sessions 
                WHERE session_token = :token
            """), {"token": session_token})
            
            session_row = result.fetchone()
            if not session_row:
                raise ValueError("Session not found")
            
            lead_id, current_index, questions_order, status = session_row
            if status != "active":
                raise ValueError("Session not active")
            
            # Get question details
            result = db.execute(sa.text("""
                SELECT id, question_text, expected_answers, score_weight
                FROM pre_qualification_questions 
                WHERE id = :id
            """), {"id": question_id})
            
            q_row = result.fetchone()
            if not q_row:
                raise ValueError("Question not found")
            
            q_id, question_text, expected_answers, score_weight = q_row
            expected_answers = json.loads(expected_answers) if isinstance(expected_answers, str) else expected_answers
            
            # Evaluate answer
            evaluation = self.evaluate_answer(answer, expected_answers, score_weight)
            
            # Store response
            response_id = str(uuid.uuid4())
            db.execute(sa.text("""
                INSERT INTO lead_responses 
                (id, lead_id, question_id, question_text, lead_answer, is_qualified, score_awarded, confidence_score, created_at)
                VALUES (:id, :lead_id, :q_id, :q_text, :answer, :qualified, :score, :confidence, :created)
            """), {
                "id": response_id,
                "lead_id": lead_id,
                "q_id": question_id,
                "q_text": question_text,
                "answer": answer,
                "qualified": evaluation["is_qualified"],
                "score": evaluation["score_awarded"],
                "confidence": evaluation["confidence_score"],
                "created": datetime.utcnow()
            })
            
            # Update session progress
            questions_list = json.loads(questions_order) if isinstance(questions_order, str) else questions_order
            new_index = current_index + 1
            
            db.execute(sa.text("""
                UPDATE qualification_sessions 
                SET current_question_index = :index
                WHERE session_token = :token
            """), {"index": new_index, "token": session_token})
            
            # Check if complete
            is_complete = new_index >= len(questions_list)
            
            if is_complete:
                # Mark session complete
                db.execute(sa.text("""
                    UPDATE qualification_sessions 
                    SET status = 'completed', completed_at = :completed
                    WHERE session_token = :token
                """), {"completed": datetime.utcnow(), "token": session_token})
                
                # Calculate final results
                final_result = self.calculate_final_results(db, lead_id)
                
                db.commit()
                
                return {
                    "status": "completed",
                    "evaluation": evaluation,
                    "final_result": final_result
                }
            else:
                db.commit()
                
                return {
                    "status": "continue",
                    "evaluation": evaluation,
                    "next_question_index": new_index,
                    "questions_remaining": len(questions_list) - new_index
                }
    
    def evaluate_answer(self, answer: str, expected_answers: list, max_score: int) -> dict:
        """Evaluate an answer against expected responses"""
        
        answer_clean = answer.strip().lower()
        
        # Check for exact matches
        for expected in expected_answers:
            if answer_clean == expected.strip().lower():
                return {
                    "is_qualified": True,
                    "score_awarded": max_score,
                    "confidence_score": 1.0,
                    "match_type": "exact",
                    "matched_answer": expected
                }
        
        # Check for partial matches (keyword overlap)
        for expected in expected_answers:
            expected_words = set(expected.lower().split())
            answer_words = set(answer_clean.split())
            
            # Remove common words
            common_words = {"the", "and", "or", "but", "yes", "no", "can", "will", "have", "has", "i", "am", "is"}
            expected_words -= common_words
            answer_words -= common_words
            
            if expected_words and answer_words:
                overlap = len(expected_words.intersection(answer_words))
                if overlap > 0:
                    confidence = overlap / len(expected_words)
                    if confidence >= 0.5:  # At least 50% word overlap
                        score = int(max_score * confidence)
                        return {
                            "is_qualified": True,
                            "score_awarded": score,
                            "confidence_score": confidence,
                            "match_type": "partial",
                            "matched_answer": expected
                        }
        
        # No match
        return {
            "is_qualified": False,
            "score_awarded": 0,
            "confidence_score": 0.0,
            "match_type": "no_match",
            "matched_answer": None
        }
    
    def calculate_final_results(self, db, lead_id: str) -> dict:
        """Calculate final qualification results"""
        
        # Get all responses
        result = db.execute(sa.text("""
            SELECT score_awarded, is_qualified
            FROM lead_responses 
            WHERE lead_id = :lead_id
        """), {"lead_id": lead_id})
        
        responses = result.fetchall()
        
        # Calculate totals
        total_score = sum(r[0] for r in responses)
        qualified_responses = sum(1 for r in responses if r[1])
        
        # Get max possible score from template
        result = db.execute(sa.text("""
            SELECT total_possible_score, qualification_threshold
            FROM question_templates 
            WHERE template_name = 'coochie_lawn_care'
        """))
        
        template_row = result.fetchone()
        max_possible_score, threshold = template_row
        
        percentage_score = (total_score / max_possible_score) * 100
        is_qualified = total_score >= threshold
        
        # Determine qualification level
        if percentage_score >= 90:
            qualification_level = "highly_qualified"
        elif percentage_score >= 80:
            qualification_level = "qualified"
        elif percentage_score >= 60:
            qualification_level = "partially_qualified"
        else:
            qualification_level = "unqualified"
        
        # Store summary
        summary_id = str(uuid.uuid4())
        db.execute(sa.text("""
            INSERT INTO lead_qualification_summaries 
            (id, lead_id, total_score, max_possible_score, percentage_score, is_fully_qualified, 
             qualification_level, questions_answered, total_questions, qualified_at, created_at)
            VALUES (:id, :lead_id, :total, :max, :percentage, :qualified, :level, :answered, :total_q, :qualified_at, :created)
        """), {
            "id": summary_id,
            "lead_id": lead_id,
            "total": total_score,
            "max": max_possible_score,
            "percentage": percentage_score,
            "qualified": is_qualified,
            "level": qualification_level,
            "answered": len(responses),
            "total_q": len(responses),
            "qualified_at": datetime.utcnow(),
            "created": datetime.utcnow()
        })
        
        # Update lead status
        final_status = "qualified" if is_qualified else "disqualified"
        db.execute(sa.text("""
            UPDATE leads SET status = :status WHERE id = :id
        """), {"status": final_status, "id": lead_id})
        
        return {
            "lead_id": lead_id,
            "total_score": total_score,
            "max_possible_score": max_possible_score,
            "percentage_score": percentage_score,
            "is_qualified": is_qualified,
            "qualification_level": qualification_level,
            "questions_answered": len(responses),
            "qualified_responses": qualified_responses
        }
    
    def run_interactive_test(self):
        """Run an interactive qualification test"""
        
        print("🚀 INTERACTIVE LEAD QUALIFICATION TEST")
        print("=" * 60)
        
        # Create test lead
        print("\n👤 Creating Test Lead...")
        lead_id = self.create_test_lead(
            email="<EMAIL>",
            full_name="Alex Johnson"
        )
        
        # Start session
        print("\n🎯 Starting Qualification Session...")
        session_token, questions = self.start_qualification_session(lead_id)
        
        print(f"\n📝 Total Questions: {len(questions)}")
        print(f"🎯 Qualification Threshold: 72 points (80%)")
        
        # Interactive Q&A
        total_score = 0
        for i, question in enumerate(questions, 1):
            print(f"\n" + "="*60)
            print(f"QUESTION {i}/{len(questions)} - {question['category']} ({question['score_weight']} points)")
            print("="*60)
            print(f"❓ {question['question_text']}")
            print(f"\n💡 Expected answers: {', '.join(question['expected_answers'])}")
            
            # Get user input
            answer = input(f"\n✏️  Your answer: ").strip()
            
            if not answer:
                answer = question['expected_answers'][0]  # Use first expected answer as default
                print(f"   Using default: {answer}")
            
            # Submit answer
            result = self.submit_answer(session_token, question['id'], answer)
            evaluation = result['evaluation']
            
            # Display results
            status_icon = "✅" if evaluation['is_qualified'] else "❌"
            print(f"\n{status_icon} Result: {evaluation['score_awarded']}/{question['score_weight']} points")
            print(f"   Match Type: {evaluation['match_type']}")
            print(f"   Confidence: {evaluation['confidence_score']:.2f}")
            
            total_score += evaluation['score_awarded']
            print(f"   Running Total: {total_score} points")
            
            if result['status'] == 'completed':
                final_result = result['final_result']
                
                print(f"\n🎉 QUALIFICATION COMPLETED!")
                print("="*60)
                print(f"Final Score: {final_result['total_score']}/{final_result['max_possible_score']}")
                print(f"Percentage: {final_result['percentage_score']:.1f}%")
                print(f"Status: {final_result['qualification_level'].upper()}")
                print(f"Qualified: {'✅ YES' if final_result['is_qualified'] else '❌ NO'}")
                print(f"Questions Answered: {final_result['questions_answered']}")
                print(f"Qualified Responses: {final_result['qualified_responses']}")
                break
        
        return final_result

def main():
    """Main test function"""
    
    print("🧪 LEAD QUALIFICATION SYSTEM - ANSWER TESTING")
    print("=" * 70)
    
    try:
        tester = LeadQualificationTester()
        
        # Test database connection
        with tester.engine.connect() as conn:
            conn.execute(sa.text("SELECT 1"))
        print("✅ Database connection successful")
        
        # Run interactive test
        result = tester.run_interactive_test()
        
        print(f"\n📊 Test completed successfully!")
        print(f"Lead qualification system is working correctly with real database integration.")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
