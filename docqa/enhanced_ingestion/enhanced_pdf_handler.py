"""
Enhanced PDF Handler with OCR, Layout Analysis, and Advanced Text Extraction

This module provides powerful PDF processing capabilities including:
- OCR for scanned PDFs and images
- Layout analysis to preserve document structure
- Table detection and extraction
- Chart and image analysis with AI
- Multi-modal content processing
"""

import fitz  # PyMuPDF
import pytesseract
from PIL import Image
import cv2
import numpy as np
import pandas as pd
import camelot
import tabula
from typing import List, Dict, Any, Optional, Tuple
import io
import base64
import re
from pathlib import Path
import logging
from dataclasses import dataclass
import openai
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

logger = logging.getLogger(__name__)

@dataclass
class ExtractedContent:
    """Container for extracted content from PDF"""
    text: str
    images: List[Dict[str, Any]]
    tables: List[Dict[str, Any]]
    charts: List[Dict[str, Any]]
    metadata: Dict[str, Any]
    structure: Dict[str, Any]

@dataclass
class ProcessingOptions:
    """Configuration for PDF processing"""
    use_ocr: bool = True
    extract_tables: bool = True
    extract_images: bool = True
    analyze_charts: bool = True
    preserve_layout: bool = True
    enhance_text: bool = True
    parallel_processing: bool = True
    ocr_languages: List[str] = None

class EnhancedPDFHandler:
    """Enhanced PDF handler with advanced processing capabilities"""
    
    def __init__(self, openai_api_key: str, options: ProcessingOptions = None):
        self.openai_client = openai.OpenAI(api_key=openai_api_key)
        self.options = options or ProcessingOptions()
        
        # Configure OCR languages
        if self.options.ocr_languages is None:
            self.options.ocr_languages = ['eng']  # Default to English
        
        # Text enhancement patterns
        self.text_patterns = {
            'bullet_points': re.compile(r'^[\s]*[•·▪▫◦‣⁃]\s*', re.MULTILINE),
            'numbered_lists': re.compile(r'^[\s]*\d+[\.\)]\s*', re.MULTILINE),
            'headings': re.compile(r'^[A-Z][A-Z\s]{2,}$', re.MULTILINE),
            'phone_numbers': re.compile(r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b'),
            'emails': re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'),
            'urls': re.compile(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+'),
            'currency': re.compile(r'\$[\d,]+(?:\.\d{2})?|\b\d+\s*(?:dollars?|USD|AUD)\b', re.IGNORECASE)
        }
    
    def process_pdf(self, file_path: Path) -> ExtractedContent:
        """
        Process PDF with enhanced capabilities
        
        Args:
            file_path: Path to PDF file
            
        Returns:
            ExtractedContent with all extracted information
        """
        logger.info(f"Starting enhanced PDF processing: {file_path}")
        start_time = time.time()
        
        try:
            # Open PDF document
            doc = fitz.open(str(file_path))
            
            if doc.needs_pass:
                raise ValueError("PDF is password protected")
            
            if len(doc) == 0:
                raise ValueError("PDF has no pages")
            
            # Initialize content containers
            all_text = []
            all_images = []
            all_tables = []
            all_charts = []
            document_structure = {
                'pages': len(doc),
                'sections': [],
                'headings': [],
                'lists': []
            }
            
            # Process pages in parallel if enabled
            if self.options.parallel_processing and len(doc) > 3:
                content = self._process_pages_parallel(doc)
            else:
                content = self._process_pages_sequential(doc)
            
            # Combine results
            for page_content in content:
                all_text.append(page_content['text'])
                all_images.extend(page_content['images'])
                all_tables.extend(page_content['tables'])
                all_charts.extend(page_content['charts'])
                document_structure['sections'].extend(page_content['structure']['sections'])
                document_structure['headings'].extend(page_content['structure']['headings'])
                document_structure['lists'].extend(page_content['structure']['lists'])
            
            # Combine and enhance text
            combined_text = '\n\n'.join(all_text)
            if self.options.enhance_text:
                combined_text = self._enhance_text(combined_text)
            
            # Extract metadata
            metadata = self._extract_metadata(doc, file_path)
            
            doc.close()
            
            processing_time = time.time() - start_time
            logger.info(f"PDF processing completed in {processing_time:.2f}s")
            logger.info(f"Extracted: {len(combined_text)} chars, {len(all_images)} images, {len(all_tables)} tables, {len(all_charts)} charts")
            
            return ExtractedContent(
                text=combined_text,
                images=all_images,
                tables=all_tables,
                charts=all_charts,
                metadata=metadata,
                structure=document_structure
            )
            
        except Exception as e:
            logger.error(f"Enhanced PDF processing failed: {str(e)}")
            raise
    
    def _process_pages_sequential(self, doc: fitz.Document) -> List[Dict[str, Any]]:
        """Process pages sequentially"""
        results = []
        
        for page_num in range(len(doc)):
            page_content = self._process_single_page(doc, page_num)
            results.append(page_content)
            
        return results
    
    def _process_pages_parallel(self, doc: fitz.Document) -> List[Dict[str, Any]]:
        """Process pages in parallel"""
        results = [None] * len(doc)
        
        with ThreadPoolExecutor(max_workers=4) as executor:
            # Submit all page processing tasks
            future_to_page = {
                executor.submit(self._process_single_page, doc, page_num): page_num
                for page_num in range(len(doc))
            }
            
            # Collect results
            for future in as_completed(future_to_page):
                page_num = future_to_page[future]
                try:
                    results[page_num] = future.result()
                except Exception as e:
                    logger.error(f"Error processing page {page_num}: {str(e)}")
                    results[page_num] = self._empty_page_content()
        
        return results
    
    def _process_single_page(self, doc: fitz.Document, page_num: int) -> Dict[str, Any]:
        """Process a single page with all enhancements"""
        page = doc[page_num]
        
        # Extract text with layout preservation
        text_content = self._extract_text_with_layout(page)
        
        # OCR for images and poor quality text
        if self.options.use_ocr:
            ocr_text = self._perform_ocr_on_page(page)
            if ocr_text and len(ocr_text.strip()) > len(text_content.strip()):
                text_content = ocr_text
        
        # Extract images
        images = []
        if self.options.extract_images:
            images = self._extract_images_from_page(page, page_num)
        
        # Extract tables
        tables = []
        if self.options.extract_tables:
            tables = self._extract_tables_from_page(doc, page_num)
        
        # Analyze charts
        charts = []
        if self.options.analyze_charts:
            charts = self._analyze_charts_on_page(page, page_num)
        
        # Extract structure
        structure = self._extract_page_structure(text_content, page_num)
        
        return {
            'text': text_content,
            'images': images,
            'tables': tables,
            'charts': charts,
            'structure': structure
        }
    
    def _extract_text_with_layout(self, page: fitz.Page) -> str:
        """Extract text while preserving layout and structure"""
        if self.options.preserve_layout:
            # Use dict mode for better layout preservation
            blocks = page.get_text("dict")
            text_content = self._process_text_blocks(blocks)
        else:
            # Simple text extraction
            text_content = page.get_text()
        
        return text_content
    
    def _process_text_blocks(self, blocks: Dict) -> str:
        """Process text blocks to preserve structure"""
        text_parts = []
        
        for block in blocks.get("blocks", []):
            if "lines" in block:  # Text block
                block_text = []
                
                for line in block["lines"]:
                    line_text = []
                    
                    for span in line.get("spans", []):
                        text = span.get("text", "").strip()
                        if text:
                            # Preserve formatting information
                            font_size = span.get("size", 12)
                            font_flags = span.get("flags", 0)
                            
                            # Mark headings based on font size and style
                            if font_size > 14 or (font_flags & 2**4):  # Bold
                                text = f"**{text}**"
                            
                            line_text.append(text)
                    
                    if line_text:
                        block_text.append(" ".join(line_text))
                
                if block_text:
                    text_parts.append("\n".join(block_text))
        
        return "\n\n".join(text_parts)
    
    def _perform_ocr_on_page(self, page: fitz.Page) -> str:
        """Perform OCR on page image"""
        try:
            # Convert page to image
            pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))  # 2x zoom for better OCR
            img_data = pix.tobytes("png")
            
            # Convert to PIL Image
            image = Image.open(io.BytesIO(img_data))
            
            # Enhance image for better OCR
            image = self._enhance_image_for_ocr(image)
            
            # Perform OCR
            ocr_text = pytesseract.image_to_string(
                image,
                lang='+'.join(self.options.ocr_languages),
                config='--oem 3 --psm 6'
            )
            
            return ocr_text
            
        except Exception as e:
            logger.warning(f"OCR failed: {str(e)}")
            return ""
    
    def _enhance_image_for_ocr(self, image: Image.Image) -> Image.Image:
        """Enhance image quality for better OCR results"""
        # Convert PIL to OpenCV
        cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
        
        # Convert to grayscale
        gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
        
        # Apply denoising
        denoised = cv2.fastNlMeansDenoising(gray)
        
        # Apply adaptive thresholding
        thresh = cv2.adaptiveThreshold(
            denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
        )
        
        # Convert back to PIL
        enhanced_image = Image.fromarray(thresh)
        
        return enhanced_image

    def _extract_images_from_page(self, page: fitz.Page, page_num: int) -> List[Dict[str, Any]]:
        """Extract and analyze images from page"""
        images = []

        try:
            image_list = page.get_images()

            for img_index, img in enumerate(image_list):
                try:
                    xref = img[0]
                    pix = fitz.Pixmap(page.parent, xref)

                    if pix.n - pix.alpha < 4:  # GRAY or RGB
                        img_data = pix.tobytes("png")

                        # Analyze image with AI if it looks like a chart/diagram
                        analysis = self._analyze_image_content(img_data)

                        image_info = {
                            'page': page_num + 1,
                            'index': img_index,
                            'width': pix.width,
                            'height': pix.height,
                            'data': img_data,
                            'analysis': analysis,
                            'type': self._classify_image_type(pix.width, pix.height, analysis)
                        }
                        images.append(image_info)

                    pix = None  # Free memory

                except Exception as e:
                    logger.warning(f"Failed to extract image {img_index} from page {page_num + 1}: {str(e)}")

        except Exception as e:
            logger.warning(f"Failed to extract images from page {page_num + 1}: {str(e)}")

        return images

    def _analyze_image_content(self, img_data: bytes) -> str:
        """Analyze image content using GPT-4 Vision"""
        try:
            # Convert image to base64
            img_base64 = base64.b64encode(img_data).decode('utf-8')

            response = self.openai_client.chat.completions.create(
                model="gpt-4-vision-preview",
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": "Analyze this image and describe what it contains. If it's a chart, graph, or diagram, explain what data it shows. If it contains text, extract the key information. Be concise but comprehensive."
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{img_base64}"
                                }
                            }
                        ]
                    }
                ],
                max_tokens=500
            )

            return response.choices[0].message.content

        except Exception as e:
            logger.warning(f"Image analysis failed: {str(e)}")
            return "Image analysis not available"

    def _classify_image_type(self, width: int, height: int, analysis: str) -> str:
        """Classify image type based on dimensions and analysis"""
        analysis_lower = analysis.lower()

        if any(word in analysis_lower for word in ['chart', 'graph', 'plot', 'diagram']):
            return 'chart'
        elif any(word in analysis_lower for word in ['table', 'data', 'rows', 'columns']):
            return 'table'
        elif any(word in analysis_lower for word in ['logo', 'brand', 'company']):
            return 'logo'
        elif width > 400 and height > 300:
            return 'illustration'
        else:
            return 'image'

    def _extract_tables_from_page(self, doc: fitz.Document, page_num: int) -> List[Dict[str, Any]]:
        """Extract tables from page using multiple methods"""
        tables = []

        try:
            # Method 1: Try Camelot (works well for well-formatted tables)
            try:
                camelot_tables = camelot.read_pdf(
                    str(doc.name),
                    pages=str(page_num + 1),
                    flavor='lattice'
                )

                for i, table in enumerate(camelot_tables):
                    if table.accuracy > 50:  # Only include high-accuracy tables
                        table_data = {
                            'page': page_num + 1,
                            'index': i,
                            'method': 'camelot',
                            'accuracy': table.accuracy,
                            'data': table.df.to_dict('records'),
                            'raw_data': table.df.to_string(),
                            'shape': table.df.shape
                        }
                        tables.append(table_data)
            except Exception as e:
                logger.debug(f"Camelot table extraction failed for page {page_num + 1}: {str(e)}")

            # Method 2: Try Tabula (works well for stream tables)
            try:
                tabula_tables = tabula.read_pdf(
                    str(doc.name),
                    pages=page_num + 1,
                    multiple_tables=True,
                    pandas_options={'header': 0}
                )

                for i, df in enumerate(tabula_tables):
                    if not df.empty and df.shape[0] > 1:  # Must have data
                        table_data = {
                            'page': page_num + 1,
                            'index': len(tables) + i,
                            'method': 'tabula',
                            'accuracy': 85,  # Tabula doesn't provide accuracy
                            'data': df.to_dict('records'),
                            'raw_data': df.to_string(),
                            'shape': df.shape
                        }
                        tables.append(table_data)
            except Exception as e:
                logger.debug(f"Tabula table extraction failed for page {page_num + 1}: {str(e)}")

        except Exception as e:
            logger.warning(f"Table extraction failed for page {page_num + 1}: {str(e)}")

        return tables

    def _analyze_charts_on_page(self, page: fitz.Page, page_num: int) -> List[Dict[str, Any]]:
        """Analyze charts and diagrams on page"""
        charts = []

        try:
            # Get page as image for chart detection
            pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))
            img_data = pix.tobytes("png")

            # Use AI to detect and analyze charts
            chart_analysis = self._detect_charts_in_image(img_data)

            if chart_analysis and 'charts_detected' in chart_analysis:
                for i, chart_info in enumerate(chart_analysis['charts_detected']):
                    chart_data = {
                        'page': page_num + 1,
                        'index': i,
                        'type': chart_info.get('type', 'unknown'),
                        'description': chart_info.get('description', ''),
                        'data_points': chart_info.get('data_points', []),
                        'insights': chart_info.get('insights', ''),
                        'confidence': chart_info.get('confidence', 0.5)
                    }
                    charts.append(chart_data)

        except Exception as e:
            logger.warning(f"Chart analysis failed for page {page_num + 1}: {str(e)}")

        return charts

    def _detect_charts_in_image(self, img_data: bytes) -> Dict[str, Any]:
        """Detect and analyze charts in image using AI"""
        try:
            img_base64 = base64.b64encode(img_data).decode('utf-8')

            response = self.openai_client.chat.completions.create(
                model="gpt-4-vision-preview",
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": """Analyze this image for charts, graphs, or diagrams. For each chart found, provide:
1. Type (bar chart, pie chart, line graph, etc.)
2. Description of what it shows
3. Key data points or values if visible
4. Main insights or trends
5. Confidence level (0-1)

Return as JSON format with 'charts_detected' array."""
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{img_base64}"
                                }
                            }
                        ]
                    }
                ],
                max_tokens=800
            )

            # Try to parse JSON response
            import json
            try:
                return json.loads(response.choices[0].message.content)
            except:
                # Fallback to text analysis
                return {
                    'charts_detected': [{
                        'type': 'unknown',
                        'description': response.choices[0].message.content,
                        'confidence': 0.7
                    }]
                }

        except Exception as e:
            logger.warning(f"Chart detection failed: {str(e)}")
            return {'charts_detected': []}

    def _extract_page_structure(self, text: str, page_num: int) -> Dict[str, Any]:
        """Extract structural elements from page text"""
        structure = {
            'sections': [],
            'headings': [],
            'lists': []
        }

        lines = text.split('\n')
        current_section = None

        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue

            # Detect headings (all caps, bold markers, or large font indicators)
            if self.text_patterns['headings'].match(line) or line.startswith('**') and line.endswith('**'):
                heading = {
                    'text': line.replace('**', ''),
                    'page': page_num + 1,
                    'line': i,
                    'level': self._determine_heading_level(line)
                }
                structure['headings'].append(heading)

                if current_section:
                    structure['sections'].append(current_section)

                current_section = {
                    'title': heading['text'],
                    'page': page_num + 1,
                    'start_line': i,
                    'content': []
                }

            # Detect lists
            elif (self.text_patterns['bullet_points'].match(line) or
                  self.text_patterns['numbered_lists'].match(line)):
                list_item = {
                    'text': line,
                    'page': page_num + 1,
                    'line': i,
                    'type': 'bullet' if '•' in line or '·' in line else 'numbered'
                }
                structure['lists'].append(list_item)

                if current_section:
                    current_section['content'].append(line)

            else:
                if current_section:
                    current_section['content'].append(line)

        # Add final section
        if current_section:
            structure['sections'].append(current_section)

        return structure

    def _determine_heading_level(self, text: str) -> int:
        """Determine heading level based on text characteristics"""
        if text.isupper() and len(text) > 10:
            return 1  # Main heading
        elif text.startswith('**') and text.endswith('**'):
            return 2  # Subheading
        elif text.endswith(':'):
            return 3  # Section header
        else:
            return 4  # Minor heading

    def _enhance_text(self, text: str) -> str:
        """Enhance extracted text with better formatting and structure"""
        # Clean up common PDF extraction issues
        text = re.sub(r'\n\s*\n\s*\n', '\n\n', text)  # Multiple newlines
        text = re.sub(r'([a-z])([A-Z])', r'\1 \2', text)  # Missing spaces
        text = re.sub(r'(\w)-\s*\n\s*(\w)', r'\1\2', text)  # Hyphenated words across lines

        # Enhance specific patterns
        for pattern_name, pattern in self.text_patterns.items():
            if pattern_name == 'currency':
                # Standardize currency formatting
                text = pattern.sub(lambda m: f"[CURRENCY: {m.group()}]", text)
            elif pattern_name == 'phone_numbers':
                # Standardize phone number formatting
                text = pattern.sub(lambda m: f"[PHONE: {m.group()}]", text)
            elif pattern_name == 'emails':
                # Mark emails for better recognition
                text = pattern.sub(lambda m: f"[EMAIL: {m.group()}]", text)
            elif pattern_name == 'urls':
                # Mark URLs for better recognition
                text = pattern.sub(lambda m: f"[URL: {m.group()}]", text)

        # Add section markers for better chunking
        lines = text.split('\n')
        enhanced_lines = []

        for line in lines:
            if self.text_patterns['headings'].match(line):
                enhanced_lines.append(f"\n=== {line} ===\n")
            elif self.text_patterns['bullet_points'].match(line):
                enhanced_lines.append(f"• {line.lstrip('•·▪▫◦‣⁃ ')}")
            elif self.text_patterns['numbered_lists'].match(line):
                enhanced_lines.append(line)
            else:
                enhanced_lines.append(line)

        return '\n'.join(enhanced_lines)

    def _extract_metadata(self, doc: fitz.Document, file_path: Path) -> Dict[str, Any]:
        """Extract comprehensive metadata from PDF"""
        metadata = {
            'filename': file_path.name,
            'file_size': file_path.stat().st_size,
            'pages': len(doc),
            'created': None,
            'modified': None,
            'author': None,
            'title': None,
            'subject': None,
            'keywords': [],
            'language': None,
            'processing_info': {
                'ocr_used': self.options.use_ocr,
                'tables_extracted': self.options.extract_tables,
                'images_analyzed': self.options.extract_images,
                'charts_analyzed': self.options.analyze_charts,
                'layout_preserved': self.options.preserve_layout
            }
        }

        # Extract PDF metadata
        pdf_metadata = doc.metadata
        if pdf_metadata:
            metadata.update({
                'title': pdf_metadata.get('title', ''),
                'author': pdf_metadata.get('author', ''),
                'subject': pdf_metadata.get('subject', ''),
                'creator': pdf_metadata.get('creator', ''),
                'producer': pdf_metadata.get('producer', ''),
                'created': pdf_metadata.get('creationDate', ''),
                'modified': pdf_metadata.get('modDate', '')
            })

        # Extract keywords from content (simple approach)
        try:
            first_page_text = doc[0].get_text() if len(doc) > 0 else ""
            keywords = self._extract_keywords(first_page_text)
            metadata['keywords'] = keywords
        except:
            pass

        return metadata

    def _extract_keywords(self, text: str) -> List[str]:
        """Extract keywords from text"""
        # Simple keyword extraction - can be enhanced with NLP
        words = re.findall(r'\b[A-Z][a-z]+\b', text)  # Capitalized words
        word_freq = {}

        for word in words:
            if len(word) > 3:  # Skip short words
                word_freq[word] = word_freq.get(word, 0) + 1

        # Return top 10 most frequent capitalized words
        return sorted(word_freq.keys(), key=word_freq.get, reverse=True)[:10]

    def _empty_page_content(self) -> Dict[str, Any]:
        """Return empty page content structure"""
        return {
            'text': '',
            'images': [],
            'tables': [],
            'charts': [],
            'structure': {
                'sections': [],
                'headings': [],
                'lists': []
            }
        }
