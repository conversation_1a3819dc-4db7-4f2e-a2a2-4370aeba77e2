"""
Enhanced DocQA Service with Advanced Document Processing

This service integrates the layout-aware processor with the existing DocQA system
to provide powerful document understanding and question answering capabilities.
"""

import asyncio
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional
import uuid
from dataclasses import dataclass
import time

from .layout_aware_processor import LayoutAwareProcessor, AdvancedConfig, ProcessedDocument
from ..vector_store.embeddings import EmbeddingService
from ..vector_store.pgvector_store import PgVectorStore
from ..services.enhanced_openai_service import EnhancedOpenAIService
from ..types import DocumentMetadata, IngestionResult

logger = logging.getLogger(__name__)

@dataclass
class EnhancedIngestionResult:
    """Result of enhanced document ingestion"""
    success: bool
    document_id: str
    chunks_created: int
    layout_elements_detected: int
    images_processed: int
    tables_extracted: int
    charts_analyzed: int
    processing_time: float
    error_message: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

class EnhancedDocQAService:
    """Enhanced DocQA service with advanced document processing"""
    
    def __init__(self, 
                 openai_api_key: str,
                 database_url: str,
                 config: AdvancedConfig = None):
        
        self.config = config or AdvancedConfig()
        
        # Initialize advanced processor
        self.processor = LayoutAwareProcessor(
            openai_api_key=openai_api_key,
            config=self.config
        )
        
        # Initialize vector store and embedding service
        self.embedding_service = EmbeddingService(
            api_key=openai_api_key,
            model="text-embedding-3-small"
        )
        
        self.vector_store = PgVectorStore(
            database_url=database_url,
            embedding_service=self.embedding_service
        )
        
        # Initialize QA service
        self.qa_service = EnhancedOpenAIService(
            openai_api_key=openai_api_key,
            vector_store=self.vector_store,
            embedding_service=self.embedding_service
        )
    
    async def process_document_enhanced(self, 
                                      file_path: Path,
                                      document_id: Optional[str] = None,
                                      table_name: str = "documents") -> EnhancedIngestionResult:
        """
        Process document with enhanced capabilities
        
        Args:
            file_path: Path to the document file
            document_id: Optional document ID (generates if not provided)
            table_name: Target table for storage
            
        Returns:
            EnhancedIngestionResult with processing details
        """
        start_time = time.time()
        
        if document_id is None:
            document_id = str(uuid.uuid4())
        
        logger.info(f"Starting enhanced document processing: {file_path}")
        
        try:
            # Step 1: Process document with layout awareness
            processed_doc = await self.processor.process_document(file_path)
            
            # Step 2: Create enhanced chunks with context
            enhanced_chunks = self._create_enhanced_chunks(processed_doc, document_id)
            
            # Step 3: Generate embeddings for all chunks
            chunks_with_embeddings = await self._generate_embeddings_for_chunks(enhanced_chunks)
            
            # Step 4: Store in vector database
            stored_chunks = await self._store_enhanced_chunks(
                chunks_with_embeddings, 
                document_id, 
                table_name,
                processed_doc.metadata
            )
            
            # Step 5: Store structured elements separately
            await self._store_structured_elements(processed_doc, document_id, table_name)
            
            processing_time = time.time() - start_time
            
            result = EnhancedIngestionResult(
                success=True,
                document_id=document_id,
                chunks_created=len(stored_chunks),
                layout_elements_detected=len(processed_doc.layout_elements),
                images_processed=len(processed_doc.images),
                tables_extracted=len(processed_doc.tables),
                charts_analyzed=len(processed_doc.charts),
                processing_time=processing_time,
                metadata=processed_doc.metadata
            )
            
            logger.info(f"Enhanced processing completed successfully in {processing_time:.2f}s")
            logger.info(f"Created {result.chunks_created} chunks, detected {result.layout_elements_detected} layout elements")
            
            return result
            
        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = f"Enhanced document processing failed: {str(e)}"
            logger.error(error_msg)
            
            return EnhancedIngestionResult(
                success=False,
                document_id=document_id,
                chunks_created=0,
                layout_elements_detected=0,
                images_processed=0,
                tables_extracted=0,
                charts_analyzed=0,
                processing_time=processing_time,
                error_message=error_msg
            )
    
    def _create_enhanced_chunks(self, processed_doc: ProcessedDocument, document_id: str) -> List[Dict[str, Any]]:
        """Create enhanced chunks with layout and context information"""
        enhanced_chunks = []
        
        # Process semantic chunks from the processor
        for i, chunk in enumerate(processed_doc.semantic_chunks):
            # Find related layout elements
            related_elements = self._find_related_layout_elements(
                chunk, processed_doc.layout_elements
            )
            
            # Create enhanced chunk with additional context
            enhanced_chunk = {
                'id': f"{document_id}_chunk_{i}",
                'document_id': document_id,
                'content': chunk['content'],
                'chunk_index': i,
                'page_numbers': chunk.get('page_numbers', []),
                'chunk_type': chunk.get('type', 'text'),
                'layout_context': {
                    'related_elements': [
                        {
                            'type': elem.type,
                            'confidence': elem.confidence,
                            'page': elem.page_num,
                            'position': elem.metadata.get('position', 'unknown')
                        }
                        for elem in related_elements
                    ],
                    'structural_info': chunk.get('structural_info', {}),
                    'reading_order': chunk.get('reading_order', i)
                },
                'enhanced_metadata': {
                    'contains_financial_info': self._contains_financial_info(chunk['content']),
                    'contains_contact_info': self._contains_contact_info(chunk['content']),
                    'contains_requirements': self._contains_requirements(chunk['content']),
                    'contains_support_info': self._contains_support_info(chunk['content']),
                    'key_entities': self._extract_key_entities(chunk['content']),
                    'content_quality_score': self._calculate_content_quality(chunk['content'])
                },
                'cross_references': self._find_cross_references(chunk, processed_doc)
            }
            
            enhanced_chunks.append(enhanced_chunk)
        
        # Add chunks for structured elements (tables, charts, images)
        enhanced_chunks.extend(self._create_structured_element_chunks(processed_doc, document_id))
        
        logger.info(f"Created {len(enhanced_chunks)} enhanced chunks")
        return enhanced_chunks
    
    def _find_related_layout_elements(self, chunk: Dict[str, Any], layout_elements: List) -> List:
        """Find layout elements related to a chunk"""
        related_elements = []
        
        chunk_pages = chunk.get('page_numbers', [])
        
        for element in layout_elements:
            if element.page_num in chunk_pages:
                # Check if chunk content contains element content
                if element.content.strip() and element.content.strip() in chunk['content']:
                    related_elements.append(element)
        
        return related_elements
    
    def _contains_financial_info(self, content: str) -> bool:
        """Check if content contains financial information"""
        financial_keywords = [
            'investment', 'cost', 'fee', 'price', 'revenue', 'profit', 'roi',
            'franchise fee', 'royalty', 'capital', 'financing', 'loan', 'budget',
            '$', 'dollar', 'percent', '%', 'financial', 'money'
        ]
        
        content_lower = content.lower()
        return any(keyword in content_lower for keyword in financial_keywords)
    
    def _contains_contact_info(self, content: str) -> bool:
        """Check if content contains contact information"""
        import re
        
        # Check for phone numbers, emails, addresses
        phone_pattern = r'\b(?:\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})\b'
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        
        return (bool(re.search(phone_pattern, content)) or 
                bool(re.search(email_pattern, content)) or
                any(word in content.lower() for word in ['contact', 'phone', 'email', 'address', 'website']))
    
    def _contains_requirements(self, content: str) -> bool:
        """Check if content contains requirement information"""
        requirement_keywords = [
            'requirement', 'qualification', 'criteria', 'must', 'should',
            'minimum', 'maximum', 'need', 'necessary', 'essential',
            'prerequisite', 'condition', 'standard'
        ]
        
        content_lower = content.lower()
        return any(keyword in content_lower for keyword in requirement_keywords)
    
    def _contains_support_info(self, content: str) -> bool:
        """Check if content contains support information"""
        support_keywords = [
            'support', 'training', 'assistance', 'help', 'guidance',
            'mentoring', 'coaching', 'education', 'program', 'service',
            'ongoing', 'continuous', 'provide', 'offer'
        ]
        
        content_lower = content.lower()
        return any(keyword in content_lower for keyword in support_keywords)
    
    def _extract_key_entities(self, content: str) -> List[str]:
        """Extract key entities from content"""
        entities = []
        
        if self.processor.nlp:
            try:
                doc = self.processor.nlp(content)
                entities = [ent.text for ent in doc.ents if ent.label_ in ['ORG', 'PERSON', 'GPE', 'MONEY', 'PERCENT']]
            except Exception as e:
                logger.debug(f"Entity extraction failed: {e}")
        
        return entities[:10]  # Limit to top 10 entities
    
    def _calculate_content_quality(self, content: str) -> float:
        """Calculate content quality score"""
        if not content.strip():
            return 0.0
        
        score = 0.5  # Base score
        
        # Length factor
        if len(content) > 100:
            score += 0.1
        if len(content) > 500:
            score += 0.1
        
        # Completeness factor
        if content.count('.') > 2:  # Multiple sentences
            score += 0.1
        
        # Information density
        if any(keyword in content.lower() for keyword in ['franchise', 'investment', 'support', 'training']):
            score += 0.2
        
        return min(score, 1.0)
    
    def _find_cross_references(self, chunk: Dict[str, Any], processed_doc: ProcessedDocument) -> List[str]:
        """Find cross-references to other parts of the document"""
        cross_refs = []
        
        # Look for references to tables, figures, sections
        content = chunk['content'].lower()
        
        if 'table' in content or 'figure' in content or 'chart' in content:
            cross_refs.append('contains_visual_reference')
        
        if 'see' in content or 'refer' in content or 'above' in content or 'below' in content:
            cross_refs.append('contains_textual_reference')
        
        return cross_refs
    
    def _create_structured_element_chunks(self, processed_doc: ProcessedDocument, document_id: str) -> List[Dict[str, Any]]:
        """Create chunks for structured elements (tables, charts, images)"""
        structured_chunks = []
        chunk_index = len(processed_doc.semantic_chunks)
        
        # Process tables
        for i, table in enumerate(processed_doc.tables):
            chunk = {
                'id': f"{document_id}_table_{i}",
                'document_id': document_id,
                'content': f"TABLE: {table.get('description', '')} {table.get('content', '')}",
                'chunk_index': chunk_index,
                'page_numbers': [table.get('page', 0)],
                'chunk_type': 'table',
                'structured_data': table,
                'enhanced_metadata': {
                    'element_type': 'table',
                    'confidence': table.get('confidence', 0.8),
                    'data_rows': table.get('rows', 0),
                    'data_columns': table.get('columns', 0)
                }
            }
            structured_chunks.append(chunk)
            chunk_index += 1
        
        # Process charts
        for i, chart in enumerate(processed_doc.charts):
            chunk = {
                'id': f"{document_id}_chart_{i}",
                'document_id': document_id,
                'content': f"CHART: {chart.get('description', '')} {chart.get('analysis', '')}",
                'chunk_index': chunk_index,
                'page_numbers': [chart.get('page', 0)],
                'chunk_type': 'chart',
                'structured_data': chart,
                'enhanced_metadata': {
                    'element_type': 'chart',
                    'chart_type': chart.get('type', 'unknown'),
                    'confidence': chart.get('confidence', 0.7)
                }
            }
            structured_chunks.append(chunk)
            chunk_index += 1
        
        # Process images with analysis
        for i, image in enumerate(processed_doc.images):
            if image.get('analysis') and len(image['analysis']) > 20:  # Only meaningful analyses
                chunk = {
                    'id': f"{document_id}_image_{i}",
                    'document_id': document_id,
                    'content': f"IMAGE: {image.get('analysis', '')}",
                    'chunk_index': chunk_index,
                    'page_numbers': [image.get('page', 0)],
                    'chunk_type': 'image',
                    'structured_data': image,
                    'enhanced_metadata': {
                        'element_type': 'image',
                        'image_type': image.get('type', 'unknown'),
                        'confidence': 0.6
                    }
                }
                structured_chunks.append(chunk)
                chunk_index += 1
        
        return structured_chunks
