"""
Advanced Document Processor with Multi-Modal Intelligence

This system uses the most powerful tools available for document processing:
- Tesseract OCR with advanced preprocessing
- OpenAI GPT-4 Vision for image/chart analysis
- Multiple PDF processing libraries for maximum text extraction
- Semantic chunking for better context preservation
- Multi-modal content understanding
- Advanced preprocessing and enhancement
"""

import fitz  # PyMuPDF
import pytesseract
from PIL import Image, ImageEnhance, ImageFilter
import cv2
import numpy as np
import pandas as pd
import camelot
import tabula
import pdfplumber
import pymupdf4llm
from typing import List, Dict, Any, Optional, Tuple, Union
import io
import base64
import re
from pathlib import Path
import logging
from dataclasses import dataclass, field
import openai
from concurrent.futures import ThreadPoolExecutor, as_completed
import time
import json
import asyncio
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document
import spacy
from transformers import pipeline
import easyocr

logger = logging.getLogger(__name__)

@dataclass
class ProcessedContent:
    """Container for all processed content from document"""
    text_content: str
    structured_content: Dict[str, Any]
    images: List[Dict[str, Any]]
    tables: List[Dict[str, Any]]
    charts: List[Dict[str, Any]]
    metadata: Dict[str, Any]
    chunks: List[Dict[str, Any]]
    embeddings_ready: bool = False

@dataclass
class ProcessingConfig:
    """Advanced configuration for document processing"""
    # OCR Configuration
    use_tesseract: bool = True
    use_easyocr: bool = True
    use_paddleocr: bool = False
    ocr_languages: List[str] = field(default_factory=lambda: ['en'])
    ocr_confidence_threshold: float = 0.7
    
    # Text Extraction
    use_multiple_extractors: bool = True
    use_pymupdf4llm: bool = True
    use_pdfplumber: bool = True
    preserve_formatting: bool = True
    
    # AI Analysis
    use_gpt4_vision: bool = True
    use_gpt4_text_analysis: bool = True
    analyze_charts: bool = True
    analyze_tables: bool = True
    extract_key_information: bool = True
    
    # Chunking Strategy
    use_semantic_chunking: bool = True
    chunk_size: int = 1000
    chunk_overlap: int = 200
    preserve_context: bool = True
    
    # Performance
    parallel_processing: bool = True
    max_workers: int = 4
    timeout_seconds: int = 300

class AdvancedDocumentProcessor:
    """Advanced document processor with multi-modal intelligence"""
    
    def __init__(self, openai_api_key: str, config: ProcessingConfig = None):
        self.openai_client = openai.OpenAI(api_key=openai_api_key)
        self.config = config or ProcessingConfig()
        
        # Initialize OCR engines
        self.tesseract_available = self._check_tesseract()
        self.easyocr_reader = None
        if self.config.use_easyocr:
            try:
                self.easyocr_reader = easyocr.Reader(self.config.ocr_languages)
            except Exception as e:
                logger.warning(f"EasyOCR initialization failed: {e}")
        
        # Initialize NLP tools
        self.nlp = None
        try:
            self.nlp = spacy.load("en_core_web_sm")
        except OSError:
            logger.warning("spaCy model not found. Install with: python -m spacy download en_core_web_sm")
        
        # Initialize text splitter for semantic chunking
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=self.config.chunk_size,
            chunk_overlap=self.config.chunk_overlap,
            separators=["\n\n", "\n", ". ", " ", ""],
            keep_separator=True
        )
        
        # Text enhancement patterns
        self.enhancement_patterns = {
            'currency': re.compile(r'\$[\d,]+(?:\.\d{2})?|\b\d+\s*(?:dollars?|USD|AUD|CAD|EUR)\b', re.IGNORECASE),
            'percentages': re.compile(r'\b\d+(?:\.\d+)?%\b'),
            'phone_numbers': re.compile(r'\b(?:\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})\b'),
            'emails': re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'),
            'urls': re.compile(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+'),
            'dates': re.compile(r'\b(?:\d{1,2}[/-]\d{1,2}[/-]\d{2,4}|\d{4}[/-]\d{1,2}[/-]\d{1,2}|\w+ \d{1,2},? \d{4})\b'),
            'numbers': re.compile(r'\b\d{1,3}(?:,\d{3})*(?:\.\d+)?\b'),
            'headings': re.compile(r'^[A-Z][A-Z\s]{2,}$', re.MULTILINE),
            'bullet_points': re.compile(r'^[\s]*[•·▪▫◦‣⁃]\s*', re.MULTILINE),
            'numbered_lists': re.compile(r'^[\s]*\d+[\.\)]\s*', re.MULTILINE)
        }
    
    def _check_tesseract(self) -> bool:
        """Check if Tesseract is available"""
        try:
            pytesseract.get_tesseract_version()
            return True
        except Exception:
            logger.warning("Tesseract not found. OCR capabilities will be limited.")
            return False
    
    async def process_document(self, file_path: Path) -> ProcessedContent:
        """
        Process document with advanced multi-modal intelligence
        
        Args:
            file_path: Path to the document file
            
        Returns:
            ProcessedContent with comprehensive extracted information
        """
        logger.info(f"Starting advanced document processing: {file_path}")
        start_time = time.time()
        
        try:
            # Step 1: Multi-method text extraction
            text_content = await self._extract_text_multi_method(file_path)
            
            # Step 2: Extract structured content (images, tables, charts)
            structured_content = await self._extract_structured_content(file_path)
            
            # Step 3: Enhance and clean text
            enhanced_text = self._enhance_text_content(text_content)
            
            # Step 4: AI-powered content analysis and key information extraction
            ai_analysis = await self._analyze_content_with_ai(enhanced_text, structured_content)
            
            # Step 5: Create semantic chunks
            chunks = self._create_semantic_chunks(enhanced_text, structured_content, ai_analysis)
            
            # Step 6: Extract comprehensive metadata
            metadata = self._extract_comprehensive_metadata(file_path, text_content, structured_content)
            
            processing_time = time.time() - start_time
            logger.info(f"Advanced processing completed in {processing_time:.2f}s")
            logger.info(f"Extracted: {len(enhanced_text)} chars, {len(chunks)} chunks, "
                       f"{len(structured_content.get('images', []))} images, "
                       f"{len(structured_content.get('tables', []))} tables")
            
            return ProcessedContent(
                text_content=enhanced_text,
                structured_content=structured_content,
                images=structured_content.get('images', []),
                tables=structured_content.get('tables', []),
                charts=structured_content.get('charts', []),
                metadata=metadata,
                chunks=chunks,
                embeddings_ready=True
            )
            
        except Exception as e:
            logger.error(f"Advanced document processing failed: {str(e)}")
            raise
    
    async def _extract_text_multi_method(self, file_path: Path) -> str:
        """Extract text using multiple methods for maximum coverage"""
        extraction_methods = []
        
        # Method 1: PyMuPDF (fast and reliable)
        extraction_methods.append(('pymupdf', self._extract_with_pymupdf))
        
        # Method 2: PyMuPDF4LLM (optimized for LLM processing)
        if self.config.use_pymupdf4llm:
            extraction_methods.append(('pymupdf4llm', self._extract_with_pymupdf4llm))
        
        # Method 3: pdfplumber (excellent for tables and layout)
        if self.config.use_pdfplumber:
            extraction_methods.append(('pdfplumber', self._extract_with_pdfplumber))
        
        # Method 4: OCR methods
        if self.tesseract_available or self.easyocr_reader:
            extraction_methods.append(('ocr', self._extract_with_ocr))
        
        # Run extraction methods
        results = {}
        
        if self.config.parallel_processing:
            # Parallel execution
            with ThreadPoolExecutor(max_workers=len(extraction_methods)) as executor:
                future_to_method = {
                    executor.submit(method_func, file_path): method_name
                    for method_name, method_func in extraction_methods
                }
                
                for future in as_completed(future_to_method, timeout=self.config.timeout_seconds):
                    method_name = future_to_method[future]
                    try:
                        results[method_name] = future.result()
                    except Exception as e:
                        logger.warning(f"Text extraction method {method_name} failed: {e}")
                        results[method_name] = ""
        else:
            # Sequential execution
            for method_name, method_func in extraction_methods:
                try:
                    results[method_name] = method_func(file_path)
                except Exception as e:
                    logger.warning(f"Text extraction method {method_name} failed: {e}")
                    results[method_name] = ""
        
        # Combine results intelligently
        return self._combine_extraction_results(results)
    
    def _extract_with_pymupdf(self, file_path: Path) -> str:
        """Extract text using PyMuPDF"""
        doc = fitz.open(str(file_path))
        text_parts = []
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            
            if self.config.preserve_formatting:
                # Use dict mode for better structure preservation
                blocks = page.get_text("dict")
                page_text = self._process_pymupdf_blocks(blocks)
            else:
                page_text = page.get_text()
            
            if page_text.strip():
                text_parts.append(f"\n--- Page {page_num + 1} ---\n{page_text}")
        
        doc.close()
        return "\n\n".join(text_parts)
    
    def _extract_with_pymupdf4llm(self, file_path: Path) -> str:
        """Extract text using PyMuPDF4LLM (optimized for LLM)"""
        try:
            # This library is optimized for LLM processing
            text = pymupdf4llm.to_markdown(str(file_path))
            return text
        except Exception as e:
            logger.warning(f"PyMuPDF4LLM extraction failed: {e}")
            return ""
    
    def _extract_with_pdfplumber(self, file_path: Path) -> str:
        """Extract text using pdfplumber (excellent for tables)"""
        try:
            import pdfplumber
            text_parts = []
            
            with pdfplumber.open(file_path) as pdf:
                for page_num, page in enumerate(pdf.pages):
                    # Extract text with layout preservation
                    page_text = page.extract_text(layout=True) if self.config.preserve_formatting else page.extract_text()
                    
                    if page_text:
                        text_parts.append(f"\n--- Page {page_num + 1} ---\n{page_text}")
            
            return "\n\n".join(text_parts)
        except Exception as e:
            logger.warning(f"pdfplumber extraction failed: {e}")
            return ""
    
    def _extract_with_ocr(self, file_path: Path) -> str:
        """Extract text using OCR methods"""
        try:
            doc = fitz.open(str(file_path))
            ocr_results = []
            
            for page_num in range(len(doc)):
                page = doc[page_num]
                
                # Convert page to high-quality image
                mat = fitz.Matrix(3, 3)  # 3x zoom for better OCR
                pix = page.get_pixmap(matrix=mat)
                img_data = pix.tobytes("png")
                
                # Process with multiple OCR engines
                page_text = self._ocr_image_multi_engine(img_data)
                
                if page_text.strip():
                    ocr_results.append(f"\n--- Page {page_num + 1} (OCR) ---\n{page_text}")
            
            doc.close()
            return "\n\n".join(ocr_results)
            
        except Exception as e:
            logger.warning(f"OCR extraction failed: {e}")
            return ""
