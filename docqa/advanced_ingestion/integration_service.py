"""
Integration Service for Enhanced Document Processing

This service integrates the advanced document processing capabilities
into the existing GrowthHive system, providing a seamless upgrade
to more powerful document understanding.
"""

import asyncio
import logging
from pathlib import Path
from typing import Dict, Any, Optional
import os
from dataclasses import dataclass

# Temporarily comment out problematic imports for webhook testing
# from .enhanced_docqa_service import EnhancedDocQAService, AdvancedConfig
# from ..services.document_service import DocumentService
# from ..core.database import get_database_session

logger = logging.getLogger(__name__)

@dataclass
class IntegrationConfig:
    """Configuration for the integration service"""
    # OpenAI Configuration
    openai_api_key: str
    
    # Database Configuration
    database_url: str
    
    # Processing Configuration
    use_layoutparser: bool = True
    use_multiple_ocr: bool = True
    use_ai_analysis: bool = True
    parallel_processing: bool = True
    
    # Performance Configuration
    max_workers: int = 4
    timeout_seconds: int = 600
    gpu_acceleration: bool = False  # Set to True if GPU available

class EnhancedDocumentIntegrationService:
    """Service to integrate enhanced document processing into existing system"""
    
    def __init__(self, config: IntegrationConfig):
        self.config = config
        
        # Create advanced processing configuration
        advanced_config = AdvancedConfig(
            use_layoutparser=config.use_layoutparser,
            use_tesseract=True,
            use_easyocr=config.use_multiple_ocr,
            use_paddleocr=False,  # Can be enabled if needed
            use_gpt4_vision=config.use_ai_analysis,
            use_gpt4_text_enhancement=config.use_ai_analysis,
            parallel_processing=config.parallel_processing,
            max_workers=config.max_workers,
            gpu_acceleration=config.gpu_acceleration,
            timeout_seconds=config.timeout_seconds
        )
        
        # Initialize enhanced DocQA service
        self.enhanced_service = EnhancedDocQAService(
            openai_api_key=config.openai_api_key,
            database_url=config.database_url,
            config=advanced_config
        )
        
        logger.info("Enhanced Document Integration Service initialized")
    
    async def process_document_enhanced(self, 
                                      file_path: str,
                                      document_id: Optional[str] = None,
                                      table_name: str = "documents") -> Dict[str, Any]:
        """
        Process document with enhanced capabilities
        
        Args:
            file_path: Path to the document file
            document_id: Optional document ID
            table_name: Target table for storage
            
        Returns:
            Processing result with enhanced information
        """
        try:
            file_path_obj = Path(file_path)
            
            if not file_path_obj.exists():
                raise FileNotFoundError(f"Document file not found: {file_path}")
            
            logger.info(f"Starting enhanced processing for: {file_path}")
            
            # Process with enhanced service
            result = await self.enhanced_service.process_document_enhanced(
                file_path=file_path_obj,
                document_id=document_id,
                table_name=table_name
            )
            
            # Convert to standard response format
            response = {
                'success': result.success,
                'document_id': result.document_id,
                'chunks_created': result.chunks_created,
                'processing_time': result.processing_time,
                'enhanced_features': {
                    'layout_elements_detected': result.layout_elements_detected,
                    'images_processed': result.images_processed,
                    'tables_extracted': result.tables_extracted,
                    'charts_analyzed': result.charts_analyzed,
                    'advanced_processing_used': True
                },
                'metadata': result.metadata,
                'error_message': result.error_message
            }
            
            if result.success:
                logger.info(f"Enhanced processing completed successfully")
                logger.info(f"Created {result.chunks_created} chunks with {result.layout_elements_detected} layout elements")
            else:
                logger.error(f"Enhanced processing failed: {result.error_message}")
            
            return response
            
        except Exception as e:
            error_msg = f"Enhanced document processing failed: {str(e)}"
            logger.error(error_msg)
            
            return {
                'success': False,
                'document_id': document_id,
                'chunks_created': 0,
                'processing_time': 0,
                'enhanced_features': {
                    'layout_elements_detected': 0,
                    'images_processed': 0,
                    'tables_extracted': 0,
                    'charts_analyzed': 0,
                    'advanced_processing_used': False
                },
                'error_message': error_msg
            }
    
    async def process_franchisor_document(self, 
                                        franchisor_id: str,
                                        document_url: str) -> Dict[str, Any]:
        """
        Process franchisor document with enhanced capabilities
        
        Args:
            franchisor_id: ID of the franchisor
            document_url: URL or path to the document
            
        Returns:
            Processing result
        """
        try:
            # Download document if it's a URL
            if document_url.startswith(('http://', 'https://', 's3://')):
                # Handle URL download (implement based on your needs)
                local_path = await self._download_document(document_url)
            else:
                local_path = document_url
            
            # Process with enhanced capabilities
            result = await self.process_document_enhanced(
                file_path=local_path,
                document_id=None,  # Will be generated
                table_name="franchisors"  # Store in franchisors table
            )
            
            # Add franchisor-specific metadata
            if result['success']:
                result['franchisor_id'] = franchisor_id
                result['source_url'] = document_url
            
            return result
            
        except Exception as e:
            error_msg = f"Franchisor document processing failed: {str(e)}"
            logger.error(error_msg)
            
            return {
                'success': False,
                'franchisor_id': franchisor_id,
                'source_url': document_url,
                'error_message': error_msg
            }
    
    async def _download_document(self, url: str) -> str:
        """Download document from URL to local temporary file"""
        import tempfile
        import aiohttp
        import aiofiles
        
        try:
            # Create temporary file
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
            temp_path = temp_file.name
            temp_file.close()
            
            # Download file
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        async with aiofiles.open(temp_path, 'wb') as f:
                            async for chunk in response.content.iter_chunked(8192):
                                await f.write(chunk)
                    else:
                        raise Exception(f"Failed to download document: HTTP {response.status}")
            
            return temp_path
            
        except Exception as e:
            logger.error(f"Document download failed: {e}")
            raise
    
    def get_processing_capabilities(self) -> Dict[str, Any]:
        """Get information about processing capabilities"""
        return {
            'advanced_processing_available': True,
            'features': {
                'layout_analysis': self.config.use_layoutparser,
                'multiple_ocr_engines': self.config.use_multiple_ocr,
                'ai_powered_analysis': self.config.use_ai_analysis,
                'parallel_processing': self.config.parallel_processing,
                'gpu_acceleration': self.config.gpu_acceleration
            },
            'supported_formats': ['PDF'],
            'ocr_languages': ['en'],  # Can be expanded
            'max_file_size': '100MB',  # Configurable
            'processing_timeout': f"{self.config.timeout_seconds}s"
        }

# Factory function to create the integration service
def create_enhanced_integration_service() -> EnhancedDocumentIntegrationService:
    """Create enhanced integration service with environment configuration"""
    
    # Get configuration from environment
    config = IntegrationConfig(
        openai_api_key=os.getenv('OPENAI_API_KEY', ''),
        database_url=os.getenv('DATABASE_URL', ''),
        use_layoutparser=os.getenv('USE_LAYOUTPARSER', 'true').lower() == 'true',
        use_multiple_ocr=os.getenv('USE_MULTIPLE_OCR', 'true').lower() == 'true',
        use_ai_analysis=os.getenv('USE_AI_ANALYSIS', 'true').lower() == 'true',
        parallel_processing=os.getenv('PARALLEL_PROCESSING', 'true').lower() == 'true',
        max_workers=int(os.getenv('MAX_WORKERS', '4')),
        timeout_seconds=int(os.getenv('PROCESSING_TIMEOUT', '600')),
        gpu_acceleration=os.getenv('GPU_ACCELERATION', 'false').lower() == 'true'
    )
    
    if not config.openai_api_key:
        raise ValueError("OPENAI_API_KEY environment variable is required")
    
    if not config.database_url:
        raise ValueError("DATABASE_URL environment variable is required")
    
    return EnhancedDocumentIntegrationService(config)

# Global instance (lazy initialization)
_integration_service = None

async def get_enhanced_integration_service() -> EnhancedDocumentIntegrationService:
    """Get or create the enhanced integration service instance"""
    global _integration_service
    
    if _integration_service is None:
        _integration_service = create_enhanced_integration_service()
    
    return _integration_service

# Convenience functions for easy integration - STUB for webhook testing
async def process_document_with_enhanced_capabilities(file_path: str,
                                                    document_id: Optional[str] = None) -> Dict[str, Any]:
    """
    Convenience function to process document with enhanced capabilities - STUB

    Args:
        file_path: Path to the document file
        document_id: Optional document ID

    Returns:
        Processing result
    """
    # STUB for webhook testing
    return {
        "success": True,
        "document_id": document_id or "stub_doc_id",
        "chunks_created": 0,
        "message": "Document processing stubbed for webhook testing"
    }

async def process_franchisor_document_enhanced(franchisor_id: str, 
                                             document_url: str) -> Dict[str, Any]:
    """
    Convenience function to process franchisor document with enhanced capabilities
    
    Args:
        franchisor_id: ID of the franchisor
        document_url: URL or path to the document
        
    Returns:
        Processing result
    """
    service = await get_enhanced_integration_service()
    return await service.process_franchisor_document(franchisor_id, document_url)

# Example usage and testing
async def test_enhanced_processing():
    """Test function for enhanced processing"""
    try:
        # Test with a sample document
        test_file = "/path/to/test/document.pdf"
        
        if Path(test_file).exists():
            result = await process_document_with_enhanced_capabilities(test_file)
            
            print("Enhanced Processing Result:")
            print(f"Success: {result['success']}")
            print(f"Chunks Created: {result['chunks_created']}")
            print(f"Layout Elements: {result['enhanced_features']['layout_elements_detected']}")
            print(f"Images Processed: {result['enhanced_features']['images_processed']}")
            print(f"Tables Extracted: {result['enhanced_features']['tables_extracted']}")
            print(f"Charts Analyzed: {result['enhanced_features']['charts_analyzed']}")
            print(f"Processing Time: {result['processing_time']:.2f}s")
            
            if not result['success']:
                print(f"Error: {result['error_message']}")
        else:
            print(f"Test file not found: {test_file}")
            
    except Exception as e:
        print(f"Test failed: {e}")

if __name__ == "__main__":
    # Run test
    asyncio.run(test_enhanced_processing())
