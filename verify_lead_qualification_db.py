#!/usr/bin/env python3
"""
Verify Lead Qualification Database

Quick script to verify the database setup and show current data.
"""

import os
import sqlalchemy as sa
from sqlalchemy import create_engine

def get_database_url():
    """Get database URL from environment"""
    db_url = os.getenv('DATABASE_URL')
    if db_url and 'asyncpg' in db_url:
        db_url = db_url.replace('postgresql+asyncpg://', 'postgresql://')
    return db_url or "postgresql://postgres:root@localhost:5432/growthhive"

def verify_database():
    """Verify the database setup and show data"""
    
    print("🔍 VERIFYING LEAD QUALIFICATION DATABASE")
    print("=" * 60)
    
    db_url = get_database_url()
    engine = create_engine(db_url)
    
    with engine.connect() as conn:
        # Check tables exist
        print("📋 Database Tables:")
        result = conn.execute(sa.text("""
            SELECT table_name FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name LIKE '%qualification%' OR table_name = 'leads'
            ORDER BY table_name
        """))
        
        tables = [row[0] for row in result.fetchall()]
        for table in tables:
            print(f"   ✅ {table}")
        
        # Show questions
        print(f"\n📝 Qualification Questions:")
        result = conn.execute(sa.text("""
            SELECT question_id, category, score_weight, 
                   LEFT(question_text, 60) || '...' as question_preview
            FROM pre_qualification_questions 
            WHERE is_active = true
            ORDER BY question_id
        """))
        
        for row in result.fetchall():
            print(f"   {row[0]} ({row[1]}): {row[2]} pts - {row[3]}")
        
        # Show template
        print(f"\n📋 Question Templates:")
        result = conn.execute(sa.text("""
            SELECT template_name, franchise_type, total_possible_score, qualification_threshold
            FROM question_templates 
            WHERE is_active = true
        """))
        
        for row in result.fetchall():
            print(f"   {row[0]} ({row[1]}): {row[2]} total pts, {row[3]} threshold (80%)")
        
        # Show any existing leads (if any)
        result = conn.execute(sa.text("SELECT COUNT(*) FROM leads"))
        lead_count = result.fetchone()[0]
        print(f"\n👥 Leads in Database: {lead_count}")
        
        # Show any responses (if any)
        result = conn.execute(sa.text("SELECT COUNT(*) FROM lead_responses"))
        response_count = result.fetchone()[0]
        print(f"💬 Lead Responses: {response_count}")
        
        # Show any qualification summaries (if any)
        result = conn.execute(sa.text("SELECT COUNT(*) FROM lead_qualification_summaries"))
        summary_count = result.fetchone()[0]
        print(f"📊 Qualification Summaries: {summary_count}")
    
    print(f"\n✅ Database verification completed!")
    print(f"\n🚀 Ready for:")
    print(f"   • FastAPI server: uvicorn app.main:app --reload")
    print(f"   • API documentation: http://localhost:8000/docs")
    print(f"   • Lead qualification workflow testing")

if __name__ == "__main__":
    verify_database()
