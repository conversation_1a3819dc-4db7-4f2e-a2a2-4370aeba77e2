#!/usr/bin/env python3
"""
Test Answer Evaluation Logic

Demonstrates how the lead qualification system evaluates different types of answers
against expected responses with scoring and confidence levels.
"""

import json
import os
from typing import List, Dict, Any


class AnswerEvaluator:
    """Test the answer evaluation logic"""
    
    def evaluate_answer(self, answer: str, expected_answers: List[str], max_score: int) -> Dict[str, Any]:
        """Evaluate an answer against expected responses"""
        
        answer_clean = answer.strip().lower()
        
        # Check for exact matches
        for expected in expected_answers:
            if answer_clean == expected.strip().lower():
                return {
                    "is_qualified": True,
                    "score_awarded": max_score,
                    "confidence_score": 1.0,
                    "match_type": "exact",
                    "matched_answer": expected
                }
        
        # Check for partial matches (keyword overlap)
        for expected in expected_answers:
            expected_words = set(expected.lower().split())
            answer_words = set(answer_clean.split())
            
            # Remove common words
            common_words = {"the", "and", "or", "but", "yes", "no", "can", "will", "have", "has", "i", "am", "is", "a", "an"}
            expected_words -= common_words
            answer_words -= common_words
            
            if expected_words and answer_words:
                overlap = len(expected_words.intersection(answer_words))
                if overlap > 0:
                    confidence = overlap / len(expected_words)
                    if confidence >= 0.5:  # At least 50% word overlap
                        score = int(max_score * confidence)
                        return {
                            "is_qualified": True,
                            "score_awarded": score,
                            "confidence_score": confidence,
                            "match_type": "partial",
                            "matched_answer": expected,
                            "matched_keywords": list(expected_words.intersection(answer_words))
                        }
        
        # No match
        return {
            "is_qualified": False,
            "score_awarded": 0,
            "confidence_score": 0.0,
            "match_type": "no_match",
            "matched_answer": None
        }
    
    def test_question_scenarios(self):
        """Test various answer scenarios for each question"""
        
        # Load questions from our generated data
        questions = [
            {
                "id": "Q001",
                "category": "Financial",
                "question_text": "What is your available investment budget for this franchise opportunity?",
                "expected_answers": ["$50,000-$100,000", "$100,000-$200,000", "$200,000+"],
                "score_weight": 15
            },
            {
                "id": "Q002", 
                "category": "Financial",
                "question_text": "Are you comfortable with ongoing royalty payments of 10% of monthly revenue?",
                "expected_answers": ["Yes", "Absolutely", "I understand and accept"],
                "score_weight": 10
            },
            {
                "id": "Q005",
                "category": "Training",
                "question_text": "Are you available to complete a 4-week training program within the first 12 months?",
                "expected_answers": ["Yes", "Absolutely", "I can commit to that"],
                "score_weight": 12
            },
            {
                "id": "Q008",
                "category": "Commitment",
                "question_text": "Can you dedicate full-time hours to operating this franchise?",
                "expected_answers": ["Yes, full-time commitment", "Yes, this will be my primary focus"],
                "score_weight": 10
            }
        ]
        
        # Test scenarios for each question
        test_scenarios = {
            "Q001": [
                ("$150,000", "Should match $100,000-$200,000"),
                ("$200,000+", "Exact match"),
                ("I have $75,000", "Should match $50,000-$100,000 partially"),
                ("$25,000", "No match - too low"),
                ("Two hundred thousand dollars", "No match - different format")
            ],
            "Q002": [
                ("Yes", "Exact match"),
                ("Absolutely", "Exact match"),
                ("I understand", "Partial match"),
                ("I accept the royalty", "Partial match"),
                ("No", "No match"),
                ("Maybe", "No match")
            ],
            "Q005": [
                ("Yes", "Exact match"),
                ("I can commit", "Partial match"),
                ("I can do the training", "Partial match"),
                ("No time", "No match"),
                ("Maybe later", "No match")
            ],
            "Q008": [
                ("Yes, full-time commitment", "Exact match"),
                ("Full-time", "Partial match"),
                ("This will be my primary focus", "Partial match"),
                ("Part-time only", "No match"),
                ("Just weekends", "No match")
            ]
        }
        
        print("🧪 TESTING ANSWER EVALUATION LOGIC")
        print("=" * 70)
        
        total_tests = 0
        for question in questions:
            if question["id"] not in test_scenarios:
                continue
                
            print(f"\n📝 {question['id']} - {question['category']} ({question['score_weight']} points)")
            print(f"❓ {question['question_text']}")
            print(f"💡 Expected: {', '.join(question['expected_answers'])}")
            print("-" * 50)
            
            for answer, description in test_scenarios[question["id"]]:
                evaluation = self.evaluate_answer(answer, question["expected_answers"], question["score_weight"])
                
                status_icon = "✅" if evaluation["is_qualified"] else "❌"
                print(f"{status_icon} '{answer}' → {evaluation['score_awarded']}/{question['score_weight']} pts")
                print(f"   Match: {evaluation['match_type']} (confidence: {evaluation['confidence_score']:.2f})")
                if evaluation.get('matched_keywords'):
                    print(f"   Keywords: {evaluation['matched_keywords']}")
                print(f"   Expected: {description}")
                print()
                
                total_tests += 1
        
        print(f"📊 Completed {total_tests} answer evaluation tests")
        
        return True
    
    def test_qualification_scenarios(self):
        """Test complete qualification scenarios"""
        
        print(f"\n🎯 TESTING COMPLETE QUALIFICATION SCENARIOS")
        print("=" * 70)
        
        scenarios = [
            {
                "name": "Perfect Candidate",
                "answers": [
                    "$200,000+",  # Q001 - 15 pts
                    "Absolutely",  # Q002 - 10 pts  
                    "Yes, I have identified an area",  # Q003 - 8 pts
                    "Definitely",  # Q004 - 7 pts
                    "I can commit to that",  # Q005 - 12 pts
                    "Yes, extensive experience",  # Q006 - 6 pts
                    "Yes, I own/owned a business",  # Q007 - 8 pts
                    "Yes, full-time commitment",  # Q008 - 10 pts
                    "Comprehensive ongoing support",  # Q009 - 6 pts
                    "Build a sustainable business"  # Q010 - 8 pts
                ]
            },
            {
                "name": "Good Candidate with Partial Matches",
                "answers": [
                    "I have $150,000 available",  # Q001 - partial
                    "I understand the royalty",  # Q002 - partial
                    "I have some areas in mind",  # Q003 - exact
                    "Yes",  # Q004 - exact
                    "I can do the training",  # Q005 - partial
                    "Some experience",  # Q006 - exact
                    "Management experience",  # Q007 - partial
                    "This will be my primary focus",  # Q008 - partial
                    "Training and marketing support",  # Q009 - exact
                    "Achieve financial independence"  # Q010 - exact
                ]
            },
            {
                "name": "Poor Candidate",
                "answers": [
                    "$20,000",  # Q001 - no match
                    "Not sure",  # Q002 - no match
                    "Anywhere",  # Q003 - no match
                    "Maybe",  # Q004 - no match
                    "No time",  # Q005 - no match
                    "Never done it",  # Q006 - no match
                    "No experience",  # Q007 - no match
                    "Just weekends",  # Q008 - no match
                    "Minimal support",  # Q009 - no match
                    "Make some money"  # Q010 - no match
                ]
            }
        ]
        
        # All questions with their details
        all_questions = [
            {"id": "Q001", "category": "Financial", "expected_answers": ["$50,000-$100,000", "$100,000-$200,000", "$200,000+"], "score_weight": 15},
            {"id": "Q002", "category": "Financial", "expected_answers": ["Yes", "Absolutely", "I understand and accept"], "score_weight": 10},
            {"id": "Q003", "category": "Location", "expected_answers": ["Yes, I have identified an area", "I have some areas in mind", "I need help selecting"], "score_weight": 8},
            {"id": "Q004", "category": "Location", "expected_answers": ["Yes", "Definitely", "That's my preference"], "score_weight": 7},
            {"id": "Q005", "category": "Training", "expected_answers": ["Yes", "Absolutely", "I can commit to that"], "score_weight": 12},
            {"id": "Q006", "category": "Training", "expected_answers": ["Yes, extensive experience", "Some experience", "No, but willing to learn"], "score_weight": 6},
            {"id": "Q007", "category": "Experience", "expected_answers": ["Yes, I own/owned a business", "Yes, management experience", "No, but eager to learn"], "score_weight": 8},
            {"id": "Q008", "category": "Commitment", "expected_answers": ["Yes, full-time commitment", "Yes, this will be my primary focus"], "score_weight": 10},
            {"id": "Q009", "category": "Support", "expected_answers": ["Regular check-ins and guidance", "Training and marketing support", "Comprehensive ongoing support"], "score_weight": 6},
            {"id": "Q010", "category": "Goals", "expected_answers": ["Build a sustainable business", "Achieve financial independence", "Expand to multiple territories"], "score_weight": 8}
        ]
        
        max_possible_score = sum(q["score_weight"] for q in all_questions)
        qualification_threshold = int(max_possible_score * 0.8)  # 80%
        
        for scenario in scenarios:
            print(f"\n👤 {scenario['name']}")
            print("-" * 40)
            
            total_score = 0
            qualified_responses = 0
            
            for i, (question, answer) in enumerate(zip(all_questions, scenario["answers"])):
                evaluation = self.evaluate_answer(answer, question["expected_answers"], question["score_weight"])
                
                status_icon = "✅" if evaluation["is_qualified"] else "❌"
                print(f"{status_icon} {question['id']} ({question['category']}): {evaluation['score_awarded']}/{question['score_weight']} pts")
                print(f"   Q: {question['expected_answers'][0]}...")
                print(f"   A: {answer}")
                print(f"   Match: {evaluation['match_type']}")
                
                total_score += evaluation["score_awarded"]
                if evaluation["is_qualified"]:
                    qualified_responses += 1
            
            percentage = (total_score / max_possible_score) * 100
            is_qualified = total_score >= qualification_threshold
            
            print(f"\n📊 RESULTS:")
            print(f"   Total Score: {total_score}/{max_possible_score}")
            print(f"   Percentage: {percentage:.1f}%")
            print(f"   Threshold: {qualification_threshold} (80%)")
            print(f"   Qualified Responses: {qualified_responses}/{len(all_questions)}")
            print(f"   Status: {'✅ QUALIFIED' if is_qualified else '❌ NOT QUALIFIED'}")
        
        return True


def main():
    """Main test function"""
    
    print("🚀 LEAD QUALIFICATION ANSWER EVALUATION TESTING")
    print("=" * 80)
    
    evaluator = AnswerEvaluator()
    
    # Test individual answer evaluation
    evaluator.test_question_scenarios()
    
    # Test complete qualification scenarios
    evaluator.test_qualification_scenarios()
    
    print(f"\n🎉 ALL TESTS COMPLETED!")
    print("\n✅ Key Features Demonstrated:")
    print("   • Exact answer matching with full scores")
    print("   • Partial answer matching with proportional scores")
    print("   • Keyword-based evaluation with confidence scoring")
    print("   • Complete qualification workflow with 80% threshold")
    print("   • Multiple candidate scenarios with different outcomes")
    
    print(f"\n🔍 Answer Evaluation Logic:")
    print("   • Exact Match: 100% score (full points)")
    print("   • Partial Match: 50-100% score based on keyword overlap")
    print("   • No Match: 0% score")
    print("   • Qualification Threshold: 80% of total possible score")


if __name__ == "__main__":
    main()
