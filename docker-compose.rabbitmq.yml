version: '3.8'

services:
  # RabbitMQ Message Broker
  rabbitmq:
    image: rabbitmq:3.12-management
    container_name: growthhive-rabbitmq
    hostname: rabbitmq
    ports:
      - "5672:5672"    # AMQP port
      - "15672:15672"  # Management UI port
    environment:
      RABBITMQ_DEFAULT_USER: growthhive
      RABBITMQ_DEFAULT_PASS: growthhive123
      RABBITMQ_DEFAULT_VHOST: /
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - growthhive-network
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # Redis for Celery Results Backend
  redis:
    image: redis:7-alpine
    container_name: growthhive-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - growthhive-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Celery Worker - High Priority Queue
  celery-worker-high:
    build: .
    container_name: growthhive-celery-worker-high
    command: python celery_worker.py --queues=high_priority,document_processing --concurrency=2
    environment:
      - DATABASE_URL=postgresql+asyncpg://root:root@postgres:5432/growthhive
      - CELERY_BROKER_URL=amqp://growthhive:growthhive123@rabbitmq:5672//
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - WORKER_TYPE=high_priority
    depends_on:
      rabbitmq:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - .:/app
    networks:
      - growthhive-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

  # Celery Worker - Medium Priority Queue
  celery-worker-medium:
    build: .
    container_name: growthhive-celery-worker-medium
    command: python celery_worker.py --queues=medium_priority,document_processing --concurrency=3
    environment:
      - DATABASE_URL=postgresql+asyncpg://root:root@postgres:5432/growthhive
      - CELERY_BROKER_URL=amqp://growthhive:growthhive123@rabbitmq:5672//
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - WORKER_TYPE=medium_priority
    depends_on:
      rabbitmq:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - .:/app
    networks:
      - growthhive-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1.5G
        reservations:
          memory: 512M

  # Celery Worker - Batch Processing
  celery-worker-batch:
    build: .
    container_name: growthhive-celery-worker-batch
    command: python celery_worker.py --queues=batch_processing,low_priority --concurrency=4
    environment:
      - DATABASE_URL=postgresql+asyncpg://root:root@postgres:5432/growthhive
      - CELERY_BROKER_URL=amqp://growthhive:growthhive123@rabbitmq:5672//
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - WORKER_TYPE=batch_processing
    depends_on:
      rabbitmq:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - .:/app
    networks:
      - growthhive-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 3G
        reservations:
          memory: 1G

  # Celery Flower (Enhanced Monitoring)
  celery-flower:
    build: .
    container_name: growthhive-celery-flower
    command: celery -A app.core.celery_app flower --port=5555 --persistent=True --db=flower.db
    ports:
      - "5555:5555"
    environment:
      - CELERY_BROKER_URL=amqp://growthhive:growthhive123@rabbitmq:5672//
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - FLOWER_BASIC_AUTH=admin:growthhive123
    depends_on:
      rabbitmq:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - flower_data:/app/flower_data
    networks:
      - growthhive-network
    restart: unless-stopped

  # DocQA Status Monitor
  docqa-monitor:
    build: .
    container_name: growthhive-docqa-monitor
    command: python -m docqa.monitoring.status_tracker
    environment:
      - DATABASE_URL=postgresql+asyncpg://root:root@postgres:5432/growthhive
      - CELERY_BROKER_URL=amqp://growthhive:growthhive123@rabbitmq:5672//
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      rabbitmq:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - .:/app
    networks:
      - growthhive-network
    restart: unless-stopped

  # Redis Commander (Redis Management UI)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: growthhive-redis-commander
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379
      - HTTP_USER=admin
      - HTTP_PASSWORD=growthhive123
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - growthhive-network
    restart: unless-stopped

volumes:
  rabbitmq_data:
    driver: local
  redis_data:
    driver: local
  flower_data:
    driver: local

networks:
  growthhive-network:
    driver: bridge
