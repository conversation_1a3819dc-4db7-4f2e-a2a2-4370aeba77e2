#!/usr/bin/env python3
"""
Setup Lead Qualification Database

Simple script to create the lead qualification tables without FastAPI dependencies.
"""

import os
import sys
import json
from pathlib import Path
from datetime import datetime
import uuid

# Database setup
import sqlalchemy as sa
from sqlalchemy import create_engine, MetaData, Table, Column, String, Integer, Float, Boolean, DateTime, Text, ForeignKey, JSON, Index
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func

def get_database_url():
    """Get database URL from environment or use default"""

    # Try to get from environment
    db_url = os.getenv('DATABASE_URL')
    if db_url:
        # Convert asyncpg to psycopg2 for SQLAlchemy core
        if 'asyncpg' in db_url:
            db_url = db_url.replace('postgresql+asyncpg://', 'postgresql://')
        return db_url

    # Default local PostgreSQL
    return "postgresql://postgres:root@localhost:5432/growthhive"

def create_lead_qualification_tables(engine):
    """Create all lead qualification tables"""
    
    metadata = MetaData()
    
    print("🔧 Creating lead qualification tables...")
    
    # Leads table
    leads = Table('leads', metadata,
        Column('id', UUID(as_uuid=True), primary_key=True, default=uuid.uuid4),
        Column('email', String(255), nullable=False, index=True),
        Column('first_name', String(100), nullable=True),
        Column('last_name', String(100), nullable=True),
        Column('phone', String(20), nullable=True),
        Column('location', String(255), nullable=True),
        Column('source', String(100), nullable=True),
        Column('status', String(50), nullable=False, default='new'),
        Column('created_at', DateTime(timezone=True), server_default=func.now()),
        Column('updated_at', DateTime(timezone=True), onupdate=func.now())
    )
    
    # Pre-qualification questions table
    pre_qualification_questions = Table('pre_qualification_questions', metadata,
        Column('id', UUID(as_uuid=True), primary_key=True, default=uuid.uuid4),
        Column('question_id', String(10), unique=True, nullable=False, index=True),
        Column('category', String(50), nullable=False, index=True),
        Column('question_text', Text, nullable=False),
        Column('expected_answers', JSON, nullable=False),
        Column('score_weight', Integer, nullable=False, default=10),
        Column('context_info', JSON, nullable=True),
        Column('is_active', Boolean, default=True, nullable=False),
        Column('created_at', DateTime(timezone=True), server_default=func.now()),
        Column('updated_at', DateTime(timezone=True), onupdate=func.now())
    )
    
    # Lead responses table
    lead_responses = Table('lead_responses', metadata,
        Column('id', UUID(as_uuid=True), primary_key=True, default=uuid.uuid4),
        Column('lead_id', UUID(as_uuid=True), ForeignKey('leads.id'), nullable=False),
        Column('question_id', UUID(as_uuid=True), ForeignKey('pre_qualification_questions.id'), nullable=False),
        Column('question_text', Text, nullable=False),
        Column('lead_answer', Text, nullable=False),
        Column('is_qualified', Boolean, nullable=False),
        Column('score_awarded', Integer, nullable=False, default=0),
        Column('confidence_score', Float, nullable=True),
        Column('response_time_seconds', Integer, nullable=True),
        Column('created_at', DateTime(timezone=True), server_default=func.now())
    )
    
    # Lead qualification summaries table
    lead_qualification_summaries = Table('lead_qualification_summaries', metadata,
        Column('id', UUID(as_uuid=True), primary_key=True, default=uuid.uuid4),
        Column('lead_id', UUID(as_uuid=True), ForeignKey('leads.id'), nullable=False, unique=True),
        Column('total_score', Integer, nullable=False, default=0),
        Column('max_possible_score', Integer, nullable=False),
        Column('percentage_score', Float, nullable=False, default=0.0),
        Column('is_fully_qualified', Boolean, nullable=False, default=False),
        Column('qualification_level', String(20), nullable=False, default='unqualified'),
        Column('questions_answered', Integer, nullable=False, default=0),
        Column('total_questions', Integer, nullable=False),
        Column('average_response_time', Float, nullable=True),
        Column('qualification_notes', Text, nullable=True),
        Column('qualified_at', DateTime(timezone=True), nullable=True),
        Column('created_at', DateTime(timezone=True), server_default=func.now()),
        Column('updated_at', DateTime(timezone=True), onupdate=func.now())
    )
    
    # Qualification sessions table
    qualification_sessions = Table('qualification_sessions', metadata,
        Column('id', UUID(as_uuid=True), primary_key=True, default=uuid.uuid4),
        Column('lead_id', UUID(as_uuid=True), ForeignKey('leads.id'), nullable=False),
        Column('session_token', String(255), nullable=False, unique=True, index=True),
        Column('status', String(20), nullable=False, default='active'),
        Column('current_question_index', Integer, nullable=False, default=0),
        Column('questions_order', JSON, nullable=False),
        Column('started_at', DateTime(timezone=True), server_default=func.now()),
        Column('completed_at', DateTime(timezone=True), nullable=True),
        Column('expires_at', DateTime(timezone=True), nullable=False),
        Column('user_agent', String(500), nullable=True),
        Column('ip_address', String(45), nullable=True)
    )
    
    # Question templates table
    question_templates = Table('question_templates', metadata,
        Column('id', UUID(as_uuid=True), primary_key=True, default=uuid.uuid4),
        Column('template_name', String(100), nullable=False, unique=True),
        Column('franchise_type', String(100), nullable=False),
        Column('description', Text, nullable=True),
        Column('question_ids', JSON, nullable=False),
        Column('total_possible_score', Integer, nullable=False),
        Column('qualification_threshold', Integer, nullable=False),
        Column('is_active', Boolean, default=True, nullable=False),
        Column('created_at', DateTime(timezone=True), server_default=func.now()),
        Column('updated_at', DateTime(timezone=True), onupdate=func.now())
    )
    
    # Qualification analytics table
    qualification_analytics = Table('qualification_analytics', metadata,
        Column('id', UUID(as_uuid=True), primary_key=True, default=uuid.uuid4),
        Column('date', DateTime(timezone=True), nullable=False, index=True),
        Column('total_leads', Integer, default=0),
        Column('qualified_leads', Integer, default=0),
        Column('qualification_rate', Float, default=0.0),
        Column('average_score', Float, default=0.0),
        Column('average_completion_time', Float, default=0.0),
        Column('most_difficult_question_id', String(10), nullable=True),
        Column('easiest_question_id', String(10), nullable=True),
        Column('common_failure_categories', JSON, nullable=True),
        Column('insights', JSON, nullable=True),
        Column('created_at', DateTime(timezone=True), server_default=func.now())
    )
    
    # Create all tables
    metadata.create_all(engine)
    
    # Create additional indexes for better performance
    try:
        with engine.connect() as conn:
            # Index for lead responses
            conn.execute(sa.text("""
                CREATE INDEX IF NOT EXISTS idx_lead_responses_lead_question
                ON lead_responses (lead_id, question_id)
            """))

            # Index for leads status
            conn.execute(sa.text("""
                CREATE INDEX IF NOT EXISTS idx_leads_status
                ON leads (status)
            """))

            # Index for qualification summaries
            conn.execute(sa.text("""
                CREATE INDEX IF NOT EXISTS idx_qualification_summaries_qualified
                ON lead_qualification_summaries (is_fully_qualified)
            """))

            conn.commit()
    except Exception as e:
        print(f"   ⚠️  Warning: Could not create some indexes: {e}")
    
    print("✅ Lead qualification tables created successfully!")
    return True

def populate_sample_questions(engine):
    """Populate the database with sample questions from JSON"""
    
    print("📝 Populating sample questions...")
    
    # Load questions from JSON if available
    json_file = "lead_qualification_data.json"
    if os.path.exists(json_file):
        with open(json_file, 'r') as f:
            data = json.load(f)
        questions = data.get('questions', [])
    else:
        # Use the 10 questions we generated
        questions = [
            {
                "id": "Q001",
                "category": "Financial",
                "question_text": "What is your available investment budget for this franchise opportunity?",
                "expected_answers": ["$50,000-$100,000", "$100,000-$200,000", "$200,000+"],
                "score_weight": 15
            },
            {
                "id": "Q002", 
                "category": "Financial",
                "question_text": "Are you comfortable with ongoing royalty payments of 10% of monthly revenue?",
                "expected_answers": ["Yes", "Absolutely", "I understand and accept"],
                "score_weight": 10
            },
            {
                "id": "Q003",
                "category": "Location",
                "question_text": "Do you have a specific geographic area in mind for your franchise?",
                "expected_answers": ["Yes, I have identified an area", "I have some areas in mind", "I need help selecting"],
                "score_weight": 8
            },
            {
                "id": "Q004",
                "category": "Location", 
                "question_text": "Are you willing to operate in suburban residential areas?",
                "expected_answers": ["Yes", "Definitely", "That's my preference"],
                "score_weight": 7
            },
            {
                "id": "Q005",
                "category": "Training",
                "question_text": "Are you available to complete a 4-week training program within the first 12 months?",
                "expected_answers": ["Yes", "Absolutely", "I can commit to that"],
                "score_weight": 12
            },
            {
                "id": "Q006",
                "category": "Training",
                "question_text": "Do you have experience with lawn care or similar outdoor services?",
                "expected_answers": ["Yes, extensive experience", "Some experience", "No, but willing to learn"],
                "score_weight": 6
            },
            {
                "id": "Q007",
                "category": "Experience",
                "question_text": "Do you have previous business ownership or management experience?",
                "expected_answers": ["Yes, I own/owned a business", "Yes, management experience", "No, but eager to learn"],
                "score_weight": 8
            },
            {
                "id": "Q008",
                "category": "Commitment",
                "question_text": "Can you dedicate full-time hours to operating this franchise?",
                "expected_answers": ["Yes, full-time commitment", "Yes, this will be my primary focus"],
                "score_weight": 10
            },
            {
                "id": "Q009",
                "category": "Support",
                "question_text": "What level of ongoing support do you expect from the franchisor?",
                "expected_answers": ["Regular check-ins and guidance", "Training and marketing support", "Comprehensive ongoing support"],
                "score_weight": 6
            },
            {
                "id": "Q010",
                "category": "Goals",
                "question_text": "What are your long-term goals with this franchise opportunity?",
                "expected_answers": ["Build a sustainable business", "Achieve financial independence", "Expand to multiple territories"],
                "score_weight": 8
            }
        ]
    
    # Insert questions
    with engine.connect() as conn:
        for q in questions:
            question_uuid = str(uuid.uuid4())
            
            # Check if question already exists
            result = conn.execute(sa.text("""
                SELECT id FROM pre_qualification_questions WHERE question_id = :qid
            """), {"qid": q["id"]})
            
            if result.fetchone():
                print(f"   ⚠️  Question {q['id']} already exists, skipping...")
                continue
            
            # Insert question
            conn.execute(sa.text("""
                INSERT INTO pre_qualification_questions 
                (id, question_id, category, question_text, expected_answers, score_weight, is_active, created_at)
                VALUES (:id, :qid, :category, :text, :answers, :weight, :active, :created)
            """), {
                "id": question_uuid,
                "qid": q["id"],
                "category": q["category"],
                "text": q["question_text"],
                "answers": json.dumps(q["expected_answers"]),
                "weight": q["score_weight"],
                "active": True,
                "created": datetime.utcnow()
            })
            
            print(f"   ➕ Added question {q['id']}: {q['category']}")
        
        # Create template
        template_uuid = str(uuid.uuid4())
        total_score = sum(q["score_weight"] for q in questions)
        
        # Get question IDs
        result = conn.execute(sa.text("""
            SELECT id FROM pre_qualification_questions WHERE is_active = true
        """))
        question_ids = [str(row[0]) for row in result.fetchall()]
        
        # Check if template exists
        result = conn.execute(sa.text("""
            SELECT id FROM question_templates WHERE template_name = 'coochie_lawn_care'
        """))
        
        if not result.fetchone():
            conn.execute(sa.text("""
                INSERT INTO question_templates 
                (id, template_name, franchise_type, description, question_ids, total_possible_score, qualification_threshold, is_active, created_at)
                VALUES (:id, :name, :type, :desc, :qids, :total, :threshold, :active, :created)
            """), {
                "id": template_uuid,
                "name": "coochie_lawn_care",
                "type": "lawn_care",
                "desc": "Lead qualification questions for Coochie HydroGreen lawn care franchise",
                "qids": json.dumps(question_ids),
                "total": total_score,
                "threshold": int(total_score * 0.8),
                "active": True,
                "created": datetime.utcnow()
            })
            
            print(f"   ✅ Created template 'coochie_lawn_care' with {len(questions)} questions")
        else:
            print(f"   ⚠️  Template 'coochie_lawn_care' already exists")
        
        conn.commit()
    
    print(f"✅ Populated {len(questions)} questions and template")

def verify_setup(engine):
    """Verify the database setup"""
    
    print("🔍 Verifying database setup...")
    
    with engine.connect() as conn:
        # Check tables exist
        tables = [
            'leads', 'pre_qualification_questions', 'lead_responses',
            'lead_qualification_summaries', 'qualification_sessions',
            'question_templates', 'qualification_analytics'
        ]
        
        for table in tables:
            result = conn.execute(sa.text(f"""
                SELECT COUNT(*) FROM information_schema.tables 
                WHERE table_name = '{table}'
            """))
            count = result.fetchone()[0]
            
            if count > 0:
                print(f"   ✅ Table '{table}' exists")
            else:
                print(f"   ❌ Table '{table}' missing")
                return False
        
        # Check questions
        result = conn.execute(sa.text("""
            SELECT COUNT(*) FROM pre_qualification_questions WHERE is_active = true
        """))
        question_count = result.fetchone()[0]
        print(f"   📝 Active questions: {question_count}")
        
        # Check templates
        result = conn.execute(sa.text("""
            SELECT COUNT(*) FROM question_templates WHERE is_active = true
        """))
        template_count = result.fetchone()[0]
        print(f"   📋 Active templates: {template_count}")
    
    print("✅ Database verification completed!")
    return True

def main():
    """Main setup function"""
    
    print("🚀 SETTING UP LEAD QUALIFICATION DATABASE")
    print("=" * 60)
    
    # Get database URL
    db_url = get_database_url()
    if 'postgresql' in db_url:
        # Extract just the host and database name for display
        db_display = db_url.split('@')[1] if '@' in db_url else db_url
        print(f"📊 Database: PostgreSQL ({db_display})")
    else:
        print(f"📊 Database URL: {db_url}")
    
    try:
        # Create engine
        engine = create_engine(db_url)
        
        # Test connection
        with engine.connect() as conn:
            conn.execute(sa.text("SELECT 1"))
        print("✅ Database connection successful")
        
        # Create tables
        create_lead_qualification_tables(engine)
        
        # Populate questions
        populate_sample_questions(engine)
        
        # Verify setup
        verify_setup(engine)
        
        print("\n🎉 LEAD QUALIFICATION DATABASE SETUP COMPLETED!")
        print("\nNext steps:")
        print("1. Test the database with: python3 lead_qualification_demo.py")
        print("2. Start the FastAPI server: uvicorn app.main:app --reload")
        print("3. Access API docs at: http://localhost:8000/docs")
        
    except Exception as e:
        print(f"❌ Error setting up database: {e}")
        print("\nTroubleshooting:")
        print("1. Make sure PostgreSQL is running")
        print("2. Check database credentials")
        print("3. Ensure database 'growthhive' exists")
        print("4. Install required packages: pip install sqlalchemy psycopg2-binary")
        sys.exit(1)

if __name__ == "__main__":
    main()
