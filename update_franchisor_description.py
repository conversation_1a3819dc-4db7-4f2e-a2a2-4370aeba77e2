#!/usr/bin/env python3
"""
Update the Coochie Hydrogreen franchisor description with detailed franchise information
"""

import asyncio
import asyncpg

async def update_franchisor_description():
    """Update the franchisor description with detailed franchise information"""
    
    # Detailed franchise information
    franchise_description = """Coochie Hydrogreen Franchise Information:

Investment Requirements:
- Initial franchise fee: $45,000
- Total investment range: $150,000 to $300,000
- Equipment and installation: $80,000-$120,000
- Initial inventory: $15,000-$25,000
- Store fixtures and signage: $20,000-$35,000
- Working capital: $35,000-$50,000
- Training and grand opening: $5,000-$10,000

Ongoing Fees:
- Royalty fee: 6% of gross sales
- Marketing fee: 2% of gross sales

Marketing Support:
- National advertising campaigns
- Local marketing materials
- Digital marketing tools
- Social media templates
- Brand promotion and advertising campaigns

Training and Support:
- Comprehensive training program
- Ongoing operational support
- Business development assistance
- Technical support

Franchise Opportunities:
- Eco-friendly hydrogreen cleaning solutions
- Growing market demand
- Proven business model
- Comprehensive support system
- Protected territory rights

Startup Costs Breakdown:
The startup costs for Coochie Hydrogreen franchise include equipment, inventory, fixtures, working capital, and training expenses totaling between $150,000 to $300,000.

Franchisee Marketing Fees:
Franchisees pay a 2% marketing fee on gross sales which contributes to national brand promotion and advertising campaigns."""
    
    print("Updating Coochie Hydrogreen franchisor description...")
    
    # Connect to database
    conn = await asyncpg.connect(
        host="localhost",
        port=5432,
        user="postgres",
        password="root",
        database="growthhive"
    )
    
    try:
        # Update the franchisor description
        await conn.execute("""
            UPDATE franchisors 
            SET description = $1 
            WHERE id = $2
        """, franchise_description, "569976f2-d845-4615-8a91-96e18086adbe")
        
        print("✅ Successfully updated Coochie Hydrogreen franchisor description")
        print("The description now contains comprehensive franchise information including:")
        print("- Startup costs and investment requirements")
        print("- Marketing fees and ongoing costs")
        print("- Training and support details")
        print("- Franchise opportunities")
        
    finally:
        await conn.close()

if __name__ == "__main__":
    asyncio.run(update_franchisor_description())
