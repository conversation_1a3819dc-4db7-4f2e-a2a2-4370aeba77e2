#!/usr/bin/env python3
"""
Generate embeddings for document chunks
"""

import asyncio
import os
import asyncpg
from openai import AsyncOpenAI
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def generate_embeddings():
    """Generate embeddings for document chunks that don't have them"""
    
    # Initialize OpenAI client
    client = AsyncOpenAI(api_key=os.getenv("OPENAI_API_KEY"))
    
    # Connect to database
    conn = await asyncpg.connect(
        host="localhost",
        port=5432,
        user="postgres",
        password="root",
        database="growthhive"
    )
    
    try:
        # Get chunks without embeddings
        chunks = await conn.fetch("""
            SELECT id, text FROM document_chunks 
            WHERE embedding IS NULL
        """)
        
        print(f"Found {len(chunks)} chunks without embeddings")
        
        for chunk in chunks:
            chunk_id = chunk['id']
            text = chunk['text']
            
            print(f"Generating embedding for chunk: {text[:100]}...")
            
            # Generate embedding
            response = await client.embeddings.create(
                model="text-embedding-3-small",
                input=text
            )
            
            embedding = response.data[0].embedding
            
            # Update database with embedding
            await conn.execute("""
                UPDATE document_chunks 
                SET embedding = $1 
                WHERE id = $2
            """, embedding, chunk_id)
            
            print(f"✅ Updated embedding for chunk {chunk_id}")
        
        print(f"✅ Successfully generated embeddings for {len(chunks)} chunks")
        
    finally:
        await conn.close()

if __name__ == "__main__":
    asyncio.run(generate_embeddings())
