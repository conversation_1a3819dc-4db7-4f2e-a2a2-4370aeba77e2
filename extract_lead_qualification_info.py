#!/usr/bin/env python3
"""
Lead Qualification Information Extractor

This script analyzes the Coochie Information Pack PDF to extract key information
for generating lead qualification questions.
"""

import asyncio
import os
import sys
import json
from pathlib import Path
import logging

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(level=logging.WARNING)

# Configuration
PDF_PATH = "Coochie_Information pack.pdf"

async def extract_qualification_info():
    """Extract key information for lead qualification from the PDF"""
    print("🔍 EXTRACTING LEAD QUALIFICATION INFORMATION")
    print("=" * 60)
    
    try:
        from docqa.advanced_ingestion.layout_aware_processor import LayoutAwareProcessor, AdvancedConfig
        
        # Create configuration for qualification extraction
        config = AdvancedConfig(
            use_layoutparser=True,
            use_tesseract=True,
            use_easyocr=True,
            use_gpt4_vision=True,
            use_gpt4_text_enhancement=True,
            analyze_document_structure=True,
            extract_key_entities=True,
            enhance_text_quality=True,
            preserve_reading_order=True,
            merge_text_blocks=True,
            use_layout_aware_chunking=True,
            respect_section_boundaries=True,
            chunk_size=1000,
            chunk_overlap=200,
            parallel_processing=False,
            max_workers=2,
            timeout_seconds=300,
            extract_tables=False,
            extract_images=True,
            analyze_charts=True
        )
        
        # Initialize processor
        processor = LayoutAwareProcessor(
            openai_api_key=os.getenv('OPENAI_API_KEY', ''),
            config=config
        )
        
        if not os.path.exists(PDF_PATH):
            print(f"❌ PDF file not found: {PDF_PATH}")
            return None
        
        print(f"📄 Processing: {PDF_PATH}")
        print("⏳ Extracting content for qualification analysis...")
        
        # Process document
        result = await processor.process_document(Path(PDF_PATH))
        
        print(f"✅ Document processed successfully")
        print(f"   📝 Text extracted: {len(result.structured_text):,} characters")
        print(f"   🧩 Semantic chunks: {len(result.semantic_chunks)}")
        
        # Extract qualification-relevant information
        qualification_info = extract_qualification_categories(result.structured_text, result.semantic_chunks)
        
        return qualification_info
        
    except Exception as e:
        print(f"❌ Error extracting qualification info: {e}")
        import traceback
        traceback.print_exc()
        return None

def extract_qualification_categories(text: str, chunks: list) -> dict:
    """Extract categorized information relevant for lead qualification"""
    
    qualification_info = {
        'financial_requirements': [],
        'location_criteria': [],
        'training_obligations': [],
        'business_experience': [],
        'time_commitment': [],
        'support_expectations': [],
        'territory_preferences': [],
        'investment_capacity': [],
        'franchise_understanding': [],
        'growth_objectives': []
    }
    
    # Analyze text for qualification categories
    sentences = [s.strip() for s in text.split('.') if len(s.strip()) > 20]
    
    # Financial Requirements
    financial_keywords = ['fee', 'cost', 'investment', 'royalty', 'revenue', 'profit', 'financial', 'capital', 'funding']
    for sentence in sentences:
        if any(keyword in sentence.lower() for keyword in financial_keywords):
            if '$' in sentence or '%' in sentence or 'cost' in sentence.lower():
                qualification_info['financial_requirements'].append(sentence.strip())
    
    # Location Criteria
    location_keywords = ['location', 'area', 'territory', 'region', 'market', 'demographic', 'population']
    for sentence in sentences:
        if any(keyword in sentence.lower() for keyword in location_keywords):
            qualification_info['location_criteria'].append(sentence.strip())
    
    # Training Obligations
    training_keywords = ['training', 'education', 'course', 'program', 'learn', 'skill', 'certification']
    for sentence in sentences:
        if any(keyword in sentence.lower() for keyword in training_keywords):
            qualification_info['training_obligations'].append(sentence.strip())
    
    # Business Experience
    experience_keywords = ['experience', 'background', 'business', 'management', 'entrepreneur', 'owner']
    for sentence in sentences:
        if any(keyword in sentence.lower() for keyword in experience_keywords):
            qualification_info['business_experience'].append(sentence.strip())
    
    # Time Commitment
    time_keywords = ['time', 'hours', 'commitment', 'dedication', 'full-time', 'part-time', 'schedule']
    for sentence in sentences:
        if any(keyword in sentence.lower() for keyword in time_keywords):
            qualification_info['time_commitment'].append(sentence.strip())
    
    # Support Expectations
    support_keywords = ['support', 'assistance', 'help', 'guidance', 'service', 'backup']
    for sentence in sentences:
        if any(keyword in sentence.lower() for keyword in support_keywords):
            qualification_info['support_expectations'].append(sentence.strip())
    
    # Territory Preferences
    territory_keywords = ['territory', 'exclusive', 'protected', 'coverage', 'market area']
    for sentence in sentences:
        if any(keyword in sentence.lower() for keyword in territory_keywords):
            qualification_info['territory_preferences'].append(sentence.strip())
    
    # Investment Capacity
    investment_keywords = ['invest', 'capital', 'fund', 'finance', 'budget', 'afford']
    for sentence in sentences:
        if any(keyword in sentence.lower() for keyword in investment_keywords):
            qualification_info['investment_capacity'].append(sentence.strip())
    
    # Franchise Understanding
    franchise_keywords = ['franchise', 'franchisor', 'franchisee', 'system', 'brand', 'concept']
    for sentence in sentences:
        if any(keyword in sentence.lower() for keyword in franchise_keywords):
            qualification_info['franchise_understanding'].append(sentence.strip())
    
    # Growth Objectives
    growth_keywords = ['growth', 'expand', 'scale', 'develop', 'build', 'increase', 'goal']
    for sentence in sentences:
        if any(keyword in sentence.lower() for keyword in growth_keywords):
            qualification_info['growth_objectives'].append(sentence.strip())
    
    # Limit results and remove duplicates
    for category in qualification_info:
        qualification_info[category] = list(set(qualification_info[category]))[:5]
    
    return qualification_info

def generate_qualification_questions(qualification_info: dict) -> list:
    """Generate lead qualification questions based on extracted information"""
    
    questions = []
    question_id = 1
    
    # Financial Requirements Questions
    if qualification_info['financial_requirements']:
        questions.extend([
            {
                'id': f'Q{question_id:03d}',
                'category': 'Financial',
                'question_text': 'What is your available investment budget for this franchise opportunity?',
                'expected_answers': ['$50,000-$100,000', '$100,000-$200,000', '$200,000+'],
                'score_weight': 15,
                'context': qualification_info['financial_requirements'][:2]
            },
            {
                'id': f'Q{question_id+1:03d}',
                'category': 'Financial',
                'question_text': 'Are you comfortable with ongoing royalty payments of 10% of monthly revenue?',
                'expected_answers': ['Yes', 'Absolutely', 'I understand and accept'],
                'score_weight': 10,
                'context': qualification_info['financial_requirements'][:2]
            }
        ])
        question_id += 2
    
    # Location Criteria Questions
    if qualification_info['location_criteria']:
        questions.extend([
            {
                'id': f'Q{question_id:03d}',
                'category': 'Location',
                'question_text': 'Do you have a specific geographic area in mind for your franchise?',
                'expected_answers': ['Yes, I have identified an area', 'I have some areas in mind', 'I need help selecting'],
                'score_weight': 8,
                'context': qualification_info['location_criteria'][:2]
            },
            {
                'id': f'Q{question_id+1:03d}',
                'category': 'Location',
                'question_text': 'Are you willing to operate in suburban residential areas?',
                'expected_answers': ['Yes', 'Definitely', 'That\'s my preference'],
                'score_weight': 7,
                'context': qualification_info['location_criteria'][:2]
            }
        ])
        question_id += 2
    
    # Training Obligations Questions
    if qualification_info['training_obligations']:
        questions.extend([
            {
                'id': f'Q{question_id:03d}',
                'category': 'Training',
                'question_text': 'Are you available to complete a 4-week training program within the first 12 months?',
                'expected_answers': ['Yes', 'Absolutely', 'I can commit to that'],
                'score_weight': 12,
                'context': qualification_info['training_obligations'][:2]
            },
            {
                'id': f'Q{question_id+1:03d}',
                'category': 'Training',
                'question_text': 'Do you have experience with lawn care or similar outdoor services?',
                'expected_answers': ['Yes, extensive experience', 'Some experience', 'No, but willing to learn'],
                'score_weight': 6,
                'context': qualification_info['training_obligations'][:2]
            }
        ])
        question_id += 2
    
    # Business Experience Questions
    if qualification_info['business_experience']:
        questions.extend([
            {
                'id': f'Q{question_id:03d}',
                'category': 'Experience',
                'question_text': 'Do you have previous business ownership or management experience?',
                'expected_answers': ['Yes, I own/owned a business', 'Yes, management experience', 'No, but eager to learn'],
                'score_weight': 8,
                'context': qualification_info['business_experience'][:2]
            }
        ])
        question_id += 1
    
    # Time Commitment Questions
    if qualification_info['time_commitment']:
        questions.extend([
            {
                'id': f'Q{question_id:03d}',
                'category': 'Commitment',
                'question_text': 'Can you dedicate full-time hours to operating this franchise?',
                'expected_answers': ['Yes, full-time commitment', 'Yes, this will be my primary focus'],
                'score_weight': 10,
                'context': qualification_info['time_commitment'][:2]
            }
        ])
        question_id += 1
    
    # Support Expectations Questions
    if qualification_info['support_expectations']:
        questions.extend([
            {
                'id': f'Q{question_id:03d}',
                'category': 'Support',
                'question_text': 'What level of ongoing support do you expect from the franchisor?',
                'expected_answers': ['Regular check-ins and guidance', 'Training and marketing support', 'Comprehensive ongoing support'],
                'score_weight': 6,
                'context': qualification_info['support_expectations'][:2]
            }
        ])
        question_id += 1
    
    # Growth Objectives Questions
    if qualification_info['growth_objectives']:
        questions.extend([
            {
                'id': f'Q{question_id:03d}',
                'category': 'Goals',
                'question_text': 'What are your long-term goals with this franchise opportunity?',
                'expected_answers': ['Build a sustainable business', 'Achieve financial independence', 'Expand to multiple territories'],
                'score_weight': 8,
                'context': qualification_info['growth_objectives'][:2]
            }
        ])
        question_id += 1
    
    return questions

async def main():
    """Main function to extract qualification information and generate questions"""
    print("🚀 LEAD QUALIFICATION SYSTEM - PDF ANALYSIS")
    print("=" * 70)
    
    # Check prerequisites
    if not os.getenv('OPENAI_API_KEY'):
        print("❌ OPENAI_API_KEY environment variable not set")
        sys.exit(1)
    
    if not os.path.exists(PDF_PATH):
        print(f"❌ PDF file not found: {PDF_PATH}")
        sys.exit(1)
    
    # Extract qualification information
    print("📋 Step 1: Extracting qualification information from PDF...")
    qualification_info = await extract_qualification_info()
    
    if not qualification_info:
        print("❌ Failed to extract qualification information")
        sys.exit(1)
    
    # Display extracted information
    print("\n📊 EXTRACTED QUALIFICATION CATEGORIES:")
    print("=" * 50)
    
    total_items = 0
    for category, items in qualification_info.items():
        if items:
            print(f"\n{category.replace('_', ' ').title()} ({len(items)} items):")
            for i, item in enumerate(items[:3], 1):
                print(f"   {i}. {item[:100]}{'...' if len(item) > 100 else ''}")
            total_items += len(items)
    
    print(f"\nTotal qualification items extracted: {total_items}")
    
    # Generate qualification questions
    print("\n🤖 Step 2: Generating lead qualification questions...")
    questions = generate_qualification_questions(qualification_info)
    
    print(f"\n📝 GENERATED QUALIFICATION QUESTIONS:")
    print("=" * 50)
    
    total_score = 0
    for question in questions:
        print(f"\n{question['id']} - {question['category']}")
        print(f"Question: {question['question_text']}")
        print(f"Expected Answers: {', '.join(question['expected_answers'])}")
        print(f"Score Weight: {question['score_weight']} points")
        total_score += question['score_weight']
    
    print(f"\nTotal Questions: {len(questions)}")
    print(f"Total Possible Score: {total_score} points")
    print(f"Qualification Threshold (80%): {total_score * 0.8:.0f} points")
    
    # Save results to JSON
    results = {
        'qualification_info': qualification_info,
        'questions': questions,
        'total_questions': len(questions),
        'total_possible_score': total_score,
        'qualification_threshold': int(total_score * 0.8)
    }
    
    with open('lead_qualification_data.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n💾 Results saved to: lead_qualification_data.json")
    
    print("\n🎉 QUALIFICATION ANALYSIS COMPLETED!")
    print("Next steps:")
    print("1. Review generated questions for accuracy")
    print("2. Create database schema for questions and responses")
    print("3. Implement qualification API endpoints")
    print("4. Test with sample lead responses")

if __name__ == "__main__":
    asyncio.run(main())
