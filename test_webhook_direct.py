#!/usr/bin/env python3
"""
Direct Webhook Testing

Test the webhook functionality directly without requiring the full FastAPI server.
This simulates the Kudosity webhook payload processing.
"""

import os
import sys
import json
import uuid
from datetime import datetime
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import sqlalchemy as sa
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

def get_database_url():
    """Get database URL from environment"""
    db_url = os.getenv('DATABASE_URL')
    if db_url and 'asyncpg' in db_url:
        db_url = db_url.replace('postgresql+asyncpg://', 'postgresql://')
    return db_url or "postgresql://postgres:root@localhost:5432/growthhive"

class DirectWebhookTester:
    """Test webhook functionality directly"""
    
    def __init__(self):
        self.engine = create_engine(get_database_url())
        self.SessionLocal = sessionmaker(bind=self.engine)
    
    def create_kudosity_payload(self, phone: str, message: str) -> dict:
        """Create realistic Kudosity webhook payload"""
        
        return {
            "event_type": "SMS_INBOUND",
            "message_id": f"kudosity_{int(datetime.now().timestamp())}_{hash(phone) % 10000}",
            "from_number": phone,
            "to_number": "+61-400-COMPANY",
            "message_text": message,
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "metadata": {
                "carrier": "Telstra",
                "country": "AU",
                "region": "NSW"
            }
        }
    
    def get_or_create_lead(self, phone_number: str, franchisor_id: str) -> str:
        """Get or create lead for phone number"""
        
        with self.SessionLocal() as db:
            # Check for existing lead
            result = db.execute(text("""
                SELECT id FROM leads WHERE phone = :phone AND is_deleted = false
            """), {"phone": phone_number})
            
            existing_lead = result.fetchone()
            if existing_lead:
                return str(existing_lead[0])
            
            # Create new lead
            lead_id = str(uuid.uuid4())
            db.execute(text("""
                INSERT INTO leads
                (id, full_name, phone, lead_source, qualification_status, status, brand_preference, is_active, is_deleted, created_at, updated_at)
                VALUES (:id, :full_name, :phone, :source, :qual_status, :status, :brand_pref, :is_active, :is_deleted, :created_at, :updated_at)
            """), {
                "id": lead_id,
                "full_name": f"SMS Lead {phone_number[-4:]}",  # Default name using last 4 digits
                "phone": phone_number,
                "source": "sms_inbound",
                "qual_status": "new",
                "status": "new",
                "brand_pref": franchisor_id,
                "is_active": True,
                "is_deleted": False,
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            })
            
            db.commit()
            print(f"   ✅ Created new lead: {lead_id[:8]}...")
            return lead_id
    
    def get_or_create_conversation(self, phone_number: str) -> dict:
        """Get or create conversation state"""
        
        with self.SessionLocal() as db:
            # Check for existing active conversation
            result = db.execute(text("""
                SELECT id, current_stage, context_data, franchisor_id, lead_id
                FROM conversation_states 
                WHERE phone_number = :phone AND is_active = true
            """), {"phone": phone_number})
            
            existing_conv = result.fetchone()
            if existing_conv:
                return {
                    "id": str(existing_conv[0]),
                    "stage": existing_conv[1],
                    "context": existing_conv[2] or {},
                    "franchisor_id": str(existing_conv[3]),
                    "lead_id": str(existing_conv[4]),
                    "is_new": False
                }
            
            # Get first available franchisor
            result = db.execute(text("SELECT id, name FROM franchisors WHERE is_active = true LIMIT 1"))
            franchisor_row = result.fetchone()
            
            if not franchisor_row:
                raise ValueError("No active franchisor found")
            
            franchisor_id = str(franchisor_row[0])
            franchisor_name = franchisor_row[1]
            
            # Create lead
            lead_id = self.get_or_create_lead(phone_number, franchisor_id)
            
            # Create conversation
            conv_id = str(uuid.uuid4())
            context_data = {
                "rep_name": "Sales Representative",
                "company_name": franchisor_name,
                "lead_first_name": "there"
            }
            
            db.execute(text("""
                INSERT INTO conversation_states 
                (id, lead_id, phone_number, franchisor_id, current_stage, context_data, is_active, created_at, updated_at)
                VALUES (:id, :lead_id, :phone, :franchisor_id, :stage, :context, :is_active, :created_at, :updated_at)
            """), {
                "id": conv_id,
                "lead_id": lead_id,
                "phone": phone_number,
                "franchisor_id": franchisor_id,
                "stage": "greeting",
                "context": json.dumps(context_data),
                "is_active": True,
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            })
            
            db.commit()
            print(f"   ✅ Created conversation: {conv_id[:8]}...")
            
            return {
                "id": conv_id,
                "stage": "greeting",
                "context": context_data,
                "franchisor_id": franchisor_id,
                "lead_id": lead_id,
                "is_new": True
            }
    
    def get_sales_script(self, franchisor_id: str, script_type: str) -> str:
        """Get sales script from database"""
        
        with self.SessionLocal() as db:
            result = db.execute(text("""
                SELECT script_body FROM sales_scripts 
                WHERE franchisor_id = :franchisor_id 
                AND script_type = :script_type 
                AND is_active = true
                LIMIT 1
            """), {
                "franchisor_id": franchisor_id,
                "script_type": script_type
            })
            
            script_row = result.fetchone()
            if script_row:
                return script_row[0]
            
            # Fallback scripts
            fallback_scripts = {
                "greeting": "Hi, this is {{rep_name}} calling on behalf of {{company_name}}. You inquired about this opportunity. Is this a good time to chat?",
                "qualification": "Great! I'd like to ask you a few questions to better understand your background. Are you ready to get started?",
                "qa": "I'd be happy to answer any questions you have about {{company_name}}. What would you like to know?",
                "scheduling": "You seem like a great fit! I'd like to schedule a detailed discussion. Would you prefer a call this week or next week?"
            }
            
            return fallback_scripts.get(script_type, "Thank you for your interest in {{company_name}}.")
    
    def render_script(self, script_body: str, context: dict) -> str:
        """Simple script rendering"""
        
        rendered = script_body
        for key, value in context.items():
            placeholder = f"{{{{{key}}}}}"
            rendered = rendered.replace(placeholder, str(value))
        
        return rendered
    
    def analyze_message_intent(self, message: str, current_stage: str) -> dict:
        """Simple message intent analysis"""
        
        message_lower = message.lower()
        
        # Positive indicators
        positive_words = ["yes", "sure", "okay", "good time", "great", "absolutely", "definitely", "interested"]
        is_positive = any(word in message_lower for word in positive_words)
        
        # Question indicators
        question_words = ["what", "how", "when", "where", "why", "who", "can you", "do you"]
        is_question = any(word in message_lower for word in question_words) or "?" in message
        
        # Scheduling indicators
        scheduling_words = ["schedule", "meeting", "call", "appointment", "next steps", "when can"]
        wants_scheduling = any(word in message_lower for word in scheduling_words)
        
        return {
            "is_positive": is_positive,
            "is_question": is_question,
            "wants_scheduling": wants_scheduling,
            "message_length": len(message),
            "intent": "positive" if is_positive else "question" if is_question else "neutral"
        }
    
    def process_webhook_message(self, payload: dict) -> dict:
        """Process webhook message and generate response"""
        
        phone_number = payload["from_number"]
        message_text = payload["message_text"]
        message_id = payload["message_id"]
        
        print(f"\n📨 Processing webhook message:")
        print(f"   From: {phone_number}")
        print(f"   Message: \"{message_text}\"")
        print(f"   ID: {message_id}")
        
        try:
            # Get or create conversation
            conversation = self.get_or_create_conversation(phone_number)
            
            # Store inbound message
            with self.SessionLocal() as db:
                msg_id = str(uuid.uuid4())
                db.execute(text("""
                    INSERT INTO conversation_messages 
                    (id, conversation_state_id, message_text, message_type, message_source, kudosity_message_id, created_at)
                    VALUES (:id, :conv_id, :text, :type, :source, :kudosity_id, :created_at)
                """), {
                    "id": msg_id,
                    "conv_id": conversation["id"],
                    "text": message_text,
                    "type": "inbound",
                    "source": "sms",
                    "kudosity_id": message_id,
                    "created_at": datetime.utcnow()
                })
                db.commit()
            
            # Analyze message intent
            intent = self.analyze_message_intent(message_text, conversation["stage"])
            print(f"   🔍 Intent: {intent['intent']} (positive: {intent['is_positive']}, question: {intent['is_question']})")
            
            # Determine response based on stage and intent
            response_script = ""
            new_stage = conversation["stage"]
            
            if conversation["stage"] == "greeting":
                if intent["is_positive"]:
                    response_script = self.get_sales_script(conversation["franchisor_id"], "qualification")
                    new_stage = "qualification"
                elif intent["is_question"]:
                    response_script = self.get_sales_script(conversation["franchisor_id"], "qa")
                    new_stage = "qa"
                else:
                    response_script = "I understand. When would be a better time to reach you about this {{company_name}} opportunity?"
            
            elif conversation["stage"] == "qualification":
                if intent["wants_scheduling"]:
                    response_script = self.get_sales_script(conversation["franchisor_id"], "scheduling")
                    new_stage = "scheduling"
                elif intent["is_question"]:
                    response_script = self.get_sales_script(conversation["franchisor_id"], "qa")
                    new_stage = "qa"
                else:
                    response_script = "Thank you for that information. Do you have any questions about the {{company_name}} opportunity?"
                    new_stage = "qa"
            
            elif conversation["stage"] == "qa":
                if intent["wants_scheduling"]:
                    response_script = self.get_sales_script(conversation["franchisor_id"], "scheduling")
                    new_stage = "scheduling"
                else:
                    response_script = "That's a great question about {{company_name}}. Based on our franchise documentation, I can provide you with detailed information. Do you have any other questions?"
            
            elif conversation["stage"] == "scheduling":
                response_script = "Perfect! I'll send you a calendar link to schedule a detailed discussion with our franchise development team."
                new_stage = "completed"
            
            else:
                response_script = "Thank you for your continued interest in {{company_name}}. A team member will follow up with you shortly."
            
            # Render response
            agent_response = self.render_script(response_script, conversation["context"])
            
            # Store outbound message and update conversation
            with self.SessionLocal() as db:
                # Store outbound message
                out_msg_id = str(uuid.uuid4())
                db.execute(text("""
                    INSERT INTO conversation_messages 
                    (id, conversation_state_id, message_text, message_type, message_source, agent_response, created_at)
                    VALUES (:id, :conv_id, :text, :type, :source, :agent_resp, :created_at)
                """), {
                    "id": out_msg_id,
                    "conv_id": conversation["id"],
                    "text": agent_response,
                    "type": "outbound",
                    "source": "agent",
                    "agent_resp": agent_response,
                    "created_at": datetime.utcnow()
                })
                
                # Update conversation stage
                db.execute(text("""
                    UPDATE conversation_states 
                    SET current_stage = :stage, last_message_at = :last_msg, updated_at = :updated_at
                    WHERE id = :conv_id
                """), {
                    "stage": new_stage,
                    "last_msg": datetime.utcnow(),
                    "updated_at": datetime.utcnow(),
                    "conv_id": conversation["id"]
                })
                
                db.commit()
            
            print(f"   🤖 Agent response: {agent_response}")
            print(f"   📊 Stage: {conversation['stage']} → {new_stage}")
            
            return {
                "success": True,
                "message_id": message_id,
                "agent_response": agent_response,
                "conversation_stage": new_stage,
                "intent_analysis": intent
            }
            
        except Exception as e:
            print(f"   ❌ Error processing message: {e}")
            return {
                "success": False,
                "message_id": message_id,
                "error": str(e)
            }
    
    def test_conversation_scenarios(self):
        """Test multiple conversation scenarios"""
        
        print("🎭 TESTING CONVERSATION SCENARIOS")
        print("=" * 60)
        
        scenarios = [
            {
                "name": "Interested Lead",
                "phone": "+61-400-555-001",
                "messages": [
                    "Yes, this is a good time to chat",
                    "I have some questions about the investment",
                    "What kind of training do you provide?",
                    "I'd like to schedule a meeting"
                ]
            },
            {
                "name": "Cautious Lead",
                "phone": "+61-400-555-002",
                "messages": [
                    "I have some questions first",
                    "What are the startup costs?",
                    "How much ongoing support do you provide?",
                    "Let's schedule a call"
                ]
            }
        ]
        
        results = []
        
        for scenario in scenarios:
            print(f"\n🎯 Scenario: {scenario['name']}")
            print(f"📱 Phone: {scenario['phone']}")
            
            scenario_results = []
            
            for i, message in enumerate(scenario['messages'], 1):
                print(f"\n   📍 Message {i}: \"{message}\"")
                
                payload = self.create_kudosity_payload(scenario['phone'], message)
                result = self.process_webhook_message(payload)
                scenario_results.append(result)
                
                if not result['success']:
                    print(f"   ❌ Message processing failed")
                    break
            
            results.append({
                "scenario": scenario['name'],
                "success": all(r['success'] for r in scenario_results),
                "messages_processed": len(scenario_results)
            })
        
        return results
    
    def run_webhook_tests(self):
        """Run comprehensive webhook tests"""
        
        print("🚀 DIRECT WEBHOOK TESTING")
        print("=" * 70)
        print("Testing Kudosity webhook processing without FastAPI server")
        
        # Test database connectivity
        try:
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            print("✅ Database connection successful")
        except Exception as e:
            print(f"❌ Database connection failed: {e}")
            return False
        
        # Test conversation scenarios
        scenario_results = self.test_conversation_scenarios()
        
        # Summary
        print(f"\n🎉 WEBHOOK TESTING SUMMARY")
        print("=" * 50)
        
        successful = sum(1 for r in scenario_results if r['success'])
        total = len(scenario_results)
        
        print(f"Scenarios tested: {successful}/{total} successful")
        
        for result in scenario_results:
            status = "✅" if result['success'] else "❌"
            print(f"   {status} {result['scenario']} ({result['messages_processed']} messages)")
        
        if successful == total:
            print(f"\n🎯 ALL WEBHOOK TESTS PASSED!")
            print(f"\n✅ Webhook Processing Ready:")
            print(f"   • Kudosity payload processing ✅")
            print(f"   • Conversation state management ✅")
            print(f"   • Message intent analysis ✅")
            print(f"   • Dynamic script rendering ✅")
            print(f"   • Database persistence ✅")
            
            print(f"\n📱 Ready for Kudosity Integration:")
            print(f"   • Webhook endpoint: /api/v1/sms/kudosity/sms-inbound")
            print(f"   • Event type: SMS_INBOUND")
            print(f"   • Method: POST")
            print(f"   • Content-Type: application/json")
        else:
            print(f"\n⚠️  Some tests failed. Review errors above.")
        
        return successful == total

def main():
    """Main test function"""
    
    try:
        tester = DirectWebhookTester()
        success = tester.run_webhook_tests()
        
        if success:
            print(f"\n🎉 WEBHOOK TESTING SUCCESSFUL!")
            print(f"Your SMS chatbot system is ready for Kudosity integration!")
        else:
            print(f"\n⚠️  TESTING INCOMPLETE")
            print(f"Please address the issues before production deployment.")
            
    except Exception as e:
        print(f"❌ Testing failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
