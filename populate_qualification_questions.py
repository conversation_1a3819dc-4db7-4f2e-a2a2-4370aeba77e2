#!/usr/bin/env python3
"""
Populate Qualification Questions

Script to populate the database with lead qualification questions
extracted from the Coochie Information Pack PDF.
"""

import asyncio
import json
import os
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal, engine
from app.models.lead_qualification import (
    PreQualificationQuestion, QuestionTemplate, Base
)
from app.agents.question_generation_agent import QuestionGenerationAgent


async def create_tables():
    """Create database tables"""
    print("🔧 Creating database tables...")
    Base.metadata.create_all(bind=engine)
    print("✅ Database tables created")


async def load_questions_from_json(file_path: str = "lead_qualification_data.json"):
    """Load questions from the generated JSON file"""
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return None
    
    with open(file_path, 'r') as f:
        data = json.load(f)
    
    return data


async def populate_questions(db: Session, questions_data: dict):
    """Populate the database with qualification questions"""
    
    print("📝 Populating qualification questions...")
    
    questions = questions_data.get('questions', [])
    
    for q_data in questions:
        # Check if question already exists
        existing = db.query(PreQualificationQuestion).filter(
            PreQualificationQuestion.question_id == q_data['id']
        ).first()
        
        if existing:
            print(f"   ⚠️  Question {q_data['id']} already exists, updating...")
            existing.category = q_data['category']
            existing.question_text = q_data['question_text']
            existing.expected_answers = q_data['expected_answers']
            existing.score_weight = q_data['score_weight']
            existing.context_info = q_data.get('context', [])
        else:
            print(f"   ➕ Adding question {q_data['id']}: {q_data['category']}")
            question = PreQualificationQuestion(
                question_id=q_data['id'],
                category=q_data['category'],
                question_text=q_data['question_text'],
                expected_answers=q_data['expected_answers'],
                score_weight=q_data['score_weight'],
                context_info=q_data.get('context', []),
                is_active=True
            )
            db.add(question)
    
    db.commit()
    print(f"✅ Added/updated {len(questions)} questions")


async def create_question_template(db: Session, questions_data: dict):
    """Create a question template for Coochie franchise"""
    
    print("📋 Creating question template...")
    
    # Get all question IDs from the database
    questions = db.query(PreQualificationQuestion).filter(
        PreQualificationQuestion.is_active == True
    ).all()
    
    question_ids = [str(q.id) for q in questions]
    total_score = sum(q.score_weight for q in questions)
    
    # Check if template already exists
    existing_template = db.query(QuestionTemplate).filter(
        QuestionTemplate.template_name == "coochie_lawn_care"
    ).first()
    
    if existing_template:
        print("   ⚠️  Template 'coochie_lawn_care' already exists, updating...")
        existing_template.question_ids = question_ids
        existing_template.total_possible_score = total_score
        existing_template.qualification_threshold = int(total_score * 0.8)
        existing_template.is_active = True
    else:
        print("   ➕ Creating new template 'coochie_lawn_care'")
        template = QuestionTemplate(
            template_name="coochie_lawn_care",
            franchise_type="lawn_care",
            description="Lead qualification questions for Coochie HydroGreen lawn care franchise",
            question_ids=question_ids,
            total_possible_score=total_score,
            qualification_threshold=int(total_score * 0.8),
            is_active=True
        )
        db.add(template)
    
    db.commit()
    print(f"✅ Template created with {len(question_ids)} questions")
    print(f"   Total possible score: {total_score}")
    print(f"   Qualification threshold (80%): {int(total_score * 0.8)}")


async def enhance_questions_with_ai(db: Session):
    """Enhance questions using AI agent"""
    
    print("🤖 Enhancing questions with AI...")
    
    # Load the original document content for enhancement
    try:
        from docqa.advanced_ingestion.layout_aware_processor import LayoutAwareProcessor, AdvancedConfig
        
        config = AdvancedConfig(
            use_layoutparser=True,
            use_tesseract=True,
            use_easyocr=True,
            chunk_size=1000,
            chunk_overlap=200
        )
        
        processor = LayoutAwareProcessor(
            openai_api_key=os.getenv('OPENAI_API_KEY', ''),
            config=config
        )
        
        # Process the PDF to get content
        pdf_path = "Coochie_Information pack.pdf"
        if os.path.exists(pdf_path):
            result = await processor.process_document(Path(pdf_path))
            document_content = result.structured_text
            
            # Get current questions
            questions = db.query(PreQualificationQuestion).filter(
                PreQualificationQuestion.is_active == True
            ).all()
            
            questions_data = [
                {
                    'question_id': q.question_id,
                    'category': q.category,
                    'question_text': q.question_text,
                    'expected_answers': q.expected_answers,
                    'score_weight': q.score_weight
                }
                for q in questions
            ]
            
            # Enhance with AI
            agent = QuestionGenerationAgent()
            enhanced_questions = await agent.enhance_existing_questions(
                questions_data, document_content
            )
            
            # Update questions in database
            for enhanced in enhanced_questions:
                question = db.query(PreQualificationQuestion).filter(
                    PreQualificationQuestion.question_id == enhanced['question_id']
                ).first()
                
                if question:
                    question.question_text = enhanced['question_text']
                    question.expected_answers = enhanced['expected_answers']
                    question.score_weight = enhanced.get('score_weight', question.score_weight)
            
            db.commit()
            print(f"✅ Enhanced {len(enhanced_questions)} questions with AI")
        
    except Exception as e:
        print(f"⚠️  AI enhancement failed: {e}")
        print("   Continuing with basic questions...")


async def validate_questions(db: Session):
    """Validate the populated questions"""
    
    print("🔍 Validating questions...")
    
    questions = db.query(PreQualificationQuestion).filter(
        PreQualificationQuestion.is_active == True
    ).all()
    
    issues = []
    
    for question in questions:
        # Check for required fields
        if not question.question_text:
            issues.append(f"Question {question.question_id}: Missing question text")
        
        if not question.expected_answers:
            issues.append(f"Question {question.question_id}: Missing expected answers")
        
        if question.score_weight <= 0:
            issues.append(f"Question {question.question_id}: Invalid score weight")
        
        # Check for reasonable answer options
        if len(question.expected_answers) < 2:
            issues.append(f"Question {question.question_id}: Should have at least 2 answer options")
    
    if issues:
        print("❌ Validation issues found:")
        for issue in issues:
            print(f"   • {issue}")
        return False
    else:
        print("✅ All questions validated successfully")
        return True


async def display_summary(db: Session):
    """Display summary of populated questions"""
    
    print("\n📊 QUALIFICATION QUESTIONS SUMMARY")
    print("=" * 50)
    
    # Count by category
    from sqlalchemy import func
    
    category_counts = db.query(
        PreQualificationQuestion.category,
        func.count(PreQualificationQuestion.id).label('count'),
        func.sum(PreQualificationQuestion.score_weight).label('total_score')
    ).filter(
        PreQualificationQuestion.is_active == True
    ).group_by(PreQualificationQuestion.category).all()
    
    total_questions = 0
    total_score = 0
    
    for category, count, score in category_counts:
        print(f"{category}: {count} questions, {score} points")
        total_questions += count
        total_score += score
    
    print(f"\nTotal: {total_questions} questions, {total_score} points")
    print(f"Qualification threshold (80%): {int(total_score * 0.8)} points")
    
    # Show templates
    templates = db.query(QuestionTemplate).filter(
        QuestionTemplate.is_active == True
    ).all()
    
    print(f"\nTemplates: {len(templates)}")
    for template in templates:
        print(f"  • {template.template_name} ({template.franchise_type})")
        print(f"    {len(template.question_ids)} questions, {template.total_possible_score} points")


async def main():
    """Main function"""
    
    print("🚀 POPULATING LEAD QUALIFICATION QUESTIONS")
    print("=" * 60)
    
    # Check prerequisites
    if not os.getenv('OPENAI_API_KEY'):
        print("⚠️  OPENAI_API_KEY not set, AI enhancement will be skipped")
    
    # Create database tables
    await create_tables()
    
    # Load questions data
    questions_data = await load_questions_from_json()
    if not questions_data:
        print("❌ Failed to load questions data")
        return
    
    # Create database session
    db = SessionLocal()
    
    try:
        # Populate questions
        await populate_questions(db, questions_data)
        
        # Create template
        await create_question_template(db, questions_data)
        
        # Enhance with AI (if API key available)
        if os.getenv('OPENAI_API_KEY'):
            await enhance_questions_with_ai(db)
        
        # Validate questions
        is_valid = await validate_questions(db)
        
        if is_valid:
            # Display summary
            await display_summary(db)
            
            print("\n🎉 QUALIFICATION QUESTIONS SETUP COMPLETED!")
            print("\nNext steps:")
            print("1. Test the qualification API endpoints")
            print("2. Create sample leads and run qualification sessions")
            print("3. Review and adjust question scoring as needed")
            print("4. Set up analytics and monitoring")
        else:
            print("\n❌ Setup completed with validation issues")
            print("Please review and fix the issues before proceeding")
    
    finally:
        db.close()


if __name__ == "__main__":
    asyncio.run(main())
