#!/usr/bin/env python3
"""
Ingest the Coochie Hydrogreen PDF using the existing DocQA ingestion system
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from docqa.ingest import DocumentIngestionService
from docqa.config import config

async def ingest_coochie_pdf():
    """Ingest the Coochie Hydrogreen PDF using the existing ingestion system"""
    
    # Path to the PDF file
    pdf_path = "Coochie_Information pack.pdf"
    
    if not os.path.exists(pdf_path):
        print(f"❌ PDF file not found: {pdf_path}")
        return False
    
    print(f"📄 Found PDF file: {pdf_path}")
    print(f"📊 File size: {os.path.getsize(pdf_path)} bytes")
    
    try:
        # Initialize the ingestion service
        print("🔧 Initializing DocQA ingestion service...")
        ingestion_service = DocumentIngestionService()
        
        # Set the franchisor ID for Coochie Hydrogreen
        franchisor_id = "569976f2-d845-4615-8a91-96e18086adbe"
        
        print(f"🚀 Starting ingestion for franchisor: {franchisor_id}")
        print(f"📁 Processing file: {pdf_path}")
        
        # Ingest the document
        # The ingestion service will:
        # 1. Extract text from the PDF
        # 2. Create chunks
        # 3. Generate embeddings
        # 4. Store in the vector database
        result = ingestion_service.ingest_document(
            source=pdf_path,
            force_table="documents",  # Store in documents table
            translate=False,  # Don't translate (already in English)
            extract_charts=True,  # Extract charts if any
            document_id=None  # Let it generate a new document ID
        )
        
        if result.success:
            print("✅ Successfully ingested Coochie Hydrogreen PDF!")
            print(f"   📄 Document ID: {result.document_id}")
            print(f"   📊 Chunks created: {result.chunks_created}")
            print(f"   🗂️ Table: {result.table_name}")
            print(f"   ⏱️ Processing time: {result.processing_time:.2f}s")
            
            # Now we need to link this document to the franchisor
            await link_document_to_franchisor(result.document_id, franchisor_id)
            
            return True
        else:
            print(f"❌ Ingestion failed: {result.error_message}")
            return False
            
    except Exception as e:
        print(f"❌ Error during ingestion: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def link_document_to_franchisor(document_id: str, franchisor_id: str):
    """Link the ingested document to the franchisor in the database"""
    
    import asyncpg
    
    try:
        # Connect to database
        conn = await asyncpg.connect(
            host="localhost",
            port=5432,
            user="postgres",
            password="root",
            database="growthhive"
        )
        
        try:
            # Update the document to link it to the franchisor
            await conn.execute("""
                UPDATE documents 
                SET franchisor_id = $1
                WHERE id = $2
            """, franchisor_id, document_id)
            
            print(f"✅ Linked document {document_id} to franchisor {franchisor_id}")
            
        finally:
            await conn.close()
            
    except Exception as e:
        print(f"❌ Error linking document to franchisor: {str(e)}")

async def verify_ingestion():
    """Verify that the document was ingested correctly"""
    
    import asyncpg
    
    try:
        # Connect to database
        conn = await asyncpg.connect(
            host="localhost",
            port=5432,
            user="postgres",
            password="root",
            database="growthhive"
        )
        
        try:
            # Check documents table
            documents = await conn.fetch("""
                SELECT id, name, franchisor_id, processing_status
                FROM documents 
                WHERE name LIKE '%Coochie%' OR name LIKE '%Information%'
                ORDER BY created_at DESC
                LIMIT 5
            """)
            
            print(f"\n📋 Found {len(documents)} documents:")
            for doc in documents:
                print(f"   - {doc['name']} (ID: {doc['id'][:8]}..., Status: {doc['processing_status']})")
            
            # Check document chunks
            chunks = await conn.fetch("""
                SELECT COUNT(*) as count, d.name
                FROM document_chunks dc
                JOIN documents d ON dc.document_id = d.id
                WHERE d.name LIKE '%Coochie%' OR d.name LIKE '%Information%'
                GROUP BY d.name
            """)
            
            print(f"\n📊 Document chunks:")
            for chunk in chunks:
                print(f"   - {chunk['name']}: {chunk['count']} chunks")
                
        finally:
            await conn.close()
            
    except Exception as e:
        print(f"❌ Error verifying ingestion: {str(e)}")

if __name__ == "__main__":
    print("🚀 Starting Coochie Hydrogreen PDF ingestion...")
    print("=" * 60)
    
    # Run the ingestion
    success = asyncio.run(ingest_coochie_pdf())
    
    if success:
        print("\n🔍 Verifying ingestion...")
        asyncio.run(verify_ingestion())
        
        print("\n✅ Ingestion completed successfully!")
        print("🎯 The PDF content is now available for vector search!")
        print("💡 You can now ask questions about the actual document content.")
    else:
        print("\n❌ Ingestion failed!")
        
    print("=" * 60)
