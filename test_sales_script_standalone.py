#!/usr/bin/env python3
"""
Standalone Sales Script System Test

Test the dynamic sales script system without FastAPI dependencies.
"""

import os
import json
import uuid
from datetime import datetime
from jinja2 import Template, Environment, BaseLoader
import sqlalchemy as sa
from sqlalchemy import create_engine, text

def get_database_url():
    """Get database URL from environment"""
    db_url = os.getenv('DATABASE_URL')
    if db_url and 'asyncpg' in db_url:
        db_url = db_url.replace('postgresql+asyncpg://', 'postgresql://')
    return db_url or "postgresql://postgres:root@localhost:5432/growthhive"

class StandaloneSalesScriptTester:
    """Standalone tester for sales script system"""
    
    def __init__(self):
        self.engine = create_engine(get_database_url())
        self.jinja_env = Environment(loader=BaseLoader())
    
    def get_time_based_greeting(self):
        """Generate time-based greeting"""
        current_hour = datetime.now().hour
        
        if current_hour < 12:
            return "Good morning"
        elif current_hour < 17:
            return "Good afternoon"
        else:
            return "Good evening"
    
    def render_script(self, script_body: str, context: dict) -> str:
        """Render script with Jinja2"""
        
        # Add automatic greeting if not provided
        if "greeting" not in context:
            context["greeting"] = self.get_time_based_greeting()
        
        template = Template(script_body)
        return template.render(**context)
    
    def test_script_rendering(self):
        """Test basic script rendering"""
        
        print("🎨 Testing Script Rendering")
        print("-" * 40)
        
        # Test script template
        script_template = '''Hi, this is {{name_of_cust_rep}}, calling you on behalf of the "{{company_name}}". 

You inquired about this opportunity. I want to give you information about this. 

Is this a good time to chat?'''
        
        context = {
            "name_of_cust_rep": "John Smith",
            "company_name": "Coffee Masters Australia",
            "lead_first_name": "Sarah"
        }
        
        print(f"📝 Template: {script_template[:50]}...")
        print(f"🎯 Context: {context}")
        
        rendered = self.render_script(script_template, context)
        print(f"\n✨ Rendered Script:")
        print(rendered)
        
        return True
    
    def test_database_scripts(self):
        """Test scripts from database"""
        
        print("\n🗄️  Testing Database Scripts")
        print("-" * 40)
        
        try:
            with self.engine.connect() as conn:
                # Check if sales_scripts table exists
                result = conn.execute(text("""
                    SELECT COUNT(*) FROM information_schema.tables 
                    WHERE table_name = 'sales_scripts'
                """))
                
                if result.fetchone()[0] == 0:
                    print("❌ sales_scripts table not found")
                    return False
                
                # Get scripts
                result = conn.execute(text("""
                    SELECT ss.script_title, ss.script_body, ss.script_type, f.name as franchisor_name
                    FROM sales_scripts ss
                    JOIN franchisors f ON ss.franchisor_id = f.id
                    WHERE ss.is_active = true
                    LIMIT 3
                """))
                
                scripts = result.fetchall()
                
                if not scripts:
                    print("❌ No active scripts found")
                    return False
                
                print(f"📝 Found {len(scripts)} scripts:")
                
                for script in scripts:
                    title, body, script_type, franchisor = script
                    print(f"\n   📋 {title} ({script_type})")
                    print(f"      Franchisor: {franchisor}")
                    print(f"      Body: {body[:60]}...")
                    
                    # Test rendering
                    context = {
                        "name_of_cust_rep": "Jane Doe",
                        "company_name": franchisor,
                        "lead_first_name": "Michael"
                    }
                    
                    try:
                        rendered = self.render_script(body, context)
                        print(f"      ✅ Rendered: {rendered[:60]}...")
                    except Exception as e:
                        print(f"      ❌ Render error: {e}")
                
                return True
                
        except Exception as e:
            print(f"❌ Database error: {e}")
            return False
    
    def test_conversation_tables(self):
        """Test conversation state tables"""
        
        print("\n💬 Testing Conversation Tables")
        print("-" * 40)
        
        try:
            with self.engine.connect() as conn:
                # Check tables exist
                tables = ['conversation_states', 'conversation_messages']
                
                for table in tables:
                    result = conn.execute(text(f"""
                        SELECT COUNT(*) FROM information_schema.tables 
                        WHERE table_name = '{table}'
                    """))
                    
                    if result.fetchone()[0] == 0:
                        print(f"❌ {table} table not found")
                        return False
                    
                    # Count records
                    result = conn.execute(text(f"SELECT COUNT(*) FROM {table}"))
                    count = result.fetchone()[0]
                    print(f"📊 {table}: {count} records")
                
                return True
                
        except Exception as e:
            print(f"❌ Table check error: {e}")
            return False
    
    def test_webhook_simulation(self):
        """Test webhook payload simulation"""
        
        print("\n📡 Testing Webhook Simulation")
        print("-" * 40)
        
        # Sample webhook payload
        webhook_payload = {
            "event_type": "SMS_INBOUND",
            "message_id": f"test_{int(datetime.now().timestamp())}",
            "from_number": "+61-***********",
            "to_number": "+61-400-COMPANY",
            "message_text": "Yes, this is a good time to chat",
            "timestamp": datetime.utcnow().isoformat()
        }
        
        print(f"📨 Webhook payload:")
        for key, value in webhook_payload.items():
            print(f"   {key}: {value}")
        
        # Simulate message processing
        message = webhook_payload["message_text"]
        
        # Simple intent analysis
        positive_indicators = ["yes", "sure", "okay", "good time", "great", "absolutely"]
        is_positive = any(indicator in message.lower() for indicator in positive_indicators)
        
        print(f"\n🔍 Message Analysis:")
        print(f"   Message: \"{message}\"")
        print(f"   Positive response: {is_positive}")
        
        # Generate appropriate response
        if is_positive:
            response_script = '''Great! I'd like to ask you a few quick questions to better understand your background and interests. This will help me provide you with the most relevant information about the {{company_name}} opportunity.

Are you ready to get started?'''
        else:
            response_script = '''I understand. When would be a better time to reach you? I have some exciting information about {{company_name}} that I think you'd be interested in.'''
        
        context = {"company_name": "Coffee Masters Australia"}
        response = self.render_script(response_script, context)
        
        print(f"\n🤖 Generated Response:")
        print(response)
        
        return True
    
    def test_complete_conversation_flow(self):
        """Test complete conversation flow simulation"""
        
        print("\n🔄 Testing Complete Conversation Flow")
        print("-" * 40)
        
        # Conversation stages and scripts
        conversation_flow = [
            {
                "stage": "greeting",
                "script": '''{{greeting}} {{lead_first_name}}, this is {{name_of_cust_rep}}, calling you on behalf of {{company_name}}. 

You inquired about this opportunity. I want to give you information about this. 

Is this a good time to chat?''',
                "expected_response": "Yes, this is a good time"
            },
            {
                "stage": "qualification_intro",
                "script": '''Great! I'd like to ask you a few quick questions to better understand your background and interests. This will help me provide you with the most relevant information about the {{company_name}} opportunity.

Are you ready to get started?''',
                "expected_response": "Yes, I'm ready"
            },
            {
                "stage": "qualification_question",
                "script": "What is your available investment budget for this franchise opportunity?",
                "expected_response": "$150,000"
            },
            {
                "stage": "qa_intro",
                "script": '''I'd be happy to answer any questions you have about {{company_name}}. 

I have detailed information about our franchise model, investment requirements, training programs, and ongoing support.

What would you like to know more about?''',
                "expected_response": "What kind of training do you provide?"
            },
            {
                "stage": "scheduling",
                "script": '''Based on our conversation, you seem like a great fit for the {{company_name}} opportunity! 

I'd like to schedule a more detailed discussion with our franchise development team. This will give you a chance to learn more about the investment, training, and support we provide.

Would you prefer a call this week or next week?''',
                "expected_response": "This week would be good"
            }
        ]
        
        context = {
            "name_of_cust_rep": "Sarah Wilson",
            "company_name": "Coffee Masters Australia",
            "lead_first_name": "Alex"
        }
        
        print(f"👤 Lead: {context['lead_first_name']}")
        print(f"👨‍💼 Rep: {context['name_of_cust_rep']}")
        print(f"🏢 Company: {context['company_name']}")
        
        for i, step in enumerate(conversation_flow, 1):
            print(f"\n📍 Stage {i}: {step['stage']}")
            
            # Render script
            rendered = self.render_script(step['script'], context)
            print(f"🤖 Agent: {rendered}")
            
            # Simulate lead response
            print(f"👤 Lead: {step['expected_response']}")
        
        print(f"\n✅ Complete conversation flow simulated successfully!")
        return True
    
    def run_all_tests(self):
        """Run all tests"""
        
        print("🚀 STANDALONE SALES SCRIPT SYSTEM TEST")
        print("=" * 70)
        
        tests = [
            ("Script Rendering", self.test_script_rendering),
            ("Database Scripts", self.test_database_scripts),
            ("Conversation Tables", self.test_conversation_tables),
            ("Webhook Simulation", self.test_webhook_simulation),
            ("Complete Conversation Flow", self.test_complete_conversation_flow)
        ]
        
        results = []
        
        for test_name, test_func in tests:
            try:
                result = test_func()
                results.append((test_name, result))
                
                if result:
                    print(f"\n✅ {test_name} PASSED")
                else:
                    print(f"\n❌ {test_name} FAILED")
                    
            except Exception as e:
                print(f"\n❌ {test_name} ERROR: {e}")
                results.append((test_name, False))
        
        # Summary
        print(f"\n🎉 TEST SUMMARY")
        print("=" * 40)
        
        passed = sum(1 for _, result in results if result)
        total = len(results)
        
        print(f"Tests passed: {passed}/{total}")
        
        for test_name, result in results:
            status = "✅" if result else "❌"
            print(f"   {status} {test_name}")
        
        if passed == total:
            print(f"\n🎯 ALL TESTS PASSED!")
            print(f"\n✅ System Ready:")
            print(f"   • Dynamic sales script rendering ✅")
            print(f"   • Database integration ✅")
            print(f"   • Conversation state management ✅")
            print(f"   • SMS webhook simulation ✅")
            print(f"   • Complete conversation flow ✅")
            
            print(f"\n📱 Ready for Production:")
            print(f"   • Kudosity SMS_INBOUND webhook")
            print(f"   • AI agent conversation management")
            print(f"   • Lead qualification integration")
            print(f"   • Appointment scheduling workflow")
        else:
            print(f"\n⚠️  Some tests failed. Review errors above.")
        
        return passed == total

def main():
    """Main function"""
    
    try:
        tester = StandaloneSalesScriptTester()
        success = tester.run_all_tests()
        
        if success:
            print(f"\n🎉 SALES SCRIPT SYSTEM READY!")
        else:
            print(f"\n⚠️  SYSTEM NEEDS ATTENTION")
            
    except Exception as e:
        print(f"❌ Testing failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
