"""
Advanced RAG System (2024-2025)
Implements latest RAG techniques including multi-query, self-RAG, and adaptive retrieval
"""

import asyncio
import time
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import structlog
import openai
from .hybrid_search_engine import HybridSearchEngine, SearchResult
from .advanced_reranker import AdvancedReranker
from .latest_embedding_service import LatestEmbeddingService

logger = structlog.get_logger()


class RAGStrategy(Enum):
    """Advanced RAG strategies"""
    BASIC = "basic"  # Traditional RAG
    MULTI_QUERY = "multi_query"  # Generate multiple queries
    SELF_RAG = "self_rag"  # Self-reflective RAG
    ADAPTIVE = "adaptive"  # Adaptive retrieval
    FUSION = "fusion"  # Fusion of multiple approaches


@dataclass
class RAGConfig:
    """Configuration for advanced RAG system"""
    strategy: RAGStrategy = RAGStrategy.FUSION
    max_context_length: int = 8000
    min_context_length: int = 1000
    retrieval_top_k: int = 20
    final_top_k: int = 5
    confidence_threshold: float = 0.7
    enable_query_expansion: bool = True
    enable_context_compression: bool = True
    enable_answer_verification: bool = True
    temperature: float = 0.1
    max_tokens: int = 1000


@dataclass
class RAGResponse:
    """Enhanced RAG response"""
    answer: str
    confidence_score: float
    sources: List[SearchResult]
    strategy_used: str
    processing_time: float
    context_length: int
    query_expansions: List[str] = None
    verification_result: Optional[Dict[str, Any]] = None
    metadata: Dict[str, Any] = None


class AdvancedRAGSystem:
    """Advanced RAG system with latest techniques"""
    
    def __init__(self,
                 openai_api_key: str,
                 search_engine: HybridSearchEngine,
                 reranker: AdvancedReranker,
                 embedding_service: LatestEmbeddingService,
                 config: RAGConfig = None):
        
        self.openai_client = openai.OpenAI(api_key=openai_api_key)
        self.search_engine = search_engine
        self.reranker = reranker
        self.embedding_service = embedding_service
        self.config = config or RAGConfig()
        
        logger.info("Advanced RAG system initialized",
                   strategy=self.config.strategy.value,
                   max_context=self.config.max_context_length)
    
    async def answer_question(self, 
                            question: str,
                            context: Optional[str] = None,
                            strategy: Optional[RAGStrategy] = None) -> RAGResponse:
        """
        Answer question using advanced RAG techniques
        
        Args:
            question: User question
            context: Additional context
            strategy: Specific strategy to use
            
        Returns:
            Enhanced RAG response
        """
        start_time = time.time()
        strategy = strategy or self.config.strategy
        
        try:
            if strategy == RAGStrategy.BASIC:
                response = await self._basic_rag(question, context)
            elif strategy == RAGStrategy.MULTI_QUERY:
                response = await self._multi_query_rag(question, context)
            elif strategy == RAGStrategy.SELF_RAG:
                response = await self._self_rag(question, context)
            elif strategy == RAGStrategy.ADAPTIVE:
                response = await self._adaptive_rag(question, context)
            else:  # FUSION
                response = await self._fusion_rag(question, context)
            
            response.processing_time = time.time() - start_time
            response.strategy_used = strategy.value
            
            logger.info("RAG question answered",
                       strategy=strategy.value,
                       confidence=response.confidence_score,
                       sources_count=len(response.sources),
                       processing_time=response.processing_time)
            
            return response
            
        except Exception as e:
            logger.error(f"RAG processing failed: {e}")
            return RAGResponse(
                answer="I apologize, but I encountered an error while processing your question. Please try again.",
                confidence_score=0.0,
                sources=[],
                strategy_used=strategy.value,
                processing_time=time.time() - start_time,
                context_length=0
            )
    
    async def _basic_rag(self, question: str, context: Optional[str] = None) -> RAGResponse:
        """Basic RAG implementation"""
        # Retrieve relevant documents
        search_results = await self.search_engine.search(
            query=question,
            top_k=self.config.retrieval_top_k
        )
        
        # Rerank results
        if search_results:
            reranked_results = await self.reranker.rerank(
                query=question,
                documents=[r.text for r in search_results]
            )
            
            # Map reranked results back to search results
            search_results = [search_results[r.index] for r in reranked_results[:self.config.final_top_k]]
        
        # Generate answer
        answer, confidence = await self._generate_answer(question, search_results, context)
        
        return RAGResponse(
            answer=answer,
            confidence_score=confidence,
            sources=search_results,
            strategy_used="basic",
            processing_time=0.0,
            context_length=sum(len(r.text) for r in search_results)
        )
    
    async def _multi_query_rag(self, question: str, context: Optional[str] = None) -> RAGResponse:
        """Multi-query RAG with query expansion"""
        # Generate multiple query variations
        query_variations = await self._generate_query_variations(question)
        
        # Search with all query variations
        all_results = []
        for query in query_variations:
            results = await self.search_engine.search(
                query=query,
                top_k=self.config.retrieval_top_k // len(query_variations)
            )
            all_results.extend(results)
        
        # Remove duplicates and rerank
        unique_results = self._deduplicate_results(all_results)
        
        if unique_results:
            reranked_results = await self.reranker.rerank(
                query=question,
                documents=[r.text for r in unique_results]
            )
            
            final_results = [unique_results[r.index] for r in reranked_results[:self.config.final_top_k]]
        else:
            final_results = []
        
        # Generate answer
        answer, confidence = await self._generate_answer(question, final_results, context)
        
        return RAGResponse(
            answer=answer,
            confidence_score=confidence,
            sources=final_results,
            strategy_used="multi_query",
            processing_time=0.0,
            context_length=sum(len(r.text) for r in final_results),
            query_expansions=query_variations
        )
    
    async def _self_rag(self, question: str, context: Optional[str] = None) -> RAGResponse:
        """Self-reflective RAG with answer verification"""
        # Initial retrieval and answer generation
        initial_response = await self._basic_rag(question, context)
        
        # Self-reflection: verify answer quality
        verification = await self._verify_answer(question, initial_response.answer, initial_response.sources)
        
        # If verification fails, try alternative approach
        if verification['confidence'] < self.config.confidence_threshold:
            logger.info("Initial answer failed verification, trying alternative approach")
            
            # Generate alternative queries
            alt_queries = await self._generate_alternative_queries(question, initial_response.answer)
            
            # Search with alternative queries
            alt_results = []
            for query in alt_queries:
                results = await self.search_engine.search(query=query, top_k=10)
                alt_results.extend(results)
            
            # Combine with original results
            combined_results = self._deduplicate_results(initial_response.sources + alt_results)
            
            # Generate improved answer
            improved_answer, improved_confidence = await self._generate_answer(
                question, combined_results[:self.config.final_top_k], context
            )
            
            return RAGResponse(
                answer=improved_answer,
                confidence_score=improved_confidence,
                sources=combined_results[:self.config.final_top_k],
                strategy_used="self_rag",
                processing_time=0.0,
                context_length=sum(len(r.text) for r in combined_results[:self.config.final_top_k]),
                verification_result=verification
            )
        
        initial_response.verification_result = verification
        return initial_response
    
    async def _adaptive_rag(self, question: str, context: Optional[str] = None) -> RAGResponse:
        """Adaptive RAG that chooses strategy based on question type"""
        # Analyze question to determine best strategy
        question_analysis = await self._analyze_question(question)
        
        # Choose strategy based on analysis
        if question_analysis['complexity'] > 0.7:
            return await self._multi_query_rag(question, context)
        elif question_analysis['requires_verification']:
            return await self._self_rag(question, context)
        else:
            return await self._basic_rag(question, context)
    
    async def _fusion_rag(self, question: str, context: Optional[str] = None) -> RAGResponse:
        """Fusion RAG combining multiple approaches"""
        # Run multiple strategies in parallel
        strategies = [
            self._basic_rag(question, context),
            self._multi_query_rag(question, context)
        ]
        
        responses = await asyncio.gather(*strategies, return_exceptions=True)
        
        # Filter successful responses
        valid_responses = [r for r in responses if isinstance(r, RAGResponse)]
        
        if not valid_responses:
            return await self._basic_rag(question, context)
        
        # Combine and rank responses
        best_response = max(valid_responses, key=lambda r: r.confidence_score)
        
        # Combine sources from all responses
        all_sources = []
        for response in valid_responses:
            all_sources.extend(response.sources)
        
        unique_sources = self._deduplicate_results(all_sources)
        
        # Generate final answer with combined context
        final_answer, final_confidence = await self._generate_answer(
            question, unique_sources[:self.config.final_top_k], context
        )
        
        return RAGResponse(
            answer=final_answer,
            confidence_score=final_confidence,
            sources=unique_sources[:self.config.final_top_k],
            strategy_used="fusion",
            processing_time=0.0,
            context_length=sum(len(r.text) for r in unique_sources[:self.config.final_top_k]),
            metadata={'fusion_responses': len(valid_responses)}
        )
    
    async def _generate_answer(self, 
                             question: str, 
                             sources: List[SearchResult],
                             context: Optional[str] = None) -> Tuple[str, float]:
        """Generate answer from retrieved sources"""
        if not sources:
            return "I couldn't find relevant information to answer your question.", 0.0
        
        # Prepare context
        context_parts = []
        if context:
            context_parts.append(f"Additional context: {context}")
        
        for i, source in enumerate(sources):
            context_parts.append(f"Source {i+1}: {source.text}")
        
        full_context = "\n\n".join(context_parts)
        
        # Compress context if too long
        if len(full_context) > self.config.max_context_length:
            full_context = await self._compress_context(full_context, question)
        
        # Generate answer
        prompt = f"""
        Based on the following context, please answer the question accurately and comprehensively.
        
        Question: {question}
        
        Context:
        {full_context}
        
        Please provide a detailed answer based on the context. If the context doesn't contain enough information to answer the question completely, please state what information is missing.
        """
        
        response = await asyncio.to_thread(
            self.openai_client.chat.completions.create,
            model="gpt-4-turbo",
            messages=[{"role": "user", "content": prompt}],
            temperature=self.config.temperature,
            max_tokens=self.config.max_tokens
        )
        
        answer = response.choices[0].message.content
        
        # Calculate confidence based on context relevance and answer quality
        confidence = await self._calculate_confidence(question, answer, sources)
        
        return answer, confidence
