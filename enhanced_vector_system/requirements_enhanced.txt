# Enhanced Vector System Requirements (2024-2025)
# Latest embedding models, hybrid search, and advanced RAG capabilities

# Core Dependencies (already in main requirements.txt)
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
sqlalchemy>=2.0.0
asyncpg>=0.29.0
psycopg2-binary>=2.9.0
pgvector>=0.2.4
redis>=5.0.0
openai>=1.3.0
numpy>=1.24.0
structlog>=23.2.0

# Latest Embedding Models
cohere>=4.40.0  # For Cohere Embed v3 and Rerank-3
sentence-transformers>=2.2.2  # For BGE and other models
transformers>=4.35.0  # For latest transformer models

# Advanced Search and Retrieval
rank-bm25>=0.2.2  # For sparse vector search (BM25)
faiss-cpu>=1.7.4  # For FAISS vector search
scikit-learn>=1.3.0  # For similarity calculations

# NLP and Text Processing
spacy>=3.7.0  # For advanced text processing
tiktoken>=0.5.0  # For token counting
nltk>=3.8.1  # For additional NLP features

# Machine Learning and Optimization
torch>=2.1.0  # For neural models
torchvision>=0.16.0  # For vision models
accelerate>=0.24.0  # For model acceleration

# Vector Databases (Optional - choose based on needs)
# qdrant-client>=1.6.0  # For Qdrant vector database
# weaviate-client>=3.25.0  # For Weaviate vector database
# pinecone-client>=2.2.4  # For Pinecone vector database

# Performance and Monitoring
prometheus-client>=0.19.0  # For metrics
psutil>=5.9.0  # For system monitoring
memory-profiler>=0.61.0  # For memory profiling

# Development and Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-benchmark>=4.0.0  # For performance testing
black>=23.0.0
ruff>=0.1.0
mypy>=1.7.0

# Optional: GPU acceleration (uncomment if using GPU)
# torch>=2.1.0+cu118  # CUDA version
# faiss-gpu>=1.7.4  # GPU version of FAISS
