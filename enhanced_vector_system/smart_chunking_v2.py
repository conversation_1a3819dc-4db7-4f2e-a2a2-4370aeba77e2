"""
Advanced Smart Chunking System v2 (2024-2025)
Implements latest chunking strategies including semantic chunking, hierarchical chunking, and context-aware splitting
"""

import re
import time
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import structlog
import tiktoken
import spacy
from sentence_transformers import SentenceTransformer
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity

logger = structlog.get_logger()


class ChunkingStrategy(Enum):
    """Advanced chunking strategies"""
    FIXED_SIZE = "fixed_size"  # Traditional fixed-size chunking
    SEMANTIC = "semantic"  # Semantic similarity-based chunking
    HIERARCHICAL = "hierarchical"  # Document structure-aware chunking
    ADAPTIVE = "adaptive"  # Adaptive based on content type
    HYBRID = "hybrid"  # Combination of multiple strategies


@dataclass
class ChunkingConfig:
    """Configuration for advanced chunking"""
    strategy: ChunkingStrategy = ChunkingStrategy.HYBRID
    target_chunk_size: int = 512  # Target tokens per chunk
    min_chunk_size: int = 100
    max_chunk_size: int = 1000
    overlap_ratio: float = 0.1  # 10% overlap
    semantic_threshold: float = 0.7  # Similarity threshold for semantic chunking
    preserve_sentences: bool = True
    preserve_paragraphs: bool = True
    respect_structure: bool = True  # Respect document structure (headers, lists, etc.)
    enable_metadata: bool = True


@dataclass
class ChunkMetadata:
    """Enhanced metadata for chunks"""
    chunk_index: int
    total_chunks: int
    section_title: Optional[str] = None
    section_type: Optional[str] = None  # paragraph, list, table, etc.
    hierarchy_level: int = 0
    semantic_cluster: Optional[int] = None
    complexity_score: float = 0.0
    token_count: int = 0
    char_count: int = 0
    sentence_count: int = 0


@dataclass
class SmartChunk:
    """Enhanced chunk with rich metadata"""
    id: str
    text: str
    metadata: ChunkMetadata
    embedding: Optional[List[float]] = None
    parent_chunk_id: Optional[str] = None
    child_chunk_ids: Optional[List[str]] = None


class SmartChunkerV2:
    """Advanced chunking system with multiple strategies"""
    
    def __init__(self, config: ChunkingConfig = None):
        self.config = config or ChunkingConfig()
        
        # Initialize tokenizer
        self.tokenizer = tiktoken.get_encoding("cl100k_base")
        
        # Initialize NLP models (lazy loading)
        self.nlp = None
        self.sentence_model = None
        
        logger.info("Smart chunker v2 initialized",
                   strategy=self.config.strategy.value,
                   target_size=self.config.target_chunk_size)
    
    def _load_nlp_models(self):
        """Lazy load NLP models"""
        if self.nlp is None:
            try:
                self.nlp = spacy.load("en_core_web_sm")
            except OSError:
                logger.warning("spaCy model not found, using basic sentence splitting")
                self.nlp = None
        
        if self.sentence_model is None and self.config.strategy in [ChunkingStrategy.SEMANTIC, ChunkingStrategy.HYBRID]:
            try:
                self.sentence_model = SentenceTransformer('all-MiniLM-L6-v2')
            except Exception as e:
                logger.warning(f"Failed to load sentence transformer: {e}")
                self.sentence_model = None
    
    async def chunk_document(self, 
                           text: str, 
                           document_type: str = "general",
                           document_metadata: Dict[str, Any] = None) -> List[SmartChunk]:
        """
        Chunk document using advanced strategies
        
        Args:
            text: Document text to chunk
            document_type: Type of document (pdf, docx, etc.)
            document_metadata: Additional document metadata
            
        Returns:
            List of smart chunks with enhanced metadata
        """
        start_time = time.time()
        
        # Load NLP models if needed
        self._load_nlp_models()
        
        # Preprocess text
        preprocessed_text = self._preprocess_text(text)
        
        # Choose chunking strategy
        if self.config.strategy == ChunkingStrategy.FIXED_SIZE:
            chunks = await self._fixed_size_chunking(preprocessed_text)
        elif self.config.strategy == ChunkingStrategy.SEMANTIC:
            chunks = await self._semantic_chunking(preprocessed_text)
        elif self.config.strategy == ChunkingStrategy.HIERARCHICAL:
            chunks = await self._hierarchical_chunking(preprocessed_text)
        elif self.config.strategy == ChunkingStrategy.ADAPTIVE:
            chunks = await self._adaptive_chunking(preprocessed_text, document_type)
        else:  # HYBRID
            chunks = await self._hybrid_chunking(preprocessed_text, document_type)
        
        # Enhance chunks with metadata
        enhanced_chunks = self._enhance_chunks_metadata(chunks, document_metadata)
        
        processing_time = time.time() - start_time
        
        logger.info("Document chunking completed",
                   strategy=self.config.strategy.value,
                   original_length=len(text),
                   chunk_count=len(enhanced_chunks),
                   processing_time=processing_time)
        
        return enhanced_chunks
    
    async def _fixed_size_chunking(self, text: str) -> List[SmartChunk]:
        """Traditional fixed-size chunking with improvements"""
        chunks = []
        sentences = self._split_into_sentences(text)
        
        current_chunk = ""
        current_tokens = 0
        chunk_index = 0
        
        for sentence in sentences:
            sentence_tokens = len(self.tokenizer.encode(sentence))
            
            # Check if adding this sentence would exceed the limit
            if current_tokens + sentence_tokens > self.config.target_chunk_size and current_chunk:
                # Create chunk
                chunk = SmartChunk(
                    id=f"chunk_{chunk_index}",
                    text=current_chunk.strip(),
                    metadata=ChunkMetadata(
                        chunk_index=chunk_index,
                        total_chunks=0,  # Will be updated later
                        token_count=current_tokens,
                        char_count=len(current_chunk),
                        sentence_count=len(current_chunk.split('. '))
                    )
                )
                chunks.append(chunk)
                
                # Start new chunk with overlap
                overlap_size = int(self.config.target_chunk_size * self.config.overlap_ratio)
                if current_tokens > overlap_size:
                    # Keep last part for overlap
                    overlap_text = self._get_overlap_text(current_chunk, overlap_size)
                    current_chunk = overlap_text + " " + sentence
                    current_tokens = len(self.tokenizer.encode(current_chunk))
                else:
                    current_chunk = sentence
                    current_tokens = sentence_tokens
                
                chunk_index += 1
            else:
                current_chunk += " " + sentence if current_chunk else sentence
                current_tokens += sentence_tokens
        
        # Add final chunk
        if current_chunk:
            chunk = SmartChunk(
                id=f"chunk_{chunk_index}",
                text=current_chunk.strip(),
                metadata=ChunkMetadata(
                    chunk_index=chunk_index,
                    total_chunks=0,
                    token_count=current_tokens,
                    char_count=len(current_chunk),
                    sentence_count=len(current_chunk.split('. '))
                )
            )
            chunks.append(chunk)
        
        # Update total chunks count
        for chunk in chunks:
            chunk.metadata.total_chunks = len(chunks)
        
        return chunks
    
    async def _semantic_chunking(self, text: str) -> List[SmartChunk]:
        """Semantic similarity-based chunking"""
        if not self.sentence_model:
            logger.warning("Sentence model not available, falling back to fixed-size chunking")
            return await self._fixed_size_chunking(text)
        
        sentences = self._split_into_sentences(text)
        
        # Generate embeddings for sentences
        sentence_embeddings = self.sentence_model.encode(sentences)
        
        # Group sentences by semantic similarity
        chunks = []
        current_group = [0]  # Start with first sentence
        chunk_index = 0
        
        for i in range(1, len(sentences)):
            # Calculate similarity with current group
            group_embedding = np.mean([sentence_embeddings[j] for j in current_group], axis=0)
            similarity = cosine_similarity([sentence_embeddings[i]], [group_embedding])[0][0]
            
            # Check if sentence belongs to current group
            current_tokens = sum(len(self.tokenizer.encode(sentences[j])) for j in current_group)
            sentence_tokens = len(self.tokenizer.encode(sentences[i]))
            
            if (similarity >= self.config.semantic_threshold and 
                current_tokens + sentence_tokens <= self.config.max_chunk_size):
                current_group.append(i)
            else:
                # Create chunk from current group
                chunk_text = " ".join(sentences[j] for j in current_group)
                chunk = SmartChunk(
                    id=f"semantic_chunk_{chunk_index}",
                    text=chunk_text,
                    metadata=ChunkMetadata(
                        chunk_index=chunk_index,
                        total_chunks=0,
                        token_count=current_tokens,
                        char_count=len(chunk_text),
                        sentence_count=len(current_group),
                        semantic_cluster=chunk_index
                    )
                )
                chunks.append(chunk)
                
                # Start new group
                current_group = [i]
                chunk_index += 1
        
        # Add final chunk
        if current_group:
            chunk_text = " ".join(sentences[j] for j in current_group)
            current_tokens = sum(len(self.tokenizer.encode(sentences[j])) for j in current_group)
            chunk = SmartChunk(
                id=f"semantic_chunk_{chunk_index}",
                text=chunk_text,
                metadata=ChunkMetadata(
                    chunk_index=chunk_index,
                    total_chunks=0,
                    token_count=current_tokens,
                    char_count=len(chunk_text),
                    sentence_count=len(current_group),
                    semantic_cluster=chunk_index
                )
            )
            chunks.append(chunk)
        
        # Update total chunks count
        for chunk in chunks:
            chunk.metadata.total_chunks = len(chunks)
        
        return chunks
    
    async def _hierarchical_chunking(self, text: str) -> List[SmartChunk]:
        """Structure-aware hierarchical chunking"""
        # Detect document structure
        sections = self._detect_document_structure(text)
        
        chunks = []
        chunk_index = 0
        
        for section in sections:
            section_chunks = await self._chunk_section(
                section['text'], 
                section['title'], 
                section['type'],
                section['level'],
                chunk_index
            )
            chunks.extend(section_chunks)
            chunk_index += len(section_chunks)
        
        # Update total chunks count
        for chunk in chunks:
            chunk.metadata.total_chunks = len(chunks)
        
        return chunks
    
    async def _adaptive_chunking(self, text: str, document_type: str) -> List[SmartChunk]:
        """Adaptive chunking based on document type and content"""
        # Analyze content complexity
        complexity = self._analyze_content_complexity(text)
        
        # Adjust chunking parameters based on complexity and document type
        adjusted_config = self._adjust_config_for_content(complexity, document_type)
        
        # Use appropriate strategy based on analysis
        if complexity > 0.7:  # High complexity
            return await self._semantic_chunking(text)
        elif document_type in ['pdf', 'docx']:  # Structured documents
            return await self._hierarchical_chunking(text)
        else:  # Default to fixed-size
            return await self._fixed_size_chunking(text)
    
    async def _hybrid_chunking(self, text: str, document_type: str) -> List[SmartChunk]:
        """Hybrid approach combining multiple strategies"""
        # First, detect structure
        sections = self._detect_document_structure(text)
        
        chunks = []
        chunk_index = 0
        
        for section in sections:
            # Choose strategy based on section characteristics
            section_complexity = self._analyze_content_complexity(section['text'])
            
            if section_complexity > 0.6 and self.sentence_model:
                # Use semantic chunking for complex sections
                section_chunks = await self._semantic_chunking(section['text'])
            else:
                # Use fixed-size chunking for simple sections
                section_chunks = await self._fixed_size_chunking(section['text'])
            
            # Update metadata with section information
            for chunk in section_chunks:
                chunk.metadata.section_title = section['title']
                chunk.metadata.section_type = section['type']
                chunk.metadata.hierarchy_level = section['level']
                chunk.metadata.chunk_index = chunk_index
                chunk.id = f"hybrid_chunk_{chunk_index}"
                chunk_index += 1
            
            chunks.extend(section_chunks)
        
        # Update total chunks count
        for chunk in chunks:
            chunk.metadata.total_chunks = len(chunks)

        return chunks

    def _preprocess_text(self, text: str) -> str:
        """Preprocess text for chunking"""
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)

        # Normalize line breaks
        text = re.sub(r'\n+', '\n', text)

        return text.strip()

    def _split_into_sentences(self, text: str) -> List[str]:
        """Split text into sentences using NLP or regex"""
        if self.nlp:
            doc = self.nlp(text)
            return [sent.text.strip() for sent in doc.sents if sent.text.strip()]
        else:
            # Fallback to regex-based sentence splitting
            sentences = re.split(r'(?<=[.!?])\s+', text)
            return [s.strip() for s in sentences if s.strip()]

    def _detect_document_structure(self, text: str) -> List[Dict[str, Any]]:
        """Detect document structure (headers, paragraphs, lists, etc.)"""
        sections = []

        # Split by double newlines (paragraphs)
        paragraphs = text.split('\n\n')

        for i, paragraph in enumerate(paragraphs):
            paragraph = paragraph.strip()
            if not paragraph:
                continue

            # Detect section type and level
            section_type = "paragraph"
            level = 0
            title = None

            # Check for headers (simple heuristics)
            if len(paragraph) < 100 and paragraph.isupper():
                section_type = "header"
                level = 1
                title = paragraph
            elif paragraph.startswith('#'):
                section_type = "header"
                level = len(paragraph) - len(paragraph.lstrip('#'))
                title = paragraph.lstrip('# ').strip()
            elif re.match(r'^\d+\.', paragraph):
                section_type = "numbered_list"
                level = 1
            elif paragraph.startswith('•') or paragraph.startswith('-'):
                section_type = "bullet_list"
                level = 1

            sections.append({
                'text': paragraph,
                'type': section_type,
                'level': level,
                'title': title,
                'index': i
            })

        return sections
