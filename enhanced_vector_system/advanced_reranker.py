"""
Advanced Reranking System (2024-2025)
Supports multiple reranking models including Cohere Rerank-3, <PERSON><PERSON> reranker, and cross-encoders
"""

import asyncio
import time
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import structlog
import cohere
import openai
from sentence_transformers import CrossEncoder
import torch

logger = structlog.get_logger()


class RerankerModel(Enum):
    """Latest reranking models for 2024-2025"""
    # Cohere Models (Latest)
    COHERE_RERANK_3 = "rerank-3"  # Latest Cohere reranker
    COHERE_RERANK_ENGLISH_V3 = "rerank-english-v3.0"
    COHERE_RERANK_MULTILINGUAL_V3 = "rerank-multilingual-v3.0"
    
    # BGE Rerankers (Open Source)
    BGE_RERANKER_V2_M3 = "BAAI/bge-reranker-v2-m3"
    BGE_RERANKER_LARGE = "BAAI/bge-reranker-large"
    
    # Cross-Encoders
    MS_MARCO_MINILM = "cross-encoder/ms-marco-MiniLM-L-6-v2"
    MS_MARCO_ELECTRA = "cross-encoder/ms-marco-electra-base"
    
    # OpenAI GPT-based reranking
    OPENAI_GPT4_RERANK = "gpt-4-turbo"


@dataclass
class RerankConfig:
    """Configuration for reranking"""
    primary_model: RerankerModel = RerankerModel.COHERE_RERANK_3
    fallback_model: RerankerModel = RerankerModel.BGE_RERANKER_V2_M3
    max_candidates: int = 50
    batch_size: int = 10
    enable_caching: bool = True
    cache_ttl: int = 1800  # 30 minutes
    score_threshold: float = 0.1


@dataclass
class RerankResult:
    """Result from reranking"""
    index: int
    score: float
    text: str
    relevance_explanation: Optional[str] = None


class AdvancedReranker:
    """Advanced reranking system with multiple models"""
    
    def __init__(self,
                 cohere_api_key: Optional[str] = None,
                 openai_api_key: Optional[str] = None,
                 config: RerankConfig = None):
        
        self.config = config or RerankConfig()
        
        # Initialize clients
        self.cohere_client = cohere.Client(cohere_api_key) if cohere_api_key else None
        self.openai_client = openai.OpenAI(api_key=openai_api_key) if openai_api_key else None
        
        # Initialize cross-encoders (lazy loading)
        self.cross_encoders = {}
        
        logger.info("Advanced reranker initialized",
                   primary_model=self.config.primary_model.value,
                   fallback_model=self.config.fallback_model.value)
    
    async def rerank(self, 
                    query: str, 
                    documents: List[str],
                    model: Optional[RerankerModel] = None,
                    return_explanations: bool = False) -> List[RerankResult]:
        """
        Rerank documents based on relevance to query
        
        Args:
            query: Search query
            documents: List of document texts to rerank
            model: Specific model to use
            return_explanations: Whether to include relevance explanations
            
        Returns:
            List of reranked results with scores
        """
        model = model or self.config.primary_model
        
        if not documents:
            return []
        
        start_time = time.time()
        
        try:
            if model.value.startswith("rerank"):
                results = await self._cohere_rerank(query, documents, model, return_explanations)
            elif model.value.startswith("BAAI/bge"):
                results = await self._bge_rerank(query, documents, model)
            elif model.value.startswith("cross-encoder"):
                results = await self._cross_encoder_rerank(query, documents, model)
            elif model.value.startswith("gpt"):
                results = await self._openai_rerank(query, documents, model, return_explanations)
            else:
                raise ValueError(f"Unsupported reranker model: {model.value}")
            
            processing_time = time.time() - start_time
            
            logger.info("Reranking completed",
                       model=model.value,
                       query_length=len(query),
                       document_count=len(documents),
                       processing_time=processing_time)
            
            return results
            
        except Exception as e:
            logger.warning(f"Primary reranker failed, trying fallback",
                          primary_model=model.value,
                          error=str(e))
            
            if model != self.config.fallback_model:
                return await self.rerank(query, documents, self.config.fallback_model, return_explanations)
            else:
                # Return original order with neutral scores
                return [
                    RerankResult(index=i, score=0.5, text=doc)
                    for i, doc in enumerate(documents)
                ]
    
    async def _cohere_rerank(self, 
                           query: str, 
                           documents: List[str],
                           model: RerankerModel,
                           return_explanations: bool = False) -> List[RerankResult]:
        """Rerank using Cohere models"""
        if not self.cohere_client:
            raise ValueError("Cohere client not initialized")
        
        # Process in batches if needed
        all_results = []
        
        for i in range(0, len(documents), self.config.batch_size):
            batch_docs = documents[i:i + self.config.batch_size]
            
            response = await asyncio.to_thread(
                self.cohere_client.rerank,
                query=query,
                documents=batch_docs,
                model=model.value,
                top_n=len(batch_docs),
                return_documents=True
            )
            
            for result in response.results:
                rerank_result = RerankResult(
                    index=i + result.index,
                    score=result.relevance_score,
                    text=result.document.text
                )
                
                # Add explanation if requested and available
                if return_explanations and hasattr(result, 'explanation'):
                    rerank_result.relevance_explanation = result.explanation
                
                all_results.append(rerank_result)
        
        # Sort by score
        all_results.sort(key=lambda x: x.score, reverse=True)
        return all_results
    
    async def _bge_rerank(self, 
                        query: str, 
                        documents: List[str],
                        model: RerankerModel) -> List[RerankResult]:
        """Rerank using BGE models"""
        model_name = model.value
        
        # Load model if not cached
        if model_name not in self.cross_encoders:
            self.cross_encoders[model_name] = CrossEncoder(model_name)
        
        cross_encoder = self.cross_encoders[model_name]
        
        # Prepare pairs for scoring
        pairs = [[query, doc] for doc in documents]
        
        # Get scores
        scores = await asyncio.to_thread(cross_encoder.predict, pairs)
        
        # Create results
        results = []
        for i, (doc, score) in enumerate(zip(documents, scores)):
            results.append(RerankResult(
                index=i,
                score=float(score),
                text=doc
            ))
        
        # Sort by score
        results.sort(key=lambda x: x.score, reverse=True)
        return results
    
    async def _cross_encoder_rerank(self, 
                                  query: str, 
                                  documents: List[str],
                                  model: RerankerModel) -> List[RerankResult]:
        """Rerank using cross-encoder models"""
        return await self._bge_rerank(query, documents, model)
    
    async def _openai_rerank(self, 
                           query: str, 
                           documents: List[str],
                           model: RerankerModel,
                           return_explanations: bool = False) -> List[RerankResult]:
        """Rerank using OpenAI GPT models"""
        if not self.openai_client:
            raise ValueError("OpenAI client not initialized")
        
        # Prepare prompt for reranking
        docs_text = "\n\n".join([f"Document {i+1}: {doc}" for i, doc in enumerate(documents)])
        
        prompt = f"""
        Given the query: "{query}"
        
        Please rank the following documents by relevance to the query. 
        Return a JSON array with objects containing 'index' (0-based), 'score' (0-1), and optionally 'explanation'.
        
        Documents:
        {docs_text}
        
        Response format:
        [
            {{"index": 0, "score": 0.95, "explanation": "Highly relevant because..."}},
            {{"index": 2, "score": 0.80, "explanation": "Relevant because..."}},
            ...
        ]
        """
        
        response = await asyncio.to_thread(
            self.openai_client.chat.completions.create,
            model=model.value,
            messages=[{"role": "user", "content": prompt}],
            temperature=0.1
        )
        
        try:
            import json
            rankings = json.loads(response.choices[0].message.content)
            
            results = []
            for ranking in rankings:
                index = ranking['index']
                if 0 <= index < len(documents):
                    result = RerankResult(
                        index=index,
                        score=ranking['score'],
                        text=documents[index]
                    )
                    
                    if return_explanations and 'explanation' in ranking:
                        result.relevance_explanation = ranking['explanation']
                    
                    results.append(result)
            
            return results
            
        except (json.JSONDecodeError, KeyError) as e:
            logger.error("Failed to parse OpenAI reranking response", error=str(e))
            # Return original order with neutral scores
            return [
                RerankResult(index=i, score=0.5, text=doc)
                for i, doc in enumerate(documents)
            ]
    
    async def compare_rerankers(self, 
                              query: str, 
                              documents: List[str]) -> Dict[str, List[RerankResult]]:
        """
        Compare different reranking models for the same query and documents
        Useful for evaluation and model selection
        """
        results = {}
        
        models_to_test = [
            RerankerModel.COHERE_RERANK_3,
            RerankerModel.BGE_RERANKER_V2_M3,
            RerankerModel.MS_MARCO_MINILM
        ]
        
        for model in models_to_test:
            try:
                start_time = time.time()
                rerank_results = await self.rerank(query, documents, model)
                processing_time = time.time() - start_time
                
                results[model.value] = {
                    "results": rerank_results,
                    "processing_time": processing_time,
                    "success": True
                }
            except Exception as e:
                results[model.value] = {
                    "error": str(e),
                    "success": False
                }
        
        return results
    
    async def batch_rerank(self, 
                         queries: List[str], 
                         documents_list: List[List[str]],
                         model: Optional[RerankerModel] = None) -> List[List[RerankResult]]:
        """
        Rerank multiple query-document pairs efficiently
        
        Args:
            queries: List of queries
            documents_list: List of document lists (one per query)
            model: Model to use
            
        Returns:
            List of reranked results for each query
        """
        if len(queries) != len(documents_list):
            raise ValueError("Number of queries must match number of document lists")
        
        # Process all queries concurrently
        tasks = []
        for query, documents in zip(queries, documents_list):
            task = self.rerank(query, documents, model)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle exceptions
        final_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Batch reranking failed for query {i}", error=str(result))
                # Return original order with neutral scores
                documents = documents_list[i]
                final_results.append([
                    RerankResult(index=j, score=0.5, text=doc)
                    for j, doc in enumerate(documents)
                ])
            else:
                final_results.append(result)
        
        return final_results

    def get_model_info(self, model: RerankerModel) -> Dict[str, Any]:
        """Get information about a specific reranking model"""
        model_info = {
            RerankerModel.COHERE_RERANK_3: {
                "provider": "Cohere",
                "type": "API-based",
                "max_documents": 1000,
                "languages": ["English", "Multilingual"],
                "cost": "Pay-per-use",
                "latency": "Low"
            },
            RerankerModel.BGE_RERANKER_V2_M3: {
                "provider": "BAAI",
                "type": "Self-hosted",
                "max_documents": "Unlimited",
                "languages": ["Multilingual"],
                "cost": "Free",
                "latency": "Medium"
            },
            RerankerModel.MS_MARCO_MINILM: {
                "provider": "Microsoft",
                "type": "Self-hosted",
                "max_documents": "Unlimited",
                "languages": ["English"],
                "cost": "Free",
                "latency": "Low"
            }
        }

        return model_info.get(model, {"provider": "Unknown", "type": "Unknown"})
