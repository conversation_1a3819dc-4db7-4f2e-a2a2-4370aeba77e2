"""
Advanced Hybrid Search Engine (2024-2025)
Combines dense vector search, sparse vector search (BM25), and reranking
"""

import asyncio
import time
from typing import List, Dict, Any, Optional, Tuple, Union
from dataclasses import dataclass
from enum import Enum
import numpy as np
import structlog
from rank_bm25 import BM25Okapi
import psycopg2
from psycopg2.extras import RealDictCursor
import json

logger = structlog.get_logger()


class SearchMode(Enum):
    """Search modes for hybrid search"""
    DENSE_ONLY = "dense_only"
    SPARSE_ONLY = "sparse_only"
    HYBRID = "hybrid"
    ADAPTIVE = "adaptive"  # Automatically choose best mode


@dataclass
class SearchResult:
    """Enhanced search result with multiple scores"""
    id: str
    text: str
    metadata: Dict[str, Any]
    dense_score: float = 0.0
    sparse_score: float = 0.0
    hybrid_score: float = 0.0
    rerank_score: Optional[float] = None
    final_score: float = 0.0
    source_table: str = ""


@dataclass
class HybridSearchConfig:
    """Configuration for hybrid search"""
    dense_weight: float = 0.7
    sparse_weight: float = 0.3
    top_k_dense: int = 50
    top_k_sparse: int = 50
    final_top_k: int = 10
    similarity_threshold: float = 0.7
    enable_reranking: bool = True
    rerank_top_k: int = 20
    adaptive_threshold: float = 0.8


class HybridSearchEngine:
    """Advanced hybrid search combining dense and sparse retrieval"""
    
    def __init__(self, 
                 database_url: str,
                 embedding_service,
                 reranker_service=None,
                 config: HybridSearchConfig = None):
        
        self.database_url = database_url
        self.embedding_service = embedding_service
        self.reranker_service = reranker_service
        self.config = config or HybridSearchConfig()
        
        # BM25 indices for sparse search
        self.bm25_indices = {}
        self.document_texts = {}
        
        logger.info("Hybrid search engine initialized",
                   dense_weight=self.config.dense_weight,
                   sparse_weight=self.config.sparse_weight,
                   reranking_enabled=self.config.enable_reranking)
    
    async def initialize_sparse_indices(self):
        """Initialize BM25 indices for sparse search"""
        logger.info("Initializing sparse search indices...")
        
        # Load documents from database
        documents = await self._load_all_documents()
        
        for table_name, docs in documents.items():
            if not docs:
                continue
            
            # Prepare texts for BM25
            texts = [doc['text'] for doc in docs]
            tokenized_texts = [text.lower().split() for text in texts]
            
            # Create BM25 index
            self.bm25_indices[table_name] = BM25Okapi(tokenized_texts)
            self.document_texts[table_name] = docs
            
            logger.info(f"BM25 index created for {table_name}",
                       document_count=len(docs))
    
    async def search(self, 
                    query: str,
                    mode: SearchMode = SearchMode.HYBRID,
                    table_priority: List[str] = None,
                    top_k: int = None) -> List[SearchResult]:
        """
        Perform hybrid search with multiple retrieval methods
        
        Args:
            query: Search query
            mode: Search mode (dense, sparse, hybrid, adaptive)
            table_priority: Priority order for tables
            top_k: Number of results to return
            
        Returns:
            List of ranked search results
        """
        top_k = top_k or self.config.final_top_k
        table_priority = table_priority or ['franchisors', 'documents']
        
        start_time = time.time()
        
        # Adaptive mode selection
        if mode == SearchMode.ADAPTIVE:
            mode = await self._select_optimal_mode(query)
        
        # Perform search based on mode
        if mode == SearchMode.DENSE_ONLY:
            results = await self._dense_search(query, table_priority, top_k)
        elif mode == SearchMode.SPARSE_ONLY:
            results = await self._sparse_search(query, table_priority, top_k)
        else:  # HYBRID
            results = await self._hybrid_search(query, table_priority, top_k)
        
        # Apply reranking if enabled
        if self.config.enable_reranking and self.reranker_service and results:
            results = await self._rerank_results(query, results)
        
        processing_time = time.time() - start_time
        
        logger.info("Hybrid search completed",
                   query_length=len(query),
                   mode=mode.value,
                   results_count=len(results),
                   processing_time=processing_time)
        
        return results[:top_k]
    
    async def _dense_search(self, 
                          query: str, 
                          table_priority: List[str],
                          top_k: int) -> List[SearchResult]:
        """Perform dense vector search"""
        # Generate query embedding
        query_embedding = await self.embedding_service.generate_embedding(query)
        
        all_results = []
        
        with psycopg2.connect(self.database_url) as conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                for table_name in table_priority:
                    if table_name == 'franchisors':
                        results = await self._search_franchisors_dense(cur, query_embedding, top_k)
                    elif table_name == 'documents':
                        results = await self._search_documents_dense(cur, query_embedding, top_k)
                    else:
                        continue
                    
                    all_results.extend(results)
        
        # Sort by dense score and return top results
        all_results.sort(key=lambda x: x.dense_score, reverse=True)
        return all_results[:self.config.top_k_dense]
    
    async def _sparse_search(self, 
                           query: str, 
                           table_priority: List[str],
                           top_k: int) -> List[SearchResult]:
        """Perform sparse (BM25) search"""
        query_tokens = query.lower().split()
        all_results = []
        
        for table_name in table_priority:
            if table_name not in self.bm25_indices:
                continue
            
            bm25 = self.bm25_indices[table_name]
            docs = self.document_texts[table_name]
            
            # Get BM25 scores
            scores = bm25.get_scores(query_tokens)
            
            # Create search results
            for i, score in enumerate(scores):
                if score > 0:  # Only include relevant results
                    doc = docs[i]
                    result = SearchResult(
                        id=doc['id'],
                        text=doc['text'],
                        metadata=doc.get('metadata', {}),
                        sparse_score=float(score),
                        final_score=float(score),
                        source_table=table_name
                    )
                    all_results.append(result)
        
        # Sort by sparse score
        all_results.sort(key=lambda x: x.sparse_score, reverse=True)
        return all_results[:self.config.top_k_sparse]
    
    async def _hybrid_search(self, 
                           query: str, 
                           table_priority: List[str],
                           top_k: int) -> List[SearchResult]:
        """Perform hybrid search combining dense and sparse"""
        # Get results from both methods
        dense_results = await self._dense_search(query, table_priority, self.config.top_k_dense)
        sparse_results = await self._sparse_search(query, table_priority, self.config.top_k_sparse)
        
        # Combine and normalize scores
        combined_results = {}
        
        # Add dense results
        max_dense_score = max([r.dense_score for r in dense_results]) if dense_results else 1.0
        for result in dense_results:
            normalized_dense = result.dense_score / max_dense_score
            combined_results[result.id] = result
            combined_results[result.id].dense_score = normalized_dense
        
        # Add sparse results
        max_sparse_score = max([r.sparse_score for r in sparse_results]) if sparse_results else 1.0
        for result in sparse_results:
            normalized_sparse = result.sparse_score / max_sparse_score
            
            if result.id in combined_results:
                # Combine scores
                combined_results[result.id].sparse_score = normalized_sparse
            else:
                # New result from sparse search
                combined_results[result.id] = result
                combined_results[result.id].sparse_score = normalized_sparse
        
        # Calculate hybrid scores
        final_results = []
        for result in combined_results.values():
            hybrid_score = (
                self.config.dense_weight * result.dense_score +
                self.config.sparse_weight * result.sparse_score
            )
            result.hybrid_score = hybrid_score
            result.final_score = hybrid_score
            final_results.append(result)
        
        # Sort by hybrid score
        final_results.sort(key=lambda x: x.hybrid_score, reverse=True)
        return final_results
    
    async def _rerank_results(self, 
                            query: str, 
                            results: List[SearchResult]) -> List[SearchResult]:
        """Apply reranking to improve result quality"""
        if not results or len(results) <= 1:
            return results
        
        # Take top results for reranking
        rerank_candidates = results[:self.config.rerank_top_k]
        
        # Prepare texts for reranking
        texts = [result.text for result in rerank_candidates]
        
        # Get rerank scores
        rerank_scores = await self.reranker_service.rerank(query, texts)
        
        # Update results with rerank scores
        for i, result in enumerate(rerank_candidates):
            if i < len(rerank_scores):
                result.rerank_score = rerank_scores[i]
                result.final_score = rerank_scores[i]
        
        # Sort by rerank score
        rerank_candidates.sort(key=lambda x: x.rerank_score or 0, reverse=True)
        
        # Combine with remaining results
        remaining_results = results[self.config.rerank_top_k:]
        return rerank_candidates + remaining_results
    
    async def _select_optimal_mode(self, query: str) -> SearchMode:
        """Automatically select the best search mode for the query"""
        # Simple heuristics for mode selection
        query_length = len(query.split())
        
        if query_length <= 3:
            # Short queries work better with sparse search
            return SearchMode.SPARSE_ONLY
        elif query_length >= 10:
            # Long queries work better with dense search
            return SearchMode.DENSE_ONLY
        else:
            # Medium queries benefit from hybrid approach
            return SearchMode.HYBRID
    
    async def _load_all_documents(self) -> Dict[str, List[Dict]]:
        """Load all documents from database for sparse indexing"""
        documents = {'franchisors': [], 'documents': []}
        
        with psycopg2.connect(self.database_url) as conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                # Load franchisors
                cur.execute("""
                    SELECT id, name, description, 
                           COALESCE(name || ' ' || description, name) as text
                    FROM franchisors 
                    WHERE is_active = true
                """)
                for row in cur.fetchall():
                    documents['franchisors'].append({
                        'id': row['id'],
                        'text': row['text'] or '',
                        'metadata': {'name': row['name'], 'description': row['description']}
                    })
                
                # Load document chunks
                cur.execute("""
                    SELECT id, text, metadata
                    FROM document_chunks
                    WHERE text IS NOT NULL AND text != ''
                """)
                for row in cur.fetchall():
                    documents['documents'].append({
                        'id': row['id'],
                        'text': row['text'],
                        'metadata': row['metadata'] or {}
                    })
        
        return documents
    
    async def _search_franchisors_dense(self, 
                                      cursor, 
                                      query_embedding: List[float],
                                      top_k: int) -> List[SearchResult]:
        """Search franchisors using dense vectors"""
        cursor.execute("""
            SELECT id, name, description, embedding,
                   1 - (embedding <=> %s) as similarity
            FROM franchisors 
            WHERE embedding IS NOT NULL 
              AND is_active = true
              AND 1 - (embedding <=> %s) >= %s
            ORDER BY embedding <=> %s
            LIMIT %s
        """, (query_embedding, query_embedding, self.config.similarity_threshold, 
              query_embedding, top_k))
        
        results = []
        for row in cursor.fetchall():
            result = SearchResult(
                id=row['id'],
                text=f"{row['name']} {row['description'] or ''}",
                metadata={'name': row['name'], 'description': row['description']},
                dense_score=float(row['similarity']),
                final_score=float(row['similarity']),
                source_table='franchisors'
            )
            results.append(result)
        
        return results
    
    async def _search_documents_dense(self, 
                                    cursor, 
                                    query_embedding: List[float],
                                    top_k: int) -> List[SearchResult]:
        """Search document chunks using dense vectors"""
        cursor.execute("""
            SELECT id, text, metadata, embedding,
                   1 - (embedding <=> %s) as similarity
            FROM document_chunks 
            WHERE embedding IS NOT NULL 
              AND 1 - (embedding <=> %s) >= %s
            ORDER BY embedding <=> %s
            LIMIT %s
        """, (query_embedding, query_embedding, self.config.similarity_threshold,
              query_embedding, top_k))
        
        results = []
        for row in cursor.fetchall():
            result = SearchResult(
                id=row['id'],
                text=row['text'],
                metadata=row['metadata'] or {},
                dense_score=float(row['similarity']),
                final_score=float(row['similarity']),
                source_table='documents'
            )
            results.append(result)
        
        return results
