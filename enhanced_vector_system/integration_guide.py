"""
Integration Guide for Enhanced Vector System
Shows how to integrate the latest vector technologies with existing system
"""

import asyncio
import os
from typing import Dict, Any, List
import structlog
from dataclasses import dataclass

# Import enhanced components
from .latest_embedding_service import LatestEmbeddingService, EmbeddingModel, EmbeddingConfig
from .hybrid_search_engine import HybridSearchEngine, SearchMode, HybridSearchConfig
from .advanced_reranker import AdvancedReranker, RerankerModel, RerankConfig
from .smart_chunking_v2 import SmartChunkerV2, ChunkingStrategy, ChunkingConfig
from .optimized_vector_store import OptimizedVectorStore, VectorBackend, VectorStoreConfig
from .advanced_rag_system import AdvancedRAGSystem, RAGStrategy, RAGConfig

logger = structlog.get_logger()


@dataclass
class EnhancedSystemConfig:
    """Configuration for the complete enhanced system"""
    # API Keys
    openai_api_key: str
    cohere_api_key: str = None
    
    # Database URLs
    database_url: str
    redis_url: str = None
    
    # Model Selection
    embedding_model: EmbeddingModel = EmbeddingModel.OPENAI_TEXT_EMBEDDING_3_LARGE
    reranker_model: RerankerModel = RerankerModel.COHERE_RERANK_3
    
    # Strategy Selection
    chunking_strategy: ChunkingStrategy = ChunkingStrategy.HYBRID
    search_mode: SearchMode = SearchMode.HYBRID
    rag_strategy: RAGStrategy = RAGStrategy.FUSION
    
    # Performance Settings
    enable_caching: bool = True
    enable_gpu: bool = False
    max_workers: int = 4


class EnhancedVectorSystem:
    """Complete enhanced vector system integration"""
    
    def __init__(self, config: EnhancedSystemConfig):
        self.config = config
        
        # Initialize components
        self.embedding_service = None
        self.chunker = None
        self.vector_store = None
        self.search_engine = None
        self.reranker = None
        self.rag_system = None
        
        logger.info("Enhanced vector system initializing...")
    
    async def initialize(self):
        """Initialize all components"""
        try:
            # 1. Initialize Embedding Service
            embedding_config = EmbeddingConfig(
                primary_model=self.config.embedding_model,
                enable_caching=self.config.enable_caching
            )
            
            self.embedding_service = LatestEmbeddingService(
                openai_api_key=self.config.openai_api_key,
                cohere_api_key=self.config.cohere_api_key,
                config=embedding_config
            )
            
            # 2. Initialize Smart Chunker
            chunking_config = ChunkingConfig(
                strategy=self.config.chunking_strategy,
                enable_metadata=True
            )
            
            self.chunker = SmartChunkerV2(config=chunking_config)
            
            # 3. Initialize Vector Store
            vector_config = VectorStoreConfig(
                primary_backend=VectorBackend.PGVECTOR,
                enable_caching=self.config.enable_caching
            )
            
            self.vector_store = OptimizedVectorStore(
                database_url=self.config.database_url,
                redis_url=self.config.redis_url,
                config=vector_config
            )
            await self.vector_store.initialize()
            
            # 4. Initialize Search Engine
            search_config = HybridSearchConfig(
                enable_reranking=True
            )
            
            self.search_engine = HybridSearchEngine(
                database_url=self.config.database_url,
                embedding_service=self.embedding_service,
                config=search_config
            )
            await self.search_engine.initialize_sparse_indices()
            
            # 5. Initialize Reranker
            rerank_config = RerankConfig(
                primary_model=self.config.reranker_model
            )
            
            self.reranker = AdvancedReranker(
                cohere_api_key=self.config.cohere_api_key,
                openai_api_key=self.config.openai_api_key,
                config=rerank_config
            )
            
            # 6. Initialize RAG System
            rag_config = RAGConfig(
                strategy=self.config.rag_strategy,
                enable_query_expansion=True,
                enable_answer_verification=True
            )
            
            self.rag_system = AdvancedRAGSystem(
                openai_api_key=self.config.openai_api_key,
                search_engine=self.search_engine,
                reranker=self.reranker,
                embedding_service=self.embedding_service,
                config=rag_config
            )
            
            logger.info("Enhanced vector system initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize enhanced vector system: {e}")
            raise
    
    async def process_document(self, 
                             text: str, 
                             document_id: str,
                             document_type: str = "pdf",
                             metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Process document with enhanced pipeline
        
        Args:
            text: Document text
            document_id: Unique document identifier
            document_type: Type of document
            metadata: Additional metadata
            
        Returns:
            Processing results
        """
        try:
            # 1. Smart Chunking
            chunks = await self.chunker.chunk_document(
                text=text,
                document_type=document_type,
                document_metadata=metadata
            )
            
            # 2. Generate Embeddings
            chunk_texts = [chunk.text for chunk in chunks]
            embeddings = await self.embedding_service.generate_batch_embeddings(chunk_texts)
            
            # 3. Prepare vectors for storage
            vectors = []
            for chunk, embedding in zip(chunks, embeddings):
                vectors.append({
                    'id': chunk.id,
                    'document_id': document_id,
                    'text': chunk.text,
                    'embedding': embedding,
                    'metadata': {
                        'chunk_index': chunk.metadata.chunk_index,
                        'section_title': chunk.metadata.section_title,
                        'section_type': chunk.metadata.section_type,
                        'token_count': chunk.metadata.token_count,
                        'complexity_score': chunk.metadata.complexity_score,
                        **metadata or {}
                    }
                })
            
            # 4. Store vectors
            success = await self.vector_store.store_vectors(vectors)
            
            return {
                'success': success,
                'chunks_created': len(chunks),
                'total_tokens': sum(chunk.metadata.token_count for chunk in chunks),
                'avg_complexity': sum(chunk.metadata.complexity_score for chunk in chunks) / len(chunks),
                'chunking_strategy': self.config.chunking_strategy.value,
                'embedding_model': self.config.embedding_model.value
            }
            
        except Exception as e:
            logger.error(f"Document processing failed: {e}")
            return {'success': False, 'error': str(e)}
    
    async def answer_question(self, 
                            question: str,
                            context: str = None,
                            strategy: RAGStrategy = None) -> Dict[str, Any]:
        """
        Answer question using enhanced RAG system
        
        Args:
            question: User question
            context: Additional context
            strategy: Specific RAG strategy to use
            
        Returns:
            Enhanced answer with metadata
        """
        try:
            response = await self.rag_system.answer_question(
                question=question,
                context=context,
                strategy=strategy
            )
            
            return {
                'answer': response.answer,
                'confidence': response.confidence_score,
                'sources': [
                    {
                        'id': source.id,
                        'text': source.text[:200] + '...' if len(source.text) > 200 else source.text,
                        'score': source.score,
                        'metadata': source.metadata
                    }
                    for source in response.sources
                ],
                'strategy_used': response.strategy_used,
                'processing_time': response.processing_time,
                'context_length': response.context_length,
                'query_expansions': response.query_expansions,
                'verification_result': response.verification_result
            }
            
        except Exception as e:
            logger.error(f"Question answering failed: {e}")
            return {
                'answer': "I apologize, but I encountered an error while processing your question.",
                'confidence': 0.0,
                'sources': [],
                'error': str(e)
            }
    
    async def benchmark_system(self) -> Dict[str, Any]:
        """
        Benchmark the enhanced system performance
        
        Returns:
            Performance metrics
        """
        try:
            # Test embedding generation
            test_text = "This is a test document for benchmarking the enhanced vector system."
            
            import time
            start_time = time.time()
            embedding = await self.embedding_service.generate_embedding(test_text)
            embedding_time = time.time() - start_time
            
            # Test search
            start_time = time.time()
            search_results = await self.search_engine.search(
                query="test query",
                mode=SearchMode.HYBRID,
                top_k=10
            )
            search_time = time.time() - start_time
            
            # Test reranking
            if search_results:
                start_time = time.time()
                rerank_results = await self.reranker.rerank(
                    query="test query",
                    documents=[r.text for r in search_results[:5]]
                )
                rerank_time = time.time() - start_time
            else:
                rerank_time = 0.0
            
            return {
                'embedding_generation_time': embedding_time,
                'embedding_dimension': len(embedding),
                'search_time': search_time,
                'search_results_count': len(search_results),
                'rerank_time': rerank_time,
                'system_status': 'healthy',
                'models_used': {
                    'embedding': self.config.embedding_model.value,
                    'reranker': self.config.reranker_model.value,
                    'chunking': self.config.chunking_strategy.value,
                    'search': self.config.search_mode.value,
                    'rag': self.config.rag_strategy.value
                }
            }
            
        except Exception as e:
            logger.error(f"Benchmarking failed: {e}")
            return {'system_status': 'error', 'error': str(e)}


# Example usage and integration
async def main():
    """Example of how to use the enhanced vector system"""
    
    # Configuration
    config = EnhancedSystemConfig(
        openai_api_key=os.getenv("OPENAI_API_KEY"),
        cohere_api_key=os.getenv("COHERE_API_KEY"),
        database_url=os.getenv("DATABASE_URL"),
        redis_url=os.getenv("REDIS_URL"),
        embedding_model=EmbeddingModel.OPENAI_TEXT_EMBEDDING_3_LARGE,
        reranker_model=RerankerModel.COHERE_RERANK_3,
        chunking_strategy=ChunkingStrategy.HYBRID,
        search_mode=SearchMode.HYBRID,
        rag_strategy=RAGStrategy.FUSION
    )
    
    # Initialize system
    system = EnhancedVectorSystem(config)
    await system.initialize()
    
    # Process a document
    sample_text = """
    Coochie Hydrogreen is an innovative eco-friendly franchise opportunity.
    The initial investment ranges from $150,000 to $300,000.
    We provide comprehensive training and ongoing support to all franchisees.
    """
    
    result = await system.process_document(
        text=sample_text,
        document_id="coochie_info_pack",
        document_type="pdf",
        metadata={"source": "franchise_brochure", "category": "investment_info"}
    )
    
    print("Document processing result:", result)
    
    # Answer a question
    answer_result = await system.answer_question(
        question="What is the investment requirement for Coochie Hydrogreen?",
        strategy=RAGStrategy.FUSION
    )
    
    print("Answer result:", answer_result)
    
    # Benchmark system
    benchmark_result = await system.benchmark_system()
    print("Benchmark result:", benchmark_result)


if __name__ == "__main__":
    asyncio.run(main())
