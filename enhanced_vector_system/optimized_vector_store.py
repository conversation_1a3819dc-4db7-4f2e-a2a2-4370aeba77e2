"""
Optimized Vector Store with Latest Technologies (2024-2025)
Implements advanced vector storage with multiple backends, caching, and performance optimizations
"""

import asyncio
import time
from typing import List, Dict, Any, Optional, Tuple, Union
from dataclasses import dataclass
from enum import Enum
import structlog
import psycopg2
from psycopg2.extras import RealDictCursor, execute_values
import redis
import json
import numpy as np
import faiss
from contextlib import asynccontextmanager

logger = structlog.get_logger()


class VectorBackend(Enum):
    """Supported vector storage backends"""
    PGVECTOR = "pgvector"  # PostgreSQL with pgvector extension
    FAISS = "faiss"  # Facebook AI Similarity Search
    QDRANT = "qdrant"  # Qdrant vector database
    WEAVIATE = "weaviate"  # Weaviate vector database
    PINECONE = "pinecone"  # Pinecone vector database


@dataclass
class VectorStoreConfig:
    """Configuration for optimized vector store"""
    primary_backend: VectorBackend = VectorBackend.PGVECTOR
    fallback_backend: Optional[VectorBackend] = VectorBackend.FAISS
    enable_caching: bool = True
    cache_ttl: int = 3600  # 1 hour
    batch_size: int = 100
    connection_pool_size: int = 10
    enable_compression: bool = True
    enable_quantization: bool = False  # For FAISS
    index_type: str = "IVF"  # FAISS index type


@dataclass
class VectorSearchResult:
    """Enhanced vector search result"""
    id: str
    text: str
    metadata: Dict[str, Any]
    score: float
    backend: str
    cached: bool = False
    processing_time: float = 0.0


class OptimizedVectorStore:
    """Advanced vector store with multiple backends and optimizations"""
    
    def __init__(self, 
                 database_url: str,
                 redis_url: Optional[str] = None,
                 config: VectorStoreConfig = None):
        
        self.database_url = database_url
        self.redis_url = redis_url
        self.config = config or VectorStoreConfig()
        
        # Initialize backends
        self.backends = {}
        self.redis_client = None
        
        # FAISS indices
        self.faiss_indices = {}
        self.faiss_id_maps = {}
        
        logger.info("Optimized vector store initialized",
                   primary_backend=self.config.primary_backend.value,
                   caching_enabled=self.config.enable_caching)
    
    async def initialize(self):
        """Initialize vector store backends"""
        # Initialize Redis cache
        if self.config.enable_caching and self.redis_url:
            try:
                self.redis_client = redis.from_url(self.redis_url)
                await self.redis_client.ping()
                logger.info("Redis cache initialized")
            except Exception as e:
                logger.warning(f"Failed to initialize Redis cache: {e}")
                self.redis_client = None
        
        # Initialize FAISS if needed
        if (self.config.primary_backend == VectorBackend.FAISS or 
            self.config.fallback_backend == VectorBackend.FAISS):
            await self._initialize_faiss()
        
        logger.info("Vector store initialization completed")
    
    async def store_vectors(self, 
                          vectors: List[Dict[str, Any]],
                          table_name: str = "document_chunks") -> bool:
        """
        Store vectors with optimized batch operations
        
        Args:
            vectors: List of vector data with id, text, embedding, metadata
            table_name: Target table name
            
        Returns:
            Success status
        """
        start_time = time.time()
        
        try:
            # Primary backend storage
            success = await self._store_vectors_backend(
                vectors, table_name, self.config.primary_backend
            )
            
            # Fallback backend storage
            if not success and self.config.fallback_backend:
                logger.warning("Primary backend failed, trying fallback")
                success = await self._store_vectors_backend(
                    vectors, table_name, self.config.fallback_backend
                )
            
            # Invalidate cache for affected vectors
            if success and self.redis_client:
                await self._invalidate_cache(vectors)
            
            processing_time = time.time() - start_time
            
            logger.info("Vector storage completed",
                       vector_count=len(vectors),
                       table_name=table_name,
                       success=success,
                       processing_time=processing_time)
            
            return success
            
        except Exception as e:
            logger.error(f"Vector storage failed: {e}")
            return False
    
    async def search_vectors(self, 
                           query_embedding: List[float],
                           top_k: int = 10,
                           similarity_threshold: float = 0.7,
                           table_priority: List[str] = None,
                           filters: Dict[str, Any] = None) -> List[VectorSearchResult]:
        """
        Search vectors with caching and multiple backend support
        
        Args:
            query_embedding: Query vector
            top_k: Number of results to return
            similarity_threshold: Minimum similarity score
            table_priority: Priority order for tables
            filters: Additional filters
            
        Returns:
            List of search results
        """
        start_time = time.time()
        
        # Generate cache key
        cache_key = self._generate_cache_key(query_embedding, top_k, similarity_threshold, filters)
        
        # Check cache first
        if self.redis_client and self.config.enable_caching:
            cached_results = await self._get_cached_results(cache_key)
            if cached_results:
                logger.debug("Returning cached search results")
                return cached_results
        
        # Search in primary backend
        results = await self._search_vectors_backend(
            query_embedding, top_k, similarity_threshold, 
            table_priority, filters, self.config.primary_backend
        )
        
        # Fallback to secondary backend if needed
        if not results and self.config.fallback_backend:
            logger.warning("Primary backend returned no results, trying fallback")
            results = await self._search_vectors_backend(
                query_embedding, top_k, similarity_threshold,
                table_priority, filters, self.config.fallback_backend
            )
        
        # Cache results
        if results and self.redis_client and self.config.enable_caching:
            await self._cache_results(cache_key, results)
        
        processing_time = time.time() - start_time
        
        # Update processing time in results
        for result in results:
            result.processing_time = processing_time
        
        logger.info("Vector search completed",
                   query_dimension=len(query_embedding),
                   results_count=len(results),
                   processing_time=processing_time)
        
        return results
    
    async def _store_vectors_backend(self, 
                                   vectors: List[Dict[str, Any]],
                                   table_name: str,
                                   backend: VectorBackend) -> bool:
        """Store vectors in specific backend"""
        if backend == VectorBackend.PGVECTOR:
            return await self._store_vectors_pgvector(vectors, table_name)
        elif backend == VectorBackend.FAISS:
            return await self._store_vectors_faiss(vectors, table_name)
        else:
            logger.error(f"Backend {backend.value} not implemented")
            return False
    
    async def _search_vectors_backend(self, 
                                    query_embedding: List[float],
                                    top_k: int,
                                    similarity_threshold: float,
                                    table_priority: List[str],
                                    filters: Dict[str, Any],
                                    backend: VectorBackend) -> List[VectorSearchResult]:
        """Search vectors in specific backend"""
        if backend == VectorBackend.PGVECTOR:
            return await self._search_vectors_pgvector(
                query_embedding, top_k, similarity_threshold, table_priority, filters
            )
        elif backend == VectorBackend.FAISS:
            return await self._search_vectors_faiss(
                query_embedding, top_k, similarity_threshold, table_priority, filters
            )
        else:
            logger.error(f"Backend {backend.value} not implemented")
            return []
    
    async def _store_vectors_pgvector(self, 
                                    vectors: List[Dict[str, Any]],
                                    table_name: str) -> bool:
        """Store vectors in PostgreSQL with pgvector"""
        try:
            with psycopg2.connect(self.database_url) as conn:
                with conn.cursor() as cur:
                    if table_name == "franchisors":
                        # Update franchisor embeddings
                        for vector in vectors:
                            cur.execute("""
                                UPDATE franchisors 
                                SET embedding = %s, updated_at = NOW()
                                WHERE id = %s
                            """, (vector['embedding'], vector['id']))
                    else:
                        # Batch insert document chunks
                        values = [
                            (
                                vector['id'],
                                vector.get('document_id'),
                                vector['text'],
                                vector['embedding'],
                                json.dumps(vector.get('metadata', {}))
                            )
                            for vector in vectors
                        ]
                        
                        execute_values(
                            cur,
                            """
                            INSERT INTO document_chunks 
                            (id, document_id, text, embedding, metadata)
                            VALUES %s
                            ON CONFLICT (id) DO UPDATE SET
                                text = EXCLUDED.text,
                                embedding = EXCLUDED.embedding,
                                metadata = EXCLUDED.metadata,
                                updated_at = NOW()
                            """,
                            values,
                            template=None,
                            page_size=self.config.batch_size
                        )
                    
                    conn.commit()
                    return True
                    
        except Exception as e:
            logger.error(f"pgvector storage failed: {e}")
            return False
    
    async def _search_vectors_pgvector(self, 
                                     query_embedding: List[float],
                                     top_k: int,
                                     similarity_threshold: float,
                                     table_priority: List[str],
                                     filters: Dict[str, Any]) -> List[VectorSearchResult]:
        """Search vectors in PostgreSQL with pgvector"""
        results = []
        table_priority = table_priority or ['franchisors', 'document_chunks']
        
        try:
            with psycopg2.connect(self.database_url) as conn:
                with conn.cursor(cursor_factory=RealDictCursor) as cur:
                    for table_name in table_priority:
                        if table_name == 'franchisors':
                            cur.execute("""
                                SELECT id, name, description, embedding,
                                       1 - (embedding <=> %s) as similarity
                                FROM franchisors 
                                WHERE embedding IS NOT NULL 
                                  AND is_active = true
                                  AND 1 - (embedding <=> %s) >= %s
                                ORDER BY embedding <=> %s
                                LIMIT %s
                            """, (query_embedding, query_embedding, similarity_threshold, 
                                  query_embedding, top_k))
                            
                            for row in cur.fetchall():
                                result = VectorSearchResult(
                                    id=row['id'],
                                    text=f"{row['name']} {row['description'] or ''}",
                                    metadata={'name': row['name'], 'description': row['description']},
                                    score=float(row['similarity']),
                                    backend=VectorBackend.PGVECTOR.value
                                )
                                results.append(result)
                        
                        elif table_name == 'document_chunks':
                            cur.execute("""
                                SELECT id, text, metadata, embedding,
                                       1 - (embedding <=> %s) as similarity
                                FROM document_chunks 
                                WHERE embedding IS NOT NULL 
                                  AND 1 - (embedding <=> %s) >= %s
                                ORDER BY embedding <=> %s
                                LIMIT %s
                            """, (query_embedding, query_embedding, similarity_threshold,
                                  query_embedding, top_k))
                            
                            for row in cur.fetchall():
                                result = VectorSearchResult(
                                    id=row['id'],
                                    text=row['text'],
                                    metadata=row['metadata'] or {},
                                    score=float(row['similarity']),
                                    backend=VectorBackend.PGVECTOR.value
                                )
                                results.append(result)
                        
                        # Stop if we have enough high-quality results
                        high_quality_results = [r for r in results if r.score >= similarity_threshold]
                        if len(high_quality_results) >= top_k:
                            break
            
            return results[:top_k]
            
        except Exception as e:
            logger.error(f"pgvector search failed: {e}")
            return []
