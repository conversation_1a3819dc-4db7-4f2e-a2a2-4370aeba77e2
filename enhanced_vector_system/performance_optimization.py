"""
Performance Optimization for Enhanced Vector System
Implements caching, connection pooling, and performance monitoring
"""

import asyncio
import time
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
import structlog
import redis
import psycopg2
from psycopg2 import pool
import json
import hashlib
from contextlib import asynccontextmanager
import threading
from collections import defaultdict

logger = structlog.get_logger()


@dataclass
class PerformanceConfig:
    """Configuration for performance optimizations"""
    # Caching
    enable_embedding_cache: bool = True
    enable_search_cache: bool = True
    cache_ttl: int = 3600  # 1 hour
    
    # Connection Pooling
    db_pool_min_conn: int = 5
    db_pool_max_conn: int = 20
    
    # Batch Processing
    embedding_batch_size: int = 100
    vector_insert_batch_size: int = 50
    
    # Performance Monitoring
    enable_metrics: bool = True
    metrics_interval: int = 60  # seconds


class PerformanceOptimizer:
    """Performance optimization manager"""
    
    def __init__(self, 
                 database_url: str,
                 redis_url: Optional[str] = None,
                 config: PerformanceConfig = None):
        
        self.database_url = database_url
        self.redis_url = redis_url
        self.config = config or PerformanceConfig()
        
        # Connection pools
        self.db_pool = None
        self.redis_client = None
        
        # Performance metrics
        self.metrics = defaultdict(list)
        self.metrics_lock = threading.Lock()
        
        logger.info("Performance optimizer initialized")
    
    async def initialize(self):
        """Initialize performance optimization components"""
        try:
            # Initialize database connection pool
            self.db_pool = psycopg2.pool.ThreadedConnectionPool(
                minconn=self.config.db_pool_min_conn,
                maxconn=self.config.db_pool_max_conn,
                dsn=self.database_url
            )
            
            # Initialize Redis cache
            if self.redis_url and (self.config.enable_embedding_cache or self.config.enable_search_cache):
                self.redis_client = redis.from_url(self.redis_url)
                await self.redis_client.ping()
                logger.info("Redis cache initialized")
            
            # Start metrics collection
            if self.config.enable_metrics:
                asyncio.create_task(self._collect_metrics())
            
            logger.info("Performance optimizer initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize performance optimizer: {e}")
            raise
    
    @asynccontextmanager
    async def get_db_connection(self):
        """Get database connection from pool"""
        conn = None
        try:
            conn = self.db_pool.getconn()
            yield conn
        finally:
            if conn:
                self.db_pool.putconn(conn)
    
    async def cache_embedding(self, text: str, embedding: List[float], model: str):
        """Cache embedding result"""
        if not self.config.enable_embedding_cache or not self.redis_client:
            return
        
        try:
            cache_key = self._generate_embedding_cache_key(text, model)
            cache_value = json.dumps(embedding)
            
            await self.redis_client.setex(
                cache_key, 
                self.config.cache_ttl, 
                cache_value
            )
            
            self._record_metric("cache_writes", 1)
            
        except Exception as e:
            logger.warning(f"Failed to cache embedding: {e}")
    
    async def get_cached_embedding(self, text: str, model: str) -> Optional[List[float]]:
        """Get cached embedding"""
        if not self.config.enable_embedding_cache or not self.redis_client:
            return None
        
        try:
            cache_key = self._generate_embedding_cache_key(text, model)
            cached_value = await self.redis_client.get(cache_key)
            
            if cached_value:
                self._record_metric("cache_hits", 1)
                return json.loads(cached_value)
            else:
                self._record_metric("cache_misses", 1)
                return None
                
        except Exception as e:
            logger.warning(f"Failed to get cached embedding: {e}")
            return None
    
    async def cache_search_results(self, 
                                 query_hash: str, 
                                 results: List[Dict[str, Any]]):
        """Cache search results"""
        if not self.config.enable_search_cache or not self.redis_client:
            return
        
        try:
            cache_key = f"search:{query_hash}"
            cache_value = json.dumps(results, default=str)
            
            await self.redis_client.setex(
                cache_key,
                self.config.cache_ttl,
                cache_value
            )
            
            self._record_metric("search_cache_writes", 1)
            
        except Exception as e:
            logger.warning(f"Failed to cache search results: {e}")
    
    async def get_cached_search_results(self, query_hash: str) -> Optional[List[Dict[str, Any]]]:
        """Get cached search results"""
        if not self.config.enable_search_cache or not self.redis_client:
            return None
        
        try:
            cache_key = f"search:{query_hash}"
            cached_value = await self.redis_client.get(cache_key)
            
            if cached_value:
                self._record_metric("search_cache_hits", 1)
                return json.loads(cached_value)
            else:
                self._record_metric("search_cache_misses", 1)
                return None
                
        except Exception as e:
            logger.warning(f"Failed to get cached search results: {e}")
            return None
    
    async def batch_insert_vectors(self, 
                                 vectors: List[Dict[str, Any]],
                                 table_name: str) -> bool:
        """Optimized batch vector insertion"""
        if not vectors:
            return True
        
        start_time = time.time()
        
        try:
            async with self.get_db_connection() as conn:
                with conn.cursor() as cur:
                    # Prepare batch data
                    if table_name == "franchisors":
                        # Batch update franchisors
                        for vector in vectors:
                            cur.execute("""
                                UPDATE franchisors 
                                SET embedding = %s, updated_at = NOW()
                                WHERE id = %s
                            """, (vector['embedding'], vector['id']))
                    else:
                        # Batch insert document chunks
                        from psycopg2.extras import execute_values
                        
                        values = [
                            (
                                vector['id'],
                                vector.get('document_id'),
                                vector['text'],
                                vector['embedding'],
                                json.dumps(vector.get('metadata', {}))
                            )
                            for vector in vectors
                        ]
                        
                        execute_values(
                            cur,
                            """
                            INSERT INTO document_chunks 
                            (id, document_id, text, embedding, metadata)
                            VALUES %s
                            ON CONFLICT (id) DO UPDATE SET
                                text = EXCLUDED.text,
                                embedding = EXCLUDED.embedding,
                                metadata = EXCLUDED.metadata,
                                updated_at = NOW()
                            """,
                            values,
                            template=None,
                            page_size=self.config.vector_insert_batch_size
                        )
                    
                    conn.commit()
            
            processing_time = time.time() - start_time
            self._record_metric("batch_insert_time", processing_time)
            self._record_metric("vectors_inserted", len(vectors))
            
            logger.info("Batch vector insertion completed",
                       vector_count=len(vectors),
                       processing_time=processing_time)
            
            return True
            
        except Exception as e:
            logger.error(f"Batch vector insertion failed: {e}")
            return False
    
    async def optimize_database(self):
        """Optimize database for vector operations"""
        try:
            async with self.get_db_connection() as conn:
                with conn.cursor() as cur:
                    # Create indexes for better performance
                    optimization_queries = [
                        # Index on embedding column for faster similarity search
                        """
                        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_document_chunks_embedding_cosine 
                        ON document_chunks USING ivfflat (embedding vector_cosine_ops)
                        WITH (lists = 100)
                        """,
                        
                        # Index on franchisor embeddings
                        """
                        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_franchisors_embedding_cosine 
                        ON franchisors USING ivfflat (embedding vector_cosine_ops)
                        WITH (lists = 50)
                        """,
                        
                        # Index on metadata for filtering
                        """
                        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_document_chunks_metadata_gin 
                        ON document_chunks USING gin (metadata)
                        """,
                        
                        # Update table statistics
                        "ANALYZE document_chunks",
                        "ANALYZE franchisors"
                    ]
                    
                    for query in optimization_queries:
                        try:
                            cur.execute(query)
                            conn.commit()
                            logger.info(f"Executed optimization query: {query[:50]}...")
                        except Exception as e:
                            logger.warning(f"Optimization query failed: {e}")
                            conn.rollback()
            
            logger.info("Database optimization completed")
            
        except Exception as e:
            logger.error(f"Database optimization failed: {e}")
    
    def _generate_embedding_cache_key(self, text: str, model: str) -> str:
        """Generate cache key for embedding"""
        text_hash = hashlib.md5(text.encode()).hexdigest()
        return f"embedding:{model}:{text_hash}"
    
    def _record_metric(self, metric_name: str, value: float):
        """Record performance metric"""
        if not self.config.enable_metrics:
            return
        
        with self.metrics_lock:
            self.metrics[metric_name].append({
                'value': value,
                'timestamp': time.time()
            })
    
    async def _collect_metrics(self):
        """Collect and log performance metrics"""
        while True:
            try:
                await asyncio.sleep(self.config.metrics_interval)
                
                with self.metrics_lock:
                    if self.metrics:
                        # Calculate aggregated metrics
                        aggregated = {}
                        for metric_name, values in self.metrics.items():
                            if values:
                                recent_values = [
                                    v['value'] for v in values 
                                    if time.time() - v['timestamp'] < self.config.metrics_interval
                                ]
                                
                                if recent_values:
                                    aggregated[metric_name] = {
                                        'count': len(recent_values),
                                        'sum': sum(recent_values),
                                        'avg': sum(recent_values) / len(recent_values),
                                        'min': min(recent_values),
                                        'max': max(recent_values)
                                    }
                        
                        if aggregated:
                            logger.info("Performance metrics", metrics=aggregated)
                        
                        # Clean old metrics
                        cutoff_time = time.time() - (self.config.metrics_interval * 2)
                        for metric_name in self.metrics:
                            self.metrics[metric_name] = [
                                v for v in self.metrics[metric_name]
                                if v['timestamp'] > cutoff_time
                            ]
                
            except Exception as e:
                logger.error(f"Metrics collection failed: {e}")
    
    async def get_performance_stats(self) -> Dict[str, Any]:
        """Get current performance statistics"""
        stats = {}
        
        # Database pool stats
        if self.db_pool:
            stats['database_pool'] = {
                'total_connections': self.db_pool.maxconn,
                'available_connections': len(self.db_pool._pool),
                'used_connections': self.db_pool.maxconn - len(self.db_pool._pool)
            }
        
        # Cache stats
        if self.redis_client:
            try:
                redis_info = await self.redis_client.info()
                stats['redis_cache'] = {
                    'used_memory': redis_info.get('used_memory_human'),
                    'connected_clients': redis_info.get('connected_clients'),
                    'keyspace_hits': redis_info.get('keyspace_hits', 0),
                    'keyspace_misses': redis_info.get('keyspace_misses', 0)
                }
                
                # Calculate hit rate
                hits = redis_info.get('keyspace_hits', 0)
                misses = redis_info.get('keyspace_misses', 0)
                total = hits + misses
                if total > 0:
                    stats['redis_cache']['hit_rate'] = hits / total
                
            except Exception as e:
                logger.warning(f"Failed to get Redis stats: {e}")
        
        # Application metrics
        with self.metrics_lock:
            if self.metrics:
                recent_metrics = {}
                cutoff_time = time.time() - 300  # Last 5 minutes
                
                for metric_name, values in self.metrics.items():
                    recent_values = [
                        v['value'] for v in values 
                        if v['timestamp'] > cutoff_time
                    ]
                    
                    if recent_values:
                        recent_metrics[metric_name] = {
                            'count': len(recent_values),
                            'avg': sum(recent_values) / len(recent_values),
                            'total': sum(recent_values)
                        }
                
                stats['application_metrics'] = recent_metrics
        
        return stats
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            if self.db_pool:
                self.db_pool.closeall()
            
            if self.redis_client:
                await self.redis_client.close()
            
            logger.info("Performance optimizer cleanup completed")
            
        except Exception as e:
            logger.error(f"Cleanup failed: {e}")


# Performance monitoring decorator
def monitor_performance(metric_name: str):
    """Decorator to monitor function performance"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                processing_time = time.time() - start_time
                
                # Record success metric
                if hasattr(args[0], 'performance_optimizer'):
                    args[0].performance_optimizer._record_metric(
                        f"{metric_name}_success_time", processing_time
                    )
                
                return result
                
            except Exception as e:
                processing_time = time.time() - start_time
                
                # Record error metric
                if hasattr(args[0], 'performance_optimizer'):
                    args[0].performance_optimizer._record_metric(
                        f"{metric_name}_error_time", processing_time
                    )
                
                raise
        
        return wrapper
    return decorator
