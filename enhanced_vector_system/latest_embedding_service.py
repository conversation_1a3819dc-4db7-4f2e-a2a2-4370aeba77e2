"""
Latest Enhanced Embedding Service with Multiple Models (2024-2025)
Supports latest embedding models with automatic fallback and optimization
"""

import asyncio
import time
from typing import List, Dict, Any, Optional, Union
from enum import Enum
import openai
import cohere
import structlog
from tenacity import retry, stop_after_attempt, wait_exponential
import numpy as np
from dataclasses import dataclass

logger = structlog.get_logger()


class EmbeddingModel(Enum):
    """Latest embedding models for 2024-2025"""
    # OpenAI Models (Latest)
    OPENAI_TEXT_EMBEDDING_3_LARGE = "text-embedding-3-large"  # 3072 dimensions, best performance
    OPENAI_TEXT_EMBEDDING_3_SMALL = "text-embedding-3-small"  # 1536 dimensions, cost-effective
    
    # Cohere Models (Latest)
    COHERE_EMBED_V3_ENGLISH = "embed-english-v3.0"  # 1024 dimensions, excellent performance
    COHERE_EMBED_V3_MULTILINGUAL = "embed-multilingual-v3.0"  # 1024 dimensions, multilingual
    
    # Future models (placeholder for easy addition)
    GEMINI_TEXT_EMBEDDING_004 = "text-embedding-004"  # 768 dimensions
    JINA_EMBEDDINGS_V3 = "jina-embeddings-v3"  # 1024 dimensions


@dataclass
class EmbeddingConfig:
    """Configuration for embedding generation"""
    primary_model: EmbeddingModel = EmbeddingModel.OPENAI_TEXT_EMBEDDING_3_LARGE
    fallback_model: EmbeddingModel = EmbeddingModel.OPENAI_TEXT_EMBEDDING_3_SMALL
    batch_size: int = 100
    max_retries: int = 3
    timeout: int = 30
    enable_caching: bool = True
    cache_ttl: int = 3600  # 1 hour


class LatestEmbeddingService:
    """Enhanced embedding service with latest models and optimizations"""
    
    def __init__(self, 
                 openai_api_key: str,
                 cohere_api_key: Optional[str] = None,
                 config: EmbeddingConfig = None):
        
        self.config = config or EmbeddingConfig()
        
        # Initialize clients
        self.openai_client = openai.OpenAI(api_key=openai_api_key)
        self.cohere_client = cohere.Client(cohere_api_key) if cohere_api_key else None
        
        # Model dimensions mapping
        self.model_dimensions = {
            EmbeddingModel.OPENAI_TEXT_EMBEDDING_3_LARGE: 3072,
            EmbeddingModel.OPENAI_TEXT_EMBEDDING_3_SMALL: 1536,
            EmbeddingModel.COHERE_EMBED_V3_ENGLISH: 1024,
            EmbeddingModel.COHERE_EMBED_V3_MULTILINGUAL: 1024,
            EmbeddingModel.GEMINI_TEXT_EMBEDDING_004: 768,
            EmbeddingModel.JINA_EMBEDDINGS_V3: 1024,
        }
        
        logger.info("Latest embedding service initialized", 
                   primary_model=self.config.primary_model.value,
                   fallback_model=self.config.fallback_model.value)
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    async def generate_embedding(self, 
                               text: str, 
                               model: Optional[EmbeddingModel] = None) -> List[float]:
        """
        Generate embedding using latest models with automatic fallback
        
        Args:
            text: Text to embed
            model: Specific model to use (defaults to primary model)
            
        Returns:
            Embedding vector
        """
        model = model or self.config.primary_model
        
        try:
            if model.value.startswith("text-embedding"):
                return await self._generate_openai_embedding(text, model)
            elif model.value.startswith("embed-"):
                return await self._generate_cohere_embedding(text, model)
            else:
                raise ValueError(f"Unsupported model: {model.value}")
                
        except Exception as e:
            logger.warning(f"Primary model failed, trying fallback", 
                          primary_model=model.value, 
                          error=str(e))
            
            if model != self.config.fallback_model:
                return await self.generate_embedding(text, self.config.fallback_model)
            else:
                raise
    
    async def _generate_openai_embedding(self, 
                                       text: str, 
                                       model: EmbeddingModel) -> List[float]:
        """Generate OpenAI embedding"""
        start_time = time.time()
        
        response = await asyncio.to_thread(
            self.openai_client.embeddings.create,
            model=model.value,
            input=self._clean_text(text),
            encoding_format="float"
        )
        
        processing_time = time.time() - start_time
        embedding = response.data[0].embedding
        
        logger.debug("OpenAI embedding generated",
                    model=model.value,
                    text_length=len(text),
                    processing_time=processing_time,
                    dimension=len(embedding))
        
        return embedding
    
    async def _generate_cohere_embedding(self, 
                                       text: str, 
                                       model: EmbeddingModel) -> List[float]:
        """Generate Cohere embedding"""
        if not self.cohere_client:
            raise ValueError("Cohere client not initialized")
        
        start_time = time.time()
        
        response = await asyncio.to_thread(
            self.cohere_client.embed,
            texts=[self._clean_text(text)],
            model=model.value,
            input_type="search_document"
        )
        
        processing_time = time.time() - start_time
        embedding = response.embeddings[0]
        
        logger.debug("Cohere embedding generated",
                    model=model.value,
                    text_length=len(text),
                    processing_time=processing_time,
                    dimension=len(embedding))
        
        return embedding
    
    async def generate_batch_embeddings(self, 
                                      texts: List[str],
                                      model: Optional[EmbeddingModel] = None) -> List[List[float]]:
        """
        Generate embeddings for multiple texts efficiently
        
        Args:
            texts: List of texts to embed
            model: Model to use
            
        Returns:
            List of embedding vectors
        """
        model = model or self.config.primary_model
        
        # Process in batches for efficiency
        all_embeddings = []
        
        for i in range(0, len(texts), self.config.batch_size):
            batch = texts[i:i + self.config.batch_size]
            
            if model.value.startswith("text-embedding"):
                batch_embeddings = await self._generate_openai_batch_embeddings(batch, model)
            elif model.value.startswith("embed-"):
                batch_embeddings = await self._generate_cohere_batch_embeddings(batch, model)
            else:
                # Fallback to individual generation
                batch_embeddings = []
                for text in batch:
                    embedding = await self.generate_embedding(text, model)
                    batch_embeddings.append(embedding)
            
            all_embeddings.extend(batch_embeddings)
        
        logger.info("Batch embeddings generated",
                   total_texts=len(texts),
                   model=model.value,
                   batches=len(texts) // self.config.batch_size + 1)
        
        return all_embeddings
    
    async def _generate_openai_batch_embeddings(self, 
                                              texts: List[str],
                                              model: EmbeddingModel) -> List[List[float]]:
        """Generate OpenAI batch embeddings"""
        cleaned_texts = [self._clean_text(text) for text in texts]
        
        response = await asyncio.to_thread(
            self.openai_client.embeddings.create,
            model=model.value,
            input=cleaned_texts,
            encoding_format="float"
        )
        
        return [data.embedding for data in response.data]
    
    async def _generate_cohere_batch_embeddings(self, 
                                              texts: List[str],
                                              model: EmbeddingModel) -> List[List[float]]:
        """Generate Cohere batch embeddings"""
        if not self.cohere_client:
            raise ValueError("Cohere client not initialized")
        
        cleaned_texts = [self._clean_text(text) for text in texts]
        
        response = await asyncio.to_thread(
            self.cohere_client.embed,
            texts=cleaned_texts,
            model=model.value,
            input_type="search_document"
        )
        
        return response.embeddings
    
    def get_model_dimension(self, model: EmbeddingModel) -> int:
        """Get dimension for a specific model"""
        return self.model_dimensions.get(model, 1536)
    
    def _clean_text(self, text: str) -> str:
        """Clean and prepare text for embedding"""
        if not text or not text.strip():
            return ""
        
        # Remove excessive whitespace
        cleaned = " ".join(text.split())
        
        # Truncate if too long (model-specific limits)
        max_length = 8192  # Conservative limit for most models
        if len(cleaned) > max_length:
            cleaned = cleaned[:max_length]
            logger.warning("Text truncated for embedding", 
                          original_length=len(text),
                          truncated_length=len(cleaned))
        
        return cleaned
    
    async def compare_models(self, text: str) -> Dict[str, Any]:
        """
        Compare different embedding models for the same text
        Useful for evaluation and model selection
        """
        results = {}
        
        for model in [EmbeddingModel.OPENAI_TEXT_EMBEDDING_3_LARGE,
                     EmbeddingModel.OPENAI_TEXT_EMBEDDING_3_SMALL,
                     EmbeddingModel.COHERE_EMBED_V3_ENGLISH]:
            try:
                start_time = time.time()
                embedding = await self.generate_embedding(text, model)
                processing_time = time.time() - start_time
                
                results[model.value] = {
                    "embedding": embedding,
                    "dimension": len(embedding),
                    "processing_time": processing_time,
                    "success": True
                }
            except Exception as e:
                results[model.value] = {
                    "error": str(e),
                    "success": False
                }
        
        return results
