"""
Comprehensive Testing Suite for Enhanced Vector System
Tests all components with performance benchmarks and accuracy measurements
"""

import asyncio
import time
import pytest
import numpy as np
from typing import Dict, Any, List
import structlog
from dataclasses import dataclass
import json
import os

# Import enhanced components
from .integration_guide import EnhancedVectorSystem, EnhancedSystemConfig
from .latest_embedding_service import EmbeddingModel
from .hybrid_search_engine import SearchMode
from .advanced_reranker import RerankerModel
from .smart_chunking_v2 import ChunkingStrategy
from .advanced_rag_system import RAGStrategy

logger = structlog.get_logger()


@dataclass
class TestResult:
    """Test result with metrics"""
    test_name: str
    success: bool
    processing_time: float
    accuracy_score: float = 0.0
    metadata: Dict[str, Any] = None
    error: str = None


class EnhancedSystemTester:
    """Comprehensive testing suite for enhanced vector system"""
    
    def __init__(self, config: EnhancedSystemConfig):
        self.config = config
        self.system = None
        self.test_results = []
        
        # Test data
        self.test_documents = [
            {
                'id': 'test_doc_1',
                'text': """
                Coochie Hydrogreen is an innovative eco-friendly franchise opportunity 
                specializing in sustainable water treatment solutions. The initial 
                investment requirement ranges from $150,000 to $300,000, which includes 
                equipment, training, and initial marketing support. We provide 
                comprehensive training programs lasting 4-6 weeks, covering technical 
                operations, business management, and customer service. Ongoing support 
                includes monthly check-ins, technical assistance, and marketing materials.
                """,
                'type': 'franchise_info',
                'metadata': {'category': 'investment', 'source': 'brochure'}
            },
            {
                'id': 'test_doc_2', 
                'text': """
                Territory requirements for Coochie Hydrogreen franchises include a 
                minimum population of 50,000 within a 15-mile radius. Exclusive 
                territory rights are granted to prevent competition between franchisees. 
                The ideal location should have access to commercial and residential 
                customers, with proximity to industrial areas being advantageous. 
                Environmental regulations compliance is mandatory in all territories.
                """,
                'type': 'territory_info',
                'metadata': {'category': 'territory', 'source': 'manual'}
            }
        ]
        
        self.test_questions = [
            {
                'question': 'What is the investment requirement for Coochie Hydrogreen?',
                'expected_keywords': ['$150,000', '$300,000', 'investment', 'equipment', 'training'],
                'category': 'financial'
            },
            {
                'question': 'What support does Coochie Hydrogreen provide to franchisees?',
                'expected_keywords': ['training', 'support', 'assistance', 'marketing', 'check-ins'],
                'category': 'support'
            },
            {
                'question': 'What are the territory requirements?',
                'expected_keywords': ['population', '50,000', 'territory', 'exclusive', '15-mile'],
                'category': 'territory'
            }
        ]
    
    async def run_comprehensive_tests(self) -> Dict[str, Any]:
        """Run all tests and return comprehensive results"""
        logger.info("Starting comprehensive enhanced system tests")
        
        try:
            # Initialize system
            await self._test_system_initialization()
            
            # Test individual components
            await self._test_embedding_service()
            await self._test_chunking_system()
            await self._test_vector_storage()
            await self._test_search_engine()
            await self._test_reranker()
            await self._test_rag_system()
            
            # Test end-to-end workflow
            await self._test_end_to_end_workflow()
            
            # Performance benchmarks
            await self._run_performance_benchmarks()
            
            # Accuracy measurements
            await self._run_accuracy_tests()
            
            # Generate final report
            return self._generate_test_report()
            
        except Exception as e:
            logger.error(f"Comprehensive testing failed: {e}")
            return {'success': False, 'error': str(e)}
    
    async def _test_system_initialization(self):
        """Test system initialization"""
        start_time = time.time()
        
        try:
            self.system = EnhancedVectorSystem(self.config)
            await self.system.initialize()
            
            processing_time = time.time() - start_time
            
            self.test_results.append(TestResult(
                test_name="system_initialization",
                success=True,
                processing_time=processing_time,
                metadata={'components_initialized': 6}
            ))
            
            logger.info("System initialization test passed", time=processing_time)
            
        except Exception as e:
            self.test_results.append(TestResult(
                test_name="system_initialization",
                success=False,
                processing_time=time.time() - start_time,
                error=str(e)
            ))
            raise
    
    async def _test_embedding_service(self):
        """Test embedding service functionality"""
        start_time = time.time()
        
        try:
            test_text = "This is a test sentence for embedding generation."
            
            # Test single embedding
            embedding = await self.system.embedding_service.generate_embedding(test_text)
            
            # Validate embedding
            assert isinstance(embedding, list)
            assert len(embedding) > 0
            assert all(isinstance(x, (int, float)) for x in embedding)
            
            # Test batch embeddings
            test_texts = [
                "First test sentence.",
                "Second test sentence.",
                "Third test sentence."
            ]
            
            batch_embeddings = await self.system.embedding_service.generate_batch_embeddings(test_texts)
            
            assert len(batch_embeddings) == len(test_texts)
            assert all(len(emb) == len(embedding) for emb in batch_embeddings)
            
            processing_time = time.time() - start_time
            
            self.test_results.append(TestResult(
                test_name="embedding_service",
                success=True,
                processing_time=processing_time,
                metadata={
                    'embedding_dimension': len(embedding),
                    'batch_size': len(test_texts),
                    'model_used': self.config.embedding_model.value
                }
            ))
            
            logger.info("Embedding service test passed", time=processing_time)
            
        except Exception as e:
            self.test_results.append(TestResult(
                test_name="embedding_service",
                success=False,
                processing_time=time.time() - start_time,
                error=str(e)
            ))
            raise
    
    async def _test_chunking_system(self):
        """Test smart chunking system"""
        start_time = time.time()
        
        try:
            test_text = self.test_documents[0]['text']
            
            # Test chunking
            chunks = await self.system.chunker.chunk_document(
                text=test_text,
                document_type="pdf",
                document_metadata={'test': True}
            )
            
            # Validate chunks
            assert len(chunks) > 0
            assert all(chunk.text.strip() for chunk in chunks)
            assert all(chunk.metadata.token_count > 0 for chunk in chunks)
            
            processing_time = time.time() - start_time
            
            self.test_results.append(TestResult(
                test_name="chunking_system",
                success=True,
                processing_time=processing_time,
                metadata={
                    'chunks_created': len(chunks),
                    'avg_chunk_size': sum(chunk.metadata.token_count for chunk in chunks) / len(chunks),
                    'strategy_used': self.config.chunking_strategy.value
                }
            ))
            
            logger.info("Chunking system test passed", time=processing_time)
            
        except Exception as e:
            self.test_results.append(TestResult(
                test_name="chunking_system",
                success=False,
                processing_time=time.time() - start_time,
                error=str(e)
            ))
            raise
    
    async def _test_vector_storage(self):
        """Test vector storage functionality"""
        start_time = time.time()
        
        try:
            # Prepare test vectors
            test_vectors = []
            for i, doc in enumerate(self.test_documents):
                embedding = await self.system.embedding_service.generate_embedding(doc['text'])
                test_vectors.append({
                    'id': f"test_vector_{i}",
                    'document_id': doc['id'],
                    'text': doc['text'],
                    'embedding': embedding,
                    'metadata': doc['metadata']
                })
            
            # Test storage
            success = await self.system.vector_store.store_vectors(test_vectors)
            assert success
            
            processing_time = time.time() - start_time
            
            self.test_results.append(TestResult(
                test_name="vector_storage",
                success=True,
                processing_time=processing_time,
                metadata={
                    'vectors_stored': len(test_vectors),
                    'storage_backend': 'pgvector'
                }
            ))
            
            logger.info("Vector storage test passed", time=processing_time)
            
        except Exception as e:
            self.test_results.append(TestResult(
                test_name="vector_storage",
                success=False,
                processing_time=time.time() - start_time,
                error=str(e)
            ))
            raise
    
    async def _test_search_engine(self):
        """Test hybrid search engine"""
        start_time = time.time()
        
        try:
            test_query = "investment requirements"
            
            # Test different search modes
            search_modes = [SearchMode.DENSE_ONLY, SearchMode.SPARSE_ONLY, SearchMode.HYBRID]
            
            for mode in search_modes:
                results = await self.system.search_engine.search(
                    query=test_query,
                    mode=mode,
                    top_k=5
                )
                
                # Validate results
                assert isinstance(results, list)
                for result in results:
                    assert hasattr(result, 'id')
                    assert hasattr(result, 'text')
                    assert hasattr(result, 'score')
                    assert 0 <= result.score <= 1
            
            processing_time = time.time() - start_time
            
            self.test_results.append(TestResult(
                test_name="search_engine",
                success=True,
                processing_time=processing_time,
                metadata={
                    'modes_tested': len(search_modes),
                    'query_tested': test_query
                }
            ))
            
            logger.info("Search engine test passed", time=processing_time)
            
        except Exception as e:
            self.test_results.append(TestResult(
                test_name="search_engine",
                success=False,
                processing_time=time.time() - start_time,
                error=str(e)
            ))
            raise
    
    async def _test_reranker(self):
        """Test reranking system"""
        start_time = time.time()
        
        try:
            test_query = "franchise investment"
            test_documents = [doc['text'] for doc in self.test_documents]
            
            # Test reranking
            rerank_results = await self.system.reranker.rerank(
                query=test_query,
                documents=test_documents
            )
            
            # Validate results
            assert len(rerank_results) == len(test_documents)
            assert all(hasattr(result, 'score') for result in rerank_results)
            assert all(0 <= result.score <= 1 for result in rerank_results)
            
            processing_time = time.time() - start_time
            
            self.test_results.append(TestResult(
                test_name="reranker",
                success=True,
                processing_time=processing_time,
                metadata={
                    'documents_reranked': len(test_documents),
                    'model_used': self.config.reranker_model.value
                }
            ))
            
            logger.info("Reranker test passed", time=processing_time)
            
        except Exception as e:
            self.test_results.append(TestResult(
                test_name="reranker",
                success=False,
                processing_time=time.time() - start_time,
                error=str(e)
            ))
            raise
    
    async def _test_rag_system(self):
        """Test RAG system functionality"""
        start_time = time.time()
        
        try:
            test_question = "What is the investment requirement?"
            
            # Test different RAG strategies
            strategies = [RAGStrategy.BASIC, RAGStrategy.MULTI_QUERY, RAGStrategy.FUSION]
            
            for strategy in strategies:
                response = await self.system.rag_system.answer_question(
                    question=test_question,
                    strategy=strategy
                )
                
                # Validate response
                assert isinstance(response.answer, str)
                assert len(response.answer) > 0
                assert 0 <= response.confidence_score <= 1
                assert isinstance(response.sources, list)
            
            processing_time = time.time() - start_time
            
            self.test_results.append(TestResult(
                test_name="rag_system",
                success=True,
                processing_time=processing_time,
                metadata={
                    'strategies_tested': len(strategies),
                    'question_tested': test_question
                }
            ))
            
            logger.info("RAG system test passed", time=processing_time)
            
        except Exception as e:
            self.test_results.append(TestResult(
                test_name="rag_system",
                success=False,
                processing_time=time.time() - start_time,
                error=str(e)
            ))
            raise
    
    async def _test_end_to_end_workflow(self):
        """Test complete end-to-end workflow"""
        start_time = time.time()
        
        try:
            # Process documents
            for doc in self.test_documents:
                result = await self.system.process_document(
                    text=doc['text'],
                    document_id=doc['id'],
                    document_type=doc['type'],
                    metadata=doc['metadata']
                )
                assert result['success']
            
            # Answer questions
            for question_data in self.test_questions:
                answer_result = await self.system.answer_question(
                    question=question_data['question']
                )
                
                assert isinstance(answer_result['answer'], str)
                assert len(answer_result['answer']) > 0
                assert answer_result['confidence'] > 0
            
            processing_time = time.time() - start_time
            
            self.test_results.append(TestResult(
                test_name="end_to_end_workflow",
                success=True,
                processing_time=processing_time,
                metadata={
                    'documents_processed': len(self.test_documents),
                    'questions_answered': len(self.test_questions)
                }
            ))
            
            logger.info("End-to-end workflow test passed", time=processing_time)
            
        except Exception as e:
            self.test_results.append(TestResult(
                test_name="end_to_end_workflow",
                success=False,
                processing_time=time.time() - start_time,
                error=str(e)
            ))
            raise
    
    async def _run_performance_benchmarks(self):
        """Run performance benchmarks"""
        start_time = time.time()
        
        try:
            benchmark_result = await self.system.benchmark_system()
            
            processing_time = time.time() - start_time
            
            self.test_results.append(TestResult(
                test_name="performance_benchmarks",
                success=True,
                processing_time=processing_time,
                metadata=benchmark_result
            ))
            
            logger.info("Performance benchmarks completed", time=processing_time)
            
        except Exception as e:
            self.test_results.append(TestResult(
                test_name="performance_benchmarks",
                success=False,
                processing_time=time.time() - start_time,
                error=str(e)
            ))
    
    async def _run_accuracy_tests(self):
        """Run accuracy measurements"""
        start_time = time.time()
        
        try:
            total_accuracy = 0
            tested_questions = 0
            
            for question_data in self.test_questions:
                answer_result = await self.system.answer_question(
                    question=question_data['question']
                )
                
                # Calculate accuracy based on keyword presence
                answer_text = answer_result['answer'].lower()
                keywords_found = sum(
                    1 for keyword in question_data['expected_keywords']
                    if keyword.lower() in answer_text
                )
                
                accuracy = keywords_found / len(question_data['expected_keywords'])
                total_accuracy += accuracy
                tested_questions += 1
            
            avg_accuracy = total_accuracy / tested_questions if tested_questions > 0 else 0
            processing_time = time.time() - start_time
            
            self.test_results.append(TestResult(
                test_name="accuracy_tests",
                success=True,
                processing_time=processing_time,
                accuracy_score=avg_accuracy,
                metadata={
                    'questions_tested': tested_questions,
                    'average_accuracy': avg_accuracy
                }
            ))
            
            logger.info("Accuracy tests completed", 
                       time=processing_time, 
                       accuracy=avg_accuracy)
            
        except Exception as e:
            self.test_results.append(TestResult(
                test_name="accuracy_tests",
                success=False,
                processing_time=time.time() - start_time,
                error=str(e)
            ))
    
    def _generate_test_report(self) -> Dict[str, Any]:
        """Generate comprehensive test report"""
        successful_tests = [r for r in self.test_results if r.success]
        failed_tests = [r for r in self.test_results if not r.success]
        
        total_processing_time = sum(r.processing_time for r in self.test_results)
        avg_accuracy = np.mean([r.accuracy_score for r in self.test_results if r.accuracy_score > 0])
        
        report = {
            'summary': {
                'total_tests': len(self.test_results),
                'successful_tests': len(successful_tests),
                'failed_tests': len(failed_tests),
                'success_rate': len(successful_tests) / len(self.test_results) if self.test_results else 0,
                'total_processing_time': total_processing_time,
                'average_accuracy': float(avg_accuracy) if not np.isnan(avg_accuracy) else 0.0
            },
            'test_results': [
                {
                    'test_name': r.test_name,
                    'success': r.success,
                    'processing_time': r.processing_time,
                    'accuracy_score': r.accuracy_score,
                    'metadata': r.metadata,
                    'error': r.error
                }
                for r in self.test_results
            ],
            'system_configuration': {
                'embedding_model': self.config.embedding_model.value,
                'reranker_model': self.config.reranker_model.value,
                'chunking_strategy': self.config.chunking_strategy.value,
                'search_mode': self.config.search_mode.value,
                'rag_strategy': self.config.rag_strategy.value
            }
        }
        
        return report


# Pytest integration
@pytest.mark.asyncio
async def test_enhanced_vector_system():
    """Pytest test for enhanced vector system"""
    config = EnhancedSystemConfig(
        openai_api_key=os.getenv("OPENAI_API_KEY"),
        cohere_api_key=os.getenv("COHERE_API_KEY"),
        database_url=os.getenv("DATABASE_URL"),
        redis_url=os.getenv("REDIS_URL")
    )
    
    tester = EnhancedSystemTester(config)
    report = await tester.run_comprehensive_tests()
    
    assert report['summary']['success_rate'] > 0.8  # 80% success rate
    assert report['summary']['average_accuracy'] > 0.6  # 60% accuracy


if __name__ == "__main__":
    async def main():
        config = EnhancedSystemConfig(
            openai_api_key=os.getenv("OPENAI_API_KEY"),
            cohere_api_key=os.getenv("COHERE_API_KEY"),
            database_url=os.getenv("DATABASE_URL"),
            redis_url=os.getenv("REDIS_URL")
        )
        
        tester = EnhancedSystemTester(config)
        report = await tester.run_comprehensive_tests()
        
        print(json.dumps(report, indent=2))
    
    asyncio.run(main())
