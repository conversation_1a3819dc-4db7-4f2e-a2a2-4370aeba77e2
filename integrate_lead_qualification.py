#!/usr/bin/env python3
"""
Integrate Enhanced Lead Qualification

This script integrates the enhanced lead qualification system with your existing
FastAPI application database, extending the current lead/question system.
"""

import os
import sys
import json
import uuid
from datetime import datetime
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import sqlalchemy as sa
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

def get_database_url():
    """Get database URL from environment"""
    db_url = os.getenv('DATABASE_URL')
    if db_url and 'asyncpg' in db_url:
        db_url = db_url.replace('postgresql+asyncpg://', 'postgresql://')
    return db_url or "postgresql://postgres:root@localhost:5432/growthhive"

def add_enhanced_qualification_columns(engine):
    """Add enhanced qualification columns to existing tables"""
    
    print("🔧 Adding enhanced qualification columns...")
    
    with engine.connect() as conn:
        # Add columns to existing questions table
        try:
            conn.execute(text("""
                ALTER TABLE questions 
                ADD COLUMN IF NOT EXISTS expected_answers JSONB,
                ADD COLUMN IF NOT EXISTS score_weight INTEGER DEFAULT 10,
                ADD COLUMN IF NOT EXISTS category VARCHAR(50) DEFAULT 'General'
            """))
            print("   ✅ Enhanced questions table")
        except Exception as e:
            print(f"   ⚠️  Questions table: {e}")
        
        # Add columns to existing lead_responses table
        try:
            conn.execute(text("""
                ALTER TABLE lead_responses 
                ADD COLUMN IF NOT EXISTS is_qualified BOOLEAN DEFAULT FALSE,
                ADD COLUMN IF NOT EXISTS score_awarded INTEGER DEFAULT 0,
                ADD COLUMN IF NOT EXISTS confidence_score FLOAT DEFAULT 0.0,
                ADD COLUMN IF NOT EXISTS response_time_seconds INTEGER
            """))
            print("   ✅ Enhanced lead_responses table")
        except Exception as e:
            print(f"   ⚠️  Lead responses table: {e}")
        
        # Add columns to existing leads table
        try:
            conn.execute(text("""
                ALTER TABLE leads 
                ADD COLUMN IF NOT EXISTS total_qualification_score INTEGER DEFAULT 0,
                ADD COLUMN IF NOT EXISTS max_possible_score INTEGER DEFAULT 0,
                ADD COLUMN IF NOT EXISTS qualification_percentage FLOAT DEFAULT 0.0,
                ADD COLUMN IF NOT EXISTS is_fully_qualified BOOLEAN DEFAULT FALSE,
                ADD COLUMN IF NOT EXISTS qualification_level VARCHAR(20) DEFAULT 'unqualified',
                ADD COLUMN IF NOT EXISTS qualified_at TIMESTAMP WITH TIME ZONE
            """))
            print("   ✅ Enhanced leads table")
        except Exception as e:
            print(f"   ⚠️  Leads table: {e}")
        
        conn.commit()

def create_qualification_session_table(engine):
    """Create qualification session table for session management"""
    
    print("📋 Creating qualification session table...")
    
    with engine.connect() as conn:
        try:
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS qualification_sessions (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    lead_id UUID REFERENCES leads(id),
                    session_token VARCHAR(255) UNIQUE NOT NULL,
                    status VARCHAR(20) DEFAULT 'active',
                    current_question_index INTEGER DEFAULT 0,
                    questions_order JSONB NOT NULL,
                    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    completed_at TIMESTAMP WITH TIME ZONE,
                    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
                    user_agent VARCHAR(500),
                    ip_address VARCHAR(45),
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                )
            """))
            
            # Create indexes
            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_qualification_sessions_lead 
                ON qualification_sessions(lead_id)
            """))
            
            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_qualification_sessions_token 
                ON qualification_sessions(session_token)
            """))
            
            print("   ✅ Qualification sessions table created")
        except Exception as e:
            print(f"   ⚠️  Session table: {e}")
        
        conn.commit()

def populate_enhanced_questions(engine):
    """Populate the existing questions table with enhanced qualification data"""
    
    print("📝 Populating enhanced qualification questions...")
    
    # Enhanced questions with scoring and expected answers
    enhanced_questions = [
        {
            "question_text": "What is your available investment budget for this franchise opportunity?",
            "question_internal_text": "Investment budget assessment for franchise qualification",
            "question_type": "financial",
            "category": "Financial",
            "expected_answers": ["$50,000-$100,000", "$100,000-$200,000", "$200,000+"],
            "score_weight": 15,
            "order_sequence": 1
        },
        {
            "question_text": "Are you comfortable with ongoing royalty payments of 10% of monthly revenue?",
            "question_internal_text": "Royalty payment acceptance assessment",
            "question_type": "financial",
            "category": "Financial", 
            "expected_answers": ["Yes", "Absolutely", "I understand and accept"],
            "score_weight": 10,
            "order_sequence": 2
        },
        {
            "question_text": "Do you have a specific geographic area in mind for your franchise?",
            "question_internal_text": "Geographic area preference assessment",
            "question_type": "location",
            "category": "Location",
            "expected_answers": ["Yes, I have identified an area", "I have some areas in mind", "I need help selecting"],
            "score_weight": 8,
            "order_sequence": 3
        },
        {
            "question_text": "Are you willing to operate in suburban residential areas?",
            "question_internal_text": "Suburban operation willingness assessment",
            "question_type": "location",
            "category": "Location",
            "expected_answers": ["Yes", "Definitely", "That's my preference"],
            "score_weight": 7,
            "order_sequence": 4
        },
        {
            "question_text": "Are you available to complete a 4-week training program within the first 12 months?",
            "question_internal_text": "Training program commitment assessment",
            "question_type": "training",
            "category": "Training",
            "expected_answers": ["Yes", "Absolutely", "I can commit to that"],
            "score_weight": 12,
            "order_sequence": 5
        },
        {
            "question_text": "Do you have experience with lawn care or similar outdoor services?",
            "question_internal_text": "Industry experience assessment",
            "question_type": "experience",
            "category": "Training",
            "expected_answers": ["Yes, extensive experience", "Some experience", "No, but willing to learn"],
            "score_weight": 6,
            "order_sequence": 6
        },
        {
            "question_text": "Do you have previous business ownership or management experience?",
            "question_internal_text": "Business experience assessment",
            "question_type": "experience",
            "category": "Experience",
            "expected_answers": ["Yes, I own/owned a business", "Yes, management experience", "No, but eager to learn"],
            "score_weight": 8,
            "order_sequence": 7
        },
        {
            "question_text": "Can you dedicate full-time hours to operating this franchise?",
            "question_internal_text": "Time commitment assessment",
            "question_type": "commitment",
            "category": "Commitment",
            "expected_answers": ["Yes, full-time commitment", "Yes, this will be my primary focus"],
            "score_weight": 10,
            "order_sequence": 8
        },
        {
            "question_text": "What level of ongoing support do you expect from the franchisor?",
            "question_internal_text": "Support expectations assessment",
            "question_type": "support",
            "category": "Support",
            "expected_answers": ["Regular check-ins and guidance", "Training and marketing support", "Comprehensive ongoing support"],
            "score_weight": 6,
            "order_sequence": 9
        },
        {
            "question_text": "What are your long-term goals with this franchise opportunity?",
            "question_internal_text": "Long-term goals assessment",
            "question_type": "goals",
            "category": "Goals",
            "expected_answers": ["Build a sustainable business", "Achieve financial independence", "Expand to multiple territories"],
            "score_weight": 8,
            "order_sequence": 10
        }
    ]
    
    with engine.connect() as conn:
        # Clear existing questions (optional - comment out if you want to keep existing)
        # conn.execute(text("DELETE FROM questions WHERE question_type IN ('financial', 'location', 'training', 'experience', 'commitment', 'support', 'goals')"))
        
        for q_data in enhanced_questions:
            # Check if question already exists
            result = conn.execute(text("""
                SELECT id FROM questions 
                WHERE question_text = :text AND question_type = :type
            """), {
                "text": q_data["question_text"],
                "type": q_data["question_type"]
            })
            
            if result.fetchone():
                print(f"   ⚠️  Question already exists: {q_data['question_type']}")
                continue
            
            # Insert new question
            question_id = str(uuid.uuid4())
            conn.execute(text("""
                INSERT INTO questions 
                (id, question_text, question_internal_text, question_type, category, 
                 expected_answers, score_weight, order_sequence, is_active, is_deleted, created_at, updated_at)
                VALUES (:id, :text, :internal_text, :type, :category, :expected_answers, 
                        :score_weight, :order_seq, :is_active, :is_deleted, :created_at, :updated_at)
            """), {
                "id": question_id,
                "text": q_data["question_text"],
                "internal_text": q_data["question_internal_text"],
                "type": q_data["question_type"],
                "category": q_data["category"],
                "expected_answers": json.dumps(q_data["expected_answers"]),
                "score_weight": q_data["score_weight"],
                "order_seq": q_data["order_sequence"],
                "is_active": True,
                "is_deleted": False,
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            })
            
            print(f"   ➕ Added question: {q_data['category']} - {q_data['question_type']}")
        
        conn.commit()
    
    print(f"✅ Enhanced questions populated")

def create_test_lead_with_responses(engine):
    """Create a test lead with sample responses to demonstrate the system"""
    
    print("👤 Creating test lead with sample responses...")
    
    with engine.connect() as conn:
        # Create test lead
        lead_id = str(uuid.uuid4())
        conn.execute(text("""
            INSERT INTO leads 
            (id, full_name, email, phone, location, lead_source, qualification_status, status, 
             is_active, is_deleted, created_at, updated_at)
            VALUES (:id, :name, :email, :phone, :location, :source, :qual_status, :status,
                    :is_active, :is_deleted, :created_at, :updated_at)
        """), {
            "id": lead_id,
            "name": "Alex Johnson",
            "email": "<EMAIL>",
            "phone": "******-0123",
            "location": "Sydney, NSW",
            "source": "website",
            "qual_status": "qualified",
            "status": "new",
            "is_active": True,
            "is_deleted": False,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        })
        
        # Get questions
        result = conn.execute(text("""
            SELECT id, question_text, expected_answers, score_weight
            FROM questions 
            WHERE is_active = true AND expected_answers IS NOT NULL
            ORDER BY order_sequence
            LIMIT 5
        """))
        
        questions = result.fetchall()
        
        # Sample answers that should score well
        sample_answers = [
            "$150,000",  # Financial
            "Yes, I understand",  # Financial
            "I have some areas in mind",  # Location
            "Yes",  # Location
            "I can commit to that"  # Training
        ]
        
        total_score = 0
        max_score = 0
        
        for i, (question, answer) in enumerate(zip(questions, sample_answers)):
            q_id, q_text, expected_answers, score_weight = question
            
            # Simple scoring logic
            expected_list = json.loads(expected_answers) if isinstance(expected_answers, str) else expected_answers
            is_qualified = any(answer.lower() in exp.lower() or exp.lower() in answer.lower() for exp in expected_list)
            score_awarded = score_weight if is_qualified else int(score_weight * 0.5)  # Partial credit
            
            # Insert response
            response_id = str(uuid.uuid4())
            conn.execute(text("""
                INSERT INTO lead_responses 
                (id, lead_id, question_id, response_text, is_qualified, score_awarded, 
                 confidence_score, answered_at, created_at, updated_at)
                VALUES (:id, :lead_id, :q_id, :response, :qualified, :score, :confidence,
                        :answered_at, :created_at, :updated_at)
            """), {
                "id": response_id,
                "lead_id": lead_id,
                "q_id": q_id,
                "response": answer,
                "qualified": is_qualified,
                "score": score_awarded,
                "confidence": 0.8 if is_qualified else 0.5,
                "answered_at": datetime.utcnow(),
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            })
            
            total_score += score_awarded
            max_score += score_weight
            
            print(f"   ➕ Response: {answer} → {score_awarded}/{score_weight} pts")
        
        # Update lead with qualification results
        percentage = (total_score / max_score * 100) if max_score > 0 else 0
        is_qualified = percentage >= 80
        qualification_level = "qualified" if is_qualified else "partially_qualified"
        
        conn.execute(text("""
            UPDATE leads 
            SET total_qualification_score = :total_score,
                max_possible_score = :max_score,
                qualification_percentage = :percentage,
                is_fully_qualified = :is_qualified,
                qualification_level = :level,
                qualified_at = :qualified_at
            WHERE id = :lead_id
        """), {
            "total_score": total_score,
            "max_score": max_score,
            "percentage": percentage,
            "is_qualified": is_qualified,
            "level": qualification_level,
            "qualified_at": datetime.utcnow(),
            "lead_id": lead_id
        })
        
        conn.commit()
        
        print(f"   ✅ Test lead created: {total_score}/{max_score} ({percentage:.1f}%)")
        print(f"   📊 Status: {'QUALIFIED' if is_qualified else 'NOT QUALIFIED'}")
        
        return lead_id

def verify_integration(engine):
    """Verify the integration was successful"""
    
    print("🔍 Verifying integration...")
    
    with engine.connect() as conn:
        # Check enhanced questions
        result = conn.execute(text("""
            SELECT COUNT(*) FROM questions 
            WHERE expected_answers IS NOT NULL AND score_weight IS NOT NULL
        """))
        enhanced_questions = result.fetchone()[0]
        
        # Check lead responses with scoring
        result = conn.execute(text("""
            SELECT COUNT(*) FROM lead_responses 
            WHERE score_awarded IS NOT NULL
        """))
        scored_responses = result.fetchone()[0]
        
        # Check qualified leads
        result = conn.execute(text("""
            SELECT COUNT(*) FROM leads 
            WHERE qualification_percentage IS NOT NULL AND qualification_percentage > 0
        """))
        qualified_leads = result.fetchone()[0]
        
        print(f"   📝 Enhanced questions: {enhanced_questions}")
        print(f"   💬 Scored responses: {scored_responses}")
        print(f"   👥 Qualified leads: {qualified_leads}")
        
        # Show sample data
        if qualified_leads > 0:
            result = conn.execute(text("""
                SELECT full_name, email, total_qualification_score, max_possible_score, 
                       qualification_percentage, qualification_level
                FROM leads 
                WHERE qualification_percentage > 0
                LIMIT 3
            """))
            
            print(f"\n📊 Sample qualified leads:")
            for row in result.fetchall():
                name, email, total, max_score, percentage, level = row
                print(f"   • {name} ({email}): {total}/{max_score} ({percentage:.1f}%) - {level}")

def main():
    """Main integration function"""
    
    print("🚀 INTEGRATING ENHANCED LEAD QUALIFICATION")
    print("=" * 70)
    
    # Get database connection
    db_url = get_database_url()
    engine = create_engine(db_url)
    
    try:
        # Test connection
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        print("✅ Database connection successful")
        
        # Add enhanced columns to existing tables
        add_enhanced_qualification_columns(engine)
        
        # Create session management table
        create_qualification_session_table(engine)
        
        # Populate enhanced questions
        populate_enhanced_questions(engine)
        
        # Create test lead with responses
        test_lead_id = create_test_lead_with_responses(engine)
        
        # Verify integration
        verify_integration(engine)
        
        print(f"\n🎉 INTEGRATION COMPLETED SUCCESSFULLY!")
        print(f"\n✅ Your existing FastAPI application now has:")
        print(f"   • Enhanced question scoring system")
        print(f"   • Lead response evaluation with confidence scoring")
        print(f"   • Automatic qualification percentage calculation")
        print(f"   • Session management for qualification workflows")
        print(f"   • Test data to demonstrate functionality")
        
        print(f"\n🔍 Next steps:")
        print(f"   1. Check your database: psql -h localhost -U postgres -d growthhive")
        print(f"   2. Query lead responses: SELECT * FROM lead_responses WHERE score_awarded > 0;")
        print(f"   3. View qualified leads: SELECT * FROM leads WHERE is_fully_qualified = true;")
        print(f"   4. Start your FastAPI server: uvicorn app.main:app --reload")
        
    except Exception as e:
        print(f"❌ Integration failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
