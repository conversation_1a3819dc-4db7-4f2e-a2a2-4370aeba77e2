"""Add lead qualification tables

Revision ID: 005_add_lead_qualification_tables
Revises: 004_add_document_processing_status
Create Date: 2025-07-14 05:35:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '005_add_lead_qualification_tables'
down_revision = '004_add_document_processing_status'
branch_labels = None
depends_on = None


def upgrade():
    # Create leads table
    op.create_table('leads',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('email', sa.String(length=255), nullable=False),
        sa.Column('first_name', sa.String(length=100), nullable=True),
        sa.Column('last_name', sa.String(length=100), nullable=True),
        sa.Column('phone', sa.String(length=20), nullable=True),
        sa.Column('location', sa.String(length=255), nullable=True),
        sa.Column('source', sa.String(length=100), nullable=True),
        sa.Column('status', sa.String(length=50), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_leads_email'), 'leads', ['email'], unique=False)
    op.create_index('idx_leads_status', 'leads', ['status'], unique=False)

    # Create pre_qualification_questions table
    op.create_table('pre_qualification_questions',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('question_id', sa.String(length=10), nullable=False),
        sa.Column('category', sa.String(length=50), nullable=False),
        sa.Column('question_text', sa.Text(), nullable=False),
        sa.Column('expected_answers', postgresql.JSON(astext_type=sa.Text()), nullable=False),
        sa.Column('score_weight', sa.Integer(), nullable=False),
        sa.Column('context_info', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_pre_qualification_questions_category'), 'pre_qualification_questions', ['category'], unique=False)
    op.create_index(op.f('ix_pre_qualification_questions_question_id'), 'pre_qualification_questions', ['question_id'], unique=True)
    op.create_index('idx_pre_qualification_questions_category', 'pre_qualification_questions', ['category'], unique=False)

    # Create lead_responses table
    op.create_table('lead_responses',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('lead_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('question_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('question_text', sa.Text(), nullable=False),
        sa.Column('lead_answer', sa.Text(), nullable=False),
        sa.Column('is_qualified', sa.Boolean(), nullable=False),
        sa.Column('score_awarded', sa.Integer(), nullable=False),
        sa.Column('confidence_score', sa.Float(), nullable=True),
        sa.Column('response_time_seconds', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['lead_id'], ['leads.id'], ),
        sa.ForeignKeyConstraint(['question_id'], ['pre_qualification_questions.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_lead_responses_lead_question', 'lead_responses', ['lead_id', 'question_id'], unique=False)

    # Create lead_qualification_summaries table
    op.create_table('lead_qualification_summaries',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('lead_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('total_score', sa.Integer(), nullable=False),
        sa.Column('max_possible_score', sa.Integer(), nullable=False),
        sa.Column('percentage_score', sa.Float(), nullable=False),
        sa.Column('is_fully_qualified', sa.Boolean(), nullable=False),
        sa.Column('qualification_level', sa.String(length=20), nullable=False),
        sa.Column('questions_answered', sa.Integer(), nullable=False),
        sa.Column('total_questions', sa.Integer(), nullable=False),
        sa.Column('average_response_time', sa.Float(), nullable=True),
        sa.Column('qualification_notes', sa.Text(), nullable=True),
        sa.Column('qualified_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['lead_id'], ['leads.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('lead_id')
    )
    op.create_index('idx_qualification_summaries_qualified', 'lead_qualification_summaries', ['is_fully_qualified'], unique=False)

    # Create qualification_sessions table
    op.create_table('qualification_sessions',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('lead_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('session_token', sa.String(length=255), nullable=False),
        sa.Column('status', sa.String(length=20), nullable=False),
        sa.Column('current_question_index', sa.Integer(), nullable=False),
        sa.Column('questions_order', postgresql.JSON(astext_type=sa.Text()), nullable=False),
        sa.Column('started_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('expires_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('user_agent', sa.String(length=500), nullable=True),
        sa.Column('ip_address', sa.String(length=45), nullable=True),
        sa.ForeignKeyConstraint(['lead_id'], ['leads.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_qualification_sessions_lead', 'qualification_sessions', ['lead_id'], unique=False)
    op.create_index('idx_qualification_sessions_token', 'qualification_sessions', ['session_token'], unique=True)

    # Create question_templates table
    op.create_table('question_templates',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('template_name', sa.String(length=100), nullable=False),
        sa.Column('franchise_type', sa.String(length=100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('question_ids', postgresql.JSON(astext_type=sa.Text()), nullable=False),
        sa.Column('total_possible_score', sa.Integer(), nullable=False),
        sa.Column('qualification_threshold', sa.Integer(), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('template_name')
    )

    # Create qualification_analytics table
    op.create_table('qualification_analytics',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('date', sa.DateTime(timezone=True), nullable=False),
        sa.Column('total_leads', sa.Integer(), nullable=True),
        sa.Column('qualified_leads', sa.Integer(), nullable=True),
        sa.Column('qualification_rate', sa.Float(), nullable=True),
        sa.Column('average_score', sa.Float(), nullable=True),
        sa.Column('average_completion_time', sa.Float(), nullable=True),
        sa.Column('most_difficult_question_id', sa.String(length=10), nullable=True),
        sa.Column('easiest_question_id', sa.String(length=10), nullable=True),
        sa.Column('common_failure_categories', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('insights', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_qualification_analytics_date', 'qualification_analytics', ['date'], unique=False)


def downgrade():
    # Drop tables in reverse order
    op.drop_index('idx_qualification_analytics_date', table_name='qualification_analytics')
    op.drop_table('qualification_analytics')
    op.drop_table('question_templates')
    op.drop_index('idx_qualification_sessions_token', table_name='qualification_sessions')
    op.drop_index('idx_qualification_sessions_lead', table_name='qualification_sessions')
    op.drop_table('qualification_sessions')
    op.drop_index('idx_qualification_summaries_qualified', table_name='lead_qualification_summaries')
    op.drop_table('lead_qualification_summaries')
    op.drop_index('idx_lead_responses_lead_question', table_name='lead_responses')
    op.drop_table('lead_responses')
    op.drop_index('idx_pre_qualification_questions_category', table_name='pre_qualification_questions')
    op.drop_index(op.f('ix_pre_qualification_questions_question_id'), table_name='pre_qualification_questions')
    op.drop_index(op.f('ix_pre_qualification_questions_category'), table_name='pre_qualification_questions')
    op.drop_table('pre_qualification_questions')
    op.drop_index('idx_leads_status', table_name='leads')
    op.drop_index(op.f('ix_leads_email'), table_name='leads')
    op.drop_table('leads')
