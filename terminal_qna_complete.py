#!/usr/bin/env python3
"""
Complete Terminal QnA Interface

Enhanced terminal interface that provides COMPLETE, comprehensive answers
without truncation for questions about the Coochie Information Pack.
"""

import asyncio
import os
import sys
import time
from pathlib import Path
import logging

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging to be less verbose for terminal use
logging.basicConfig(level=logging.WARNING)

# Configuration
PDF_PATH = "Coochie_Information pack.pdf"

class CompleteTerminalQnA:
    """Terminal-based QnA interface with complete answers"""
    
    def __init__(self):
        self.processor = None
        self.document_result = None
        self.is_ready = False
    
    async def initialize(self):
        """Initialize the enhanced document processor"""
        print("🚀 Initializing Complete QnA System...")
        print("=" * 50)
        
        try:
            from docqa.advanced_ingestion.layout_aware_processor import LayoutAwareProcessor, AdvancedConfig
            
            # Create optimized configuration for complete answers
            config = AdvancedConfig(
                use_layoutparser=True,
                use_tesseract=True,
                use_easyocr=True,
                use_gpt4_vision=True,
                use_gpt4_text_enhancement=True,
                analyze_document_structure=True,
                extract_key_entities=True,
                enhance_text_quality=True,
                preserve_reading_order=True,
                merge_text_blocks=True,
                use_layout_aware_chunking=True,
                respect_section_boundaries=True,
                chunk_size=1500,  # Larger chunks for more complete context
                chunk_overlap=300,
                parallel_processing=False,
                max_workers=2,
                timeout_seconds=300,
                extract_tables=False,
                extract_images=True,
                analyze_charts=True
            )
            
            # Initialize processor
            self.processor = LayoutAwareProcessor(
                openai_api_key=os.getenv('OPENAI_API_KEY', ''),
                config=config
            )
            
            if not os.path.exists(PDF_PATH):
                print(f"❌ PDF file not found: {PDF_PATH}")
                return False
            
            print(f"📄 Processing document: {PDF_PATH}")
            print("⏳ This may take 30-60 seconds...")
            
            start_time = time.time()
            
            # Process document
            self.document_result = await self.processor.process_document(Path(PDF_PATH))
            
            processing_time = time.time() - start_time
            
            print(f"✅ Document processed in {processing_time:.1f}s")
            print(f"   📝 Text extracted: {len(self.document_result.structured_text):,} characters")
            print(f"   🏗️  Layout elements: {len(self.document_result.layout_elements)}")
            print(f"   🧩 Semantic chunks: {len(self.document_result.semantic_chunks)}")
            
            self.is_ready = True
            return True
            
        except Exception as e:
            print(f"❌ Initialization failed: {e}")
            return False
    
    def answer_question(self, question: str) -> str:
        """Answer a question using the enhanced content with COMPLETE responses"""
        if not self.is_ready:
            return "❌ System not ready. Please wait for initialization to complete."
        
        try:
            # Find ALL relevant chunks (not just top 3)
            relevant_chunks = self.find_relevant_chunks(question)
            
            # Generate comprehensive answer
            if relevant_chunks:
                # Use more chunks for complete context
                context = "\n\n".join([chunk['content'] for chunk in relevant_chunks[:8]])
                answer = self.extract_comprehensive_answer(question, context)
            else:
                # Fallback to full text search with more content
                context = self.find_comprehensive_text_sections(question)
                answer = self.extract_comprehensive_answer(question, context)
            
            return answer
            
        except Exception as e:
            return f"❌ Error generating answer: {e}"
    
    def find_relevant_chunks(self, question: str) -> list:
        """Find ALL relevant semantic chunks for comprehensive answers"""
        question_lower = question.lower()
        question_keywords = self.extract_keywords(question_lower)
        
        scored_chunks = []
        
        for chunk in self.document_result.semantic_chunks:
            content = chunk.get('content', '').lower()
            score = 0
            
            # Score based on keyword matches
            for keyword in question_keywords:
                if keyword in content:
                    score += content.count(keyword) * 3  # Higher weight
            
            # Bonus for exact phrase matches
            if any(phrase in content for phrase in [question_lower[:30], question_lower[-30:]]):
                score += 10
            
            # Bonus for question-specific terms
            if any(term in content for term in ['training', 'support', 'fee', 'cost', 'business', 'franchise']):
                score += 5
            
            if score > 0:
                scored_chunks.append((score, chunk))
        
        # Return MORE chunks sorted by relevance for complete answers
        scored_chunks.sort(key=lambda x: x[0], reverse=True)
        return [chunk for score, chunk in scored_chunks[:10]]  # Top 10 instead of 5
    
    def find_comprehensive_text_sections(self, question: str) -> str:
        """Find comprehensive sections in the full text"""
        question_lower = question.lower()
        keywords = self.extract_keywords(question_lower)
        
        sentences = self.document_result.structured_text.split('.')
        relevant_sentences = []
        
        for sentence in sentences:
            sentence_lower = sentence.lower()
            score = sum(3 for keyword in keywords if keyword in sentence_lower)
            
            if score > 0:
                relevant_sentences.append((score, sentence.strip()))
        
        # Sort by relevance and return MORE sentences for complete context
        relevant_sentences.sort(key=lambda x: x[0], reverse=True)
        return '. '.join([sentence for score, sentence in relevant_sentences[:15]])  # More sentences
    
    def extract_keywords(self, text: str) -> list:
        """Extract keywords from question"""
        stop_words = {'what', 'is', 'the', 'how', 'does', 'are', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'a', 'an'}
        words = text.split()
        keywords = [word.strip('?.,!') for word in words if word.lower() not in stop_words and len(word) > 2]
        return keywords
    
    def extract_comprehensive_answer(self, question: str, context: str) -> str:
        """Extract COMPREHENSIVE answer from context without truncation"""
        if not context or len(context.strip()) < 10:
            return "❌ I couldn't find specific information about this question in the document."
        
        question_lower = question.lower()
        
        # Financial questions - COMPLETE information
        if any(word in question_lower for word in ['fee', 'cost', 'investment', 'price', 'money', 'royalty']):
            import re
            money_pattern = r'\$[\d,]+(?:\.\d{2})?'
            percentage_pattern = r'\d+(?:\.\d+)?%'
            money_matches = re.findall(money_pattern, context)
            percentage_matches = re.findall(percentage_pattern, context)
            
            answer = f"💰 COMPLETE FINANCIAL INFORMATION:\n\n"
            
            if money_matches:
                answer += f"💵 Financial Amounts Found:\n"
                for amount in set(money_matches):
                    answer += f"   • {amount}\n"
                answer += "\n"
            
            if percentage_matches:
                answer += f"📊 Percentages Found:\n"
                for percent in set(percentage_matches):
                    answer += f"   • {percent}\n"
                answer += "\n"
            
            # Add full context without truncation
            answer += f"📄 COMPLETE DETAILS:\n{context}"
            return answer
        
        # Training/Support questions - COMPLETE information
        if any(word in question_lower for word in ['training', 'support', 'help', 'assistance', 'program']):
            answer = f"🤝 COMPLETE TRAINING & SUPPORT INFORMATION:\n\n"
            
            # Extract training-specific information
            training_info = []
            support_info = []
            
            sentences = context.split('.')
            for sentence in sentences:
                sentence_lower = sentence.lower()
                if any(word in sentence_lower for word in ['training', 'program', 'course', 'education']):
                    training_info.append(sentence.strip())
                if any(word in sentence_lower for word in ['support', 'assistance', 'help', 'service']):
                    support_info.append(sentence.strip())
            
            if training_info:
                answer += f"🎓 TRAINING PROGRAMS:\n"
                for info in training_info[:5]:
                    if info:
                        answer += f"   • {info}.\n"
                answer += "\n"
            
            if support_info:
                answer += f"🆘 SUPPORT SERVICES:\n"
                for info in support_info[:5]:
                    if info:
                        answer += f"   • {info}.\n"
                answer += "\n"
            
            # Add full context
            answer += f"📄 COMPLETE DETAILS:\n{context}"
            return answer
        
        # Business model questions - COMPLETE information
        if any(word in question_lower for word in ['business model', 'hydrogreen', 'how does', 'work', 'operate']):
            answer = f"🏢 COMPLETE BUSINESS MODEL INFORMATION:\n\n"
            
            # Extract business model components
            model_info = []
            operation_info = []
            
            sentences = context.split('.')
            for sentence in sentences:
                sentence_lower = sentence.lower()
                if any(word in sentence_lower for word in ['business model', 'model', 'system', 'approach']):
                    model_info.append(sentence.strip())
                if any(word in sentence_lower for word in ['operate', 'work', 'function', 'service']):
                    operation_info.append(sentence.strip())
            
            if model_info:
                answer += f"📋 BUSINESS MODEL:\n"
                for info in model_info[:3]:
                    if info:
                        answer += f"   • {info}.\n"
                answer += "\n"
            
            if operation_info:
                answer += f"⚙️ OPERATIONS:\n"
                for info in operation_info[:3]:
                    if info:
                        answer += f"   • {info}.\n"
                answer += "\n"
            
            # Add full context
            answer += f"📄 COMPLETE DETAILS:\n{context}"
            return answer
        
        # Territory questions - COMPLETE information
        if any(word in question_lower for word in ['territory', 'area', 'coverage', 'location', 'region']):
            answer = f"📍 COMPLETE TERRITORY INFORMATION:\n\n"
            answer += f"📄 COMPLETE DETAILS:\n{context}"
            return answer
        
        # Benefits questions - COMPLETE information
        if any(word in question_lower for word in ['benefit', 'advantage', 'why choose', 'reason']):
            answer = f"✨ COMPLETE BENEFITS INFORMATION:\n\n"
            
            # Extract benefits
            benefits = []
            sentences = context.split('.')
            for sentence in sentences:
                sentence_lower = sentence.lower()
                if any(word in sentence_lower for word in ['benefit', 'advantage', 'excellent', 'unique', 'superior']):
                    benefits.append(sentence.strip())
            
            if benefits:
                answer += f"🌟 KEY BENEFITS:\n"
                for benefit in benefits[:5]:
                    if benefit:
                        answer += f"   • {benefit}.\n"
                answer += "\n"
            
            # Add full context
            answer += f"📄 COMPLETE DETAILS:\n{context}"
            return answer
        
        # Default comprehensive answer
        answer = f"📄 COMPLETE ANSWER:\n\n{context}"
        return answer
    
    def show_help(self):
        """Show help information"""
        help_text = """
🤖 COMPLETE COOCHIE QnA SYSTEM - HELP
=" * 50

✨ ENHANCED FEATURES:
  • COMPLETE answers without truncation
  • Comprehensive context from multiple sources
  • Detailed financial, training, and business information
  • Enhanced keyword matching and relevance scoring

Available Commands:
  help, h          - Show this help message
  quit, exit, q    - Exit the QnA system
  stats            - Show document processing statistics
  examples         - Show example questions

Question Categories:
  💰 Financial     - fees, costs, investment, royalties (COMPLETE details)
  🤝 Support       - training, support, assistance (COMPREHENSIVE info)
  🏢 Business      - business model, operations (FULL explanation)
  📍 Territory     - area coverage, location (COMPLETE coverage)
  ✨ Benefits      - advantages, why choose (ALL benefits)
  🔧 Service       - lawn care program, treatments (DETAILED info)

Tips for COMPLETE Answers:
  • Ask specific questions for detailed responses
  • Use keywords like "fee", "training", "business model"
  • System provides FULL context without truncation
  • Enhanced processing analyzes 41,000+ characters
  • Multiple relevant sections combined for completeness

Example Questions:
  "What is the complete franchise fee structure?"
  "What training and support is provided in detail?"
  "How does the business model work completely?"
  "What are all the ongoing costs?"
"""
        print(help_text)
    
    def show_stats(self):
        """Show document processing statistics"""
        if not self.is_ready:
            print("❌ System not ready yet.")
            return
        
        stats = f"""
📊 COMPLETE DOCUMENT PROCESSING STATISTICS:

📄 Document: {PDF_PATH}
📝 Text Content:
   • Raw text: {len(self.document_result.raw_text):,} characters
   • Enhanced text: {len(self.document_result.structured_text):,} characters
   • Improvement: {len(self.document_result.structured_text) / max(len(self.document_result.raw_text), 1):.1f}x

🏗️  Layout Analysis:
   • Layout elements: {len(self.document_result.layout_elements)}
   • Images: {len(self.document_result.images)}
   • Charts: {len(self.document_result.charts)}

🧩 Semantic Processing:
   • Semantic chunks: {len(self.document_result.semantic_chunks)}
   • Average chunk size: {sum(len(chunk['content']) for chunk in self.document_result.semantic_chunks) // len(self.document_result.semantic_chunks) if self.document_result.semantic_chunks else 0} characters
   • Enhanced for COMPLETE answers

🎯 System Status: READY FOR COMPLETE QUESTIONS
✨ Answer Quality: COMPREHENSIVE & COMPLETE
"""
        print(stats)
    
    async def run_interactive_session(self):
        """Run the interactive QnA session with complete answers"""
        print("\n🤖 COMPLETE COOCHIE QnA SYSTEM - INTERACTIVE MODE")
        print("=" * 60)
        print("Ask questions and get COMPLETE, comprehensive answers")
        print("Commands: 'help' for help, 'quit' to exit")
        print("=" * 60)
        
        while True:
            try:
                # Get user input
                question = input("\n❓ Your Question: ").strip()
                
                if not question:
                    continue
                
                # Handle commands
                if question.lower() in ['quit', 'exit', 'q']:
                    print("👋 Goodbye! Thanks for using the Complete Coochie QnA System!")
                    break
                elif question.lower() in ['help', 'h']:
                    self.show_help()
                    continue
                elif question.lower() == 'stats':
                    self.show_stats()
                    continue
                
                # Process question
                print("\n🔍 Processing your question for COMPLETE answer...")
                start_time = time.time()
                
                answer = self.answer_question(question)
                
                response_time = time.time() - start_time
                
                print(f"\n🤖 COMPLETE Answer (responded in {response_time:.1f}s):")
                print("=" * 80)
                print(answer)
                print("=" * 80)
                
            except KeyboardInterrupt:
                print("\n\n👋 Goodbye! Thanks for using the Complete Coochie QnA System!")
                break
            except Exception as e:
                print(f"\n❌ Error: {e}")

async def main():
    """Main function"""
    print("🚀 COMPLETE COOCHIE QnA TERMINAL INTERFACE")
    print("=" * 60)
    print("✨ Enhanced for COMPLETE, comprehensive answers")
    print("=" * 60)
    
    # Check prerequisites
    if not os.getenv('OPENAI_API_KEY'):
        print("❌ OPENAI_API_KEY environment variable not set")
        sys.exit(1)
    
    if not os.path.exists(PDF_PATH):
        print(f"❌ PDF file not found: {PDF_PATH}")
        sys.exit(1)
    
    # Initialize complete QnA system
    qna = CompleteTerminalQnA()
    
    if await qna.initialize():
        print("\n✅ System ready! Ask questions for COMPLETE answers.")
        await qna.run_interactive_session()
    else:
        print("❌ Failed to initialize complete QnA system")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
