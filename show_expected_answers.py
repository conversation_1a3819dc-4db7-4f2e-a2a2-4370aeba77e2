#!/usr/bin/env python3
"""
Show Expected Answers from Database

Display the expected answers for each qualification question.
"""

import os
import json
import sqlalchemy as sa
from sqlalchemy import create_engine

def get_database_url():
    """Get database URL from environment"""
    db_url = os.getenv('DATABASE_URL')
    if db_url and 'asyncpg' in db_url:
        db_url = db_url.replace('postgresql+asyncpg://', 'postgresql://')
    return db_url or "postgresql://postgres:root@localhost:5432/growthhive"

def show_expected_answers():
    """Show expected answers from the database"""
    
    print("📝 LEAD QUALIFICATION EXPECTED ANSWERS")
    print("=" * 70)
    
    engine = create_engine(get_database_url())
    
    with engine.connect() as conn:
        result = conn.execute(sa.text("""
            SELECT question_id, category, question_text, expected_answers, score_weight
            FROM pre_qualification_questions 
            WHERE is_active = true
            ORDER BY question_id
        """))
        
        total_score = 0
        
        for row in result.fetchall():
            question_id, category, question_text, expected_answers, score_weight = row
            
            # Parse JSON if it's a string
            if isinstance(expected_answers, str):
                expected_answers = json.loads(expected_answers)
            
            print(f"\n{question_id} - {category} ({score_weight} points)")
            print(f"❓ {question_text}")
            print(f"✅ Expected Answers:")
            for i, answer in enumerate(expected_answers, 1):
                print(f"   {i}. \"{answer}\"")
            
            total_score += score_weight
        
        threshold = int(total_score * 0.8)
        print(f"\n📊 SCORING SUMMARY:")
        print(f"   Total Possible Score: {total_score} points")
        print(f"   Qualification Threshold: {threshold} points (80%)")
        print(f"   Questions: {len(list(result.fetchall())) if hasattr(result, 'fetchall') else 'Multiple'}")

if __name__ == "__main__":
    show_expected_answers()
