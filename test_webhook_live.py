#!/usr/bin/env python3
"""
Live Webhook Testing

Test the actual running webhook endpoint with real HTTP requests.
"""

import requests
import json
import time
from datetime import datetime

def test_webhook_endpoint():
    """Test the live webhook endpoint"""
    
    print("🚀 LIVE WEBHOOK ENDPOINT TESTING")
    print("=" * 60)
    
    webhook_url = 'http://localhost:8000/api/sms/kudosity/sms-inbound'
    
    # Test scenarios
    test_scenarios = [
        {
            "name": "Initial Interest",
            "message": "Hello, I am interested in your franchise opportunity"
        },
        {
            "name": "Positive Response",
            "message": "Yes, this is a good time to chat"
        },
        {
            "name": "Investment Question",
            "message": "What is the investment required?"
        },
        {
            "name": "Training Question", 
            "message": "What kind of training do you provide?"
        },
        {
            "name": "Scheduling Request",
            "message": "I'd like to schedule a meeting"
        }
    ]
    
    results = []
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n📱 Test {i}: {scenario['name']}")
        print(f"Message: \"{scenario['message']}\"")
        
        # Create payload
        payload = {
            'event_type': 'SMS_INBOUND',
            'message_id': f'live_test_{i}_{int(time.time())}',
            'from_number': f'+61-400-LIVE-{i:03d}',
            'to_number': '+61-400-COMPANY',
            'message_text': scenario['message'],
            'timestamp': datetime.utcnow().isoformat() + 'Z',
            'metadata': {
                'carrier': 'Telstra',
                'country': 'AU',
                'test': True,
                'scenario': scenario['name']
            }
        }
        
        try:
            # Send request
            response = requests.post(
                webhook_url,
                json=payload,
                headers={'Content-Type': 'application/json'},
                timeout=30
            )
            
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    print(f"✅ Success: {result.get('success', 'Unknown')}")
                    
                    agent_response = result.get('agent_response', 'No response')
                    if agent_response and agent_response != 'No response':
                        print(f"🤖 Agent: {agent_response}")
                    else:
                        print(f"❌ No agent response")
                    
                    stage = result.get('conversation_stage', 'Unknown')
                    print(f"📊 Stage: {stage}")
                    
                    results.append({
                        'scenario': scenario['name'],
                        'success': result.get('success', False),
                        'has_response': bool(agent_response and agent_response != 'No response'),
                        'stage': stage
                    })
                    
                except json.JSONDecodeError:
                    print(f"❌ Invalid JSON response: {response.text}")
                    results.append({
                        'scenario': scenario['name'],
                        'success': False,
                        'error': 'Invalid JSON'
                    })
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                print(f"Response: {response.text}")
                results.append({
                    'scenario': scenario['name'],
                    'success': False,
                    'error': f'HTTP {response.status_code}'
                })
                
        except Exception as e:
            print(f"❌ Request failed: {e}")
            results.append({
                'scenario': scenario['name'],
                'success': False,
                'error': str(e)
            })
        
        # Wait between requests
        time.sleep(2)
    
    # Summary
    print(f"\n🎉 LIVE WEBHOOK TESTING SUMMARY")
    print("=" * 50)
    
    successful = sum(1 for r in results if r.get('success', False))
    total = len(results)
    
    print(f"Tests completed: {successful}/{total} successful")
    
    for result in results:
        status = "✅" if result.get('success', False) else "❌"
        print(f"   {status} {result['scenario']}")
        
        if 'error' in result:
            print(f"      Error: {result['error']}")
        elif result.get('has_response'):
            print(f"      Stage: {result.get('stage', 'Unknown')}")
    
    if successful > 0:
        print(f"\n🎯 WEBHOOK ENDPOINT IS WORKING!")
        print(f"✅ {successful} out of {total} scenarios processed successfully")
        print(f"📱 Ready for Kudosity integration")
        
        if successful < total:
            print(f"\n⚠️  Some scenarios had issues:")
            print(f"   • Check server logs for AsyncSession errors")
            print(f"   • Database connection may need adjustment")
            print(f"   • Agent response generation may need fixes")
    else:
        print(f"\n❌ WEBHOOK ENDPOINT NEEDS ATTENTION")
        print(f"All test scenarios failed")
    
    return successful == total

def test_conversation_status():
    """Test conversation status endpoint"""
    
    print(f"\n📊 Testing conversation status endpoint...")
    
    status_url = 'http://localhost:8000/api/sms/conversation/+61-400-LIVE-001'
    
    try:
        response = requests.get(status_url, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Status endpoint working")
            
            if result.get('exists'):
                print(f"   Conversation found")
                print(f"   Stage: {result.get('current_stage', 'Unknown')}")
                print(f"   Messages: {len(result.get('recent_messages', []))}")
            else:
                print(f"   No conversation found (expected for first test)")
        else:
            print(f"❌ Status endpoint failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Status endpoint error: {e}")

def main():
    """Main test function"""
    
    print("🔥 LIVE WEBHOOK ENDPOINT TESTING")
    print("Testing the actual running FastAPI server webhook endpoint")
    print("=" * 70)
    
    # Test server connectivity
    try:
        response = requests.get('http://localhost:8000/', timeout=5)
        if response.status_code == 200:
            print("✅ Server is accessible")
        else:
            print(f"❌ Server returned: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Server not accessible: {e}")
        print("💡 Make sure FastAPI server is running:")
        print("   uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload")
        return False
    
    # Test webhook endpoint
    success = test_webhook_endpoint()
    
    # Test conversation status
    test_conversation_status()
    
    if success:
        print(f"\n🎉 LIVE WEBHOOK TESTING SUCCESSFUL!")
        print(f"Your SMS webhook endpoint is working and ready for Kudosity!")
    else:
        print(f"\n⚠️  LIVE WEBHOOK TESTING INCOMPLETE")
        print(f"Some issues need to be resolved before production use.")
    
    return success

if __name__ == "__main__":
    main()
