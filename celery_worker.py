#!/usr/bin/env python3
"""
Celery Worker Script for GrowthHive
Starts Celery workers for background task processing
"""

import os
import sys
import argparse
from app.core.celery_app import celery_app

def main():
    """Main function to start Celery worker"""
    parser = argparse.ArgumentParser(description='Start Celery worker for GrowthHive')
    parser.add_argument('--queues', default='document_processing,high_priority,medium_priority', 
                       help='Comma-separated list of queues to process')
    parser.add_argument('--concurrency', type=int, default=2, 
                       help='Number of concurrent worker processes')
    parser.add_argument('--loglevel', default='info', 
                       help='Logging level (debug, info, warning, error)')
    
    args = parser.parse_args()
    
    # Start the Celery worker
    celery_app.worker_main([
        'worker',
        f'--queues={args.queues}',
        f'--concurrency={args.concurrency}',
        f'--loglevel={args.loglevel}',
        '--without-gossip',
        '--without-mingle',
        '--without-heartbeat'
    ])

if __name__ == '__main__':
    main()
