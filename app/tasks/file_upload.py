"""
File Upload Background Tasks
Tasks for handling file uploads and processing in the background
"""

import asyncio
import io
from typing import Dict, Any, Optional
from datetime import datetime, timezone

import structlog
from celery import Task
from fastapi import UploadFile

from app.core.celery_app import celery_app
from app.core.config.settings import settings

logger = structlog.get_logger(__name__)


class FileUploadCallbackTask(Task):
    """Custom Celery task with callback support for file uploads"""
    
    def on_success(self, retval, task_id, args, kwargs):
        """Called when task succeeds"""
        logger.info("File upload task completed successfully",
                   task_id=task_id,
                   result=retval)
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """Called when task fails"""
        logger.error("File upload task failed",
                    task_id=task_id,
                    error=str(exc),
                    traceback=str(einfo))


@celery_app.task(
    bind=True,
    base=FileUploadCallbackTask,
    name="app.tasks.file_upload.process_franchisor_brochure_upload_task",
    queue=settings.DOCUMENT_PROCESSING_QUEUE,
    max_retries=settings.DOCUMENT_PROCESSING_MAX_RETRIES,
    default_retry_delay=settings.DOCUMENT_PROCESSING_RETRY_DELAY,
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 3, 'countdown': 60}
)
def process_franchisor_brochure_upload_task(
    self,
    franchisor_id: str,
    filename: str,
    file_content: bytes,
    user_id: str
) -> Dict[str, Any]:
    """
    Process complete franchisor brochure upload workflow in background
    
    Args:
        franchisor_id: Franchisor ID
        filename: Original filename
        file_content: File content as bytes
        user_id: User ID who initiated the upload
        
    Returns:
        Dict with upload and processing results
    """
    try:
        logger.info("Starting franchisor brochure upload workflow",
                   franchisor_id=franchisor_id,
                   filename=filename,
                   task_id=self.request.id)
        
        # Update task status to processing
        _update_upload_status_sync(
            task_id=self.request.id,
            status="uploading",
            progress=10,
            message="Starting file upload to S3...",
            started_at=datetime.now(timezone.utc)
        )
        
        # Process upload workflow
        result = asyncio.run(_process_franchisor_brochure_upload(
            franchisor_id=franchisor_id,
            filename=filename,
            file_content=file_content,
            user_id=user_id,
            task_id=self.request.id
        ))
        
        if result["success"]:
            logger.info("Franchisor brochure upload workflow completed successfully",
                       franchisor_id=franchisor_id,
                       task_id=self.request.id)
            
            # Update final status
            _update_upload_status_sync(
                task_id=self.request.id,
                status="completed",
                progress=100,
                message="Upload and processing completed successfully",
                completed_at=datetime.now(timezone.utc)
            )
        else:
            logger.error("Franchisor brochure upload workflow failed",
                        franchisor_id=franchisor_id,
                        error=result.get("error", "Unknown error"),
                        task_id=self.request.id)
            
            # Update error status
            _update_upload_status_sync(
                task_id=self.request.id,
                status="failed",
                progress=100,
                message=f"Upload failed: {result.get('error', 'Unknown error')}",
                error=result.get("error"),
                completed_at=datetime.now(timezone.utc)
            )
        
        return result
        
    except Exception as e:
        logger.error("Franchisor brochure upload workflow task failed",
                    franchisor_id=franchisor_id,
                    error=str(e),
                    task_id=self.request.id)
        
        # Update error status
        _update_upload_status_sync(
            task_id=self.request.id,
            status="failed",
            progress=100,
            message=f"Task failed: {str(e)}",
            error=str(e),
            completed_at=datetime.now(timezone.utc)
        )
        
        return {
            "success": False,
            "error": str(e),
            "franchisor_id": franchisor_id,
            "task_id": self.request.id
        }


async def _process_franchisor_brochure_upload(
    franchisor_id: str,
    filename: str,
    file_content: bytes,
    user_id: str,
    task_id: str
) -> Dict[str, Any]:
    """Process complete franchisor brochure upload workflow"""
    try:
        from app.services.s3_service import S3Service
        from app.services.franchisor_service import get_franchisor_service
        from app.services.background_task_manager import get_background_task_manager
        from app.core.config.settings import settings
        
        # Step 1: Upload file to S3 (30% progress)
        _update_upload_status_sync(
            task_id=task_id,
            status="uploading",
            progress=30,
            message="Uploading file to S3..."
        )
        
        # Create UploadFile-like object from bytes
        file_obj = io.BytesIO(file_content)
        file_obj.name = filename
        
        # Upload to S3
        s3_service = S3Service()
        brochure_filename = await s3_service.upload_file_from_bytes(
            file_content=file_content,
            filename=filename,
            prefix="brochures"
        )
        
        # Step 2: Update franchisor record (50% progress)
        _update_upload_status_sync(
            task_id=task_id,
            status="updating_database",
            progress=50,
            message="Updating franchisor record..."
        )
        
        franchise_service = get_franchisor_service()
        updated_franchisor = await franchise_service.update_brochure_url(franchisor_id, brochure_filename)
        
        if not updated_franchisor:
            return {
                "success": False,
                "error": "Failed to update franchisor with brochure filename",
                "franchisor_id": franchisor_id
            }
        
        # Step 3: Queue document processing (70% progress)
        _update_upload_status_sync(
            task_id=task_id,
            status="queuing_processing",
            progress=70,
            message="Queuing document processing..."
        )
        
        # Construct S3 URL for document processing
        s3_base_url = settings.S3_BASE_URL or f"https://{settings.S3_BUCKET_NAME}.s3.{settings.AWS_REGION}.amazonaws.com"
        document_url = f"{s3_base_url}/{brochure_filename}"
        
        # Queue DocQA processing
        task_manager = get_background_task_manager()
        processing_task_id = task_manager.queue_docqa_processing(
            franchisor_id=franchisor_id,
            brochure_url=document_url,
            processing_options={
                "document_type": "brochure",
                "source": "async_brochure_upload",
                "user_id": user_id,
                "filename": filename,
                "upload_task_id": task_id
            }
        )
        
        # Step 4: Complete (100% progress)
        _update_upload_status_sync(
            task_id=task_id,
            status="processing_queued",
            progress=90,
            message="Document processing queued successfully"
        )
        
        return {
            "success": True,
            "franchisor_id": franchisor_id,
            "brochure_filename": brochure_filename,
            "brochure_url": document_url,
            "processing_task_id": processing_task_id,
            "upload_task_id": task_id
        }
        
    except Exception as e:
        logger.error("Franchisor brochure upload workflow failed",
                    franchisor_id=franchisor_id,
                    error=str(e))
        return {
            "success": False,
            "error": str(e),
            "franchisor_id": franchisor_id
        }


def _update_upload_status_sync(
    task_id: str,
    status: str,
    progress: int,
    message: str,
    error: Optional[str] = None,
    started_at: Optional[datetime] = None,
    completed_at: Optional[datetime] = None
):
    """Update upload task status synchronously (for use in Celery tasks)"""
    try:
        # This would typically update Redis or database with task status
        # For now, just log the status update
        logger.info("Upload task status update",
                   task_id=task_id,
                   status=status,
                   progress=progress,
                   message=message,
                   error=error)
        
        # TODO: Implement actual status storage in Redis
        # redis_client = get_redis_client()
        # redis_client.hset(f"upload_task:{task_id}", mapping={
        #     "status": status,
        #     "progress": progress,
        #     "message": message,
        #     "error": error or "",
        #     "updated_at": datetime.now(timezone.utc).isoformat()
        # })
        
    except Exception as e:
        logger.error("Failed to update upload task status",
                    task_id=task_id,
                    error=str(e))
