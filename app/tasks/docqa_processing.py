"""
DocQA Processing Celery Tasks
Background tasks for franchisor brochure processing
"""

import asyncio
from typing import Optional, Dict, Any

from celery import Task
import structlog

from app.core.celery_app import celery_app
from app.core.config.settings import settings

logger = structlog.get_logger(__name__)


class DocQACallbackTask(Task):
    """Base task class for DocQA processing"""
    
    def on_success(self, retval, task_id, args, kwargs):
        """Called when task succeeds"""
        logger.info("DocQA task completed successfully", 
                   task_id=task_id, result=retval)
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """Called when task fails"""
        logger.error("DocQA task failed", 
                    task_id=task_id, error=str(exc), traceback=str(einfo))


@celery_app.task(
    bind=True,
    base=DocQACallbackTask,
    name="app.tasks.docqa_processing.process_docqa_task",
    queue=settings.DOCUMENT_PROCESSING_QUEUE,
    max_retries=settings.DOCUMENT_PROCESSING_MAX_RETRIES,
    default_retry_delay=settings.DOCUMENT_PROCESSING_RETRY_DELAY,
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 3, 'countdown': 60}
)
def process_docqa_task(
    self,
    franchisor_id: str,
    brochure_url: str,
    processing_options: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Process franchisor brochure with DocQA in background thread
    
    Args:
        franchisor_id: Franchisor ID
        brochure_url: S3 URL of the brochure
        processing_options: Optional processing configuration
        
    Returns:
        Dict with processing results
    """
    import time
    start_time = time.time()

    try:
        logger.info("📋 CELERY TASK STARTED - Franchisor Brochure Processing",
                   franchisor_id=franchisor_id,
                   brochure_url=brochure_url,
                   task_id=self.request.id,
                   processing_options=processing_options)
        
        # Process brochure with Enhanced DocQA service
        logger.info("🧠 Starting enhanced franchisor brochure processing",
                   franchisor_id=franchisor_id,
                   system="enhanced-docqa")

        result = asyncio.run(_process_franchisor_brochure(
            franchisor_id=franchisor_id,
            brochure_url=brochure_url,
            processing_options=processing_options or {}
        ))
        
        processing_time = time.time() - start_time

        if result["success"]:
            logger.info("🎉 CELERY TASK COMPLETED - Franchisor Brochure Processing Success",
                       franchisor_id=franchisor_id,
                       task_id=self.request.id,
                       processing_time=f"{processing_time:.2f}s",
                       chunks_created=result.get('chunks_created', 0),
                       enhanced_system_used=result.get('enhanced_system_used', False),
                       system_version=result.get('system_version', 'unknown'))
        else:
            logger.error("❌ CELERY TASK FAILED - Franchisor Brochure Processing Error",
                        franchisor_id=franchisor_id,
                        task_id=self.request.id,
                        processing_time=f"{processing_time:.2f}s",
                        error=result.get("error", "Unknown error"),
                        enhanced_system_used=result.get('enhanced_system_used', False))
        
        return result
        
    except Exception as exc:
        logger.error("DocQA processing task failed",
                    franchisor_id=franchisor_id,
                    error=str(exc),
                    task_id=self.request.id)
        
        # Retry if not max retries reached
        if self.request.retries < self.max_retries:
            logger.info("Retrying DocQA processing task",
                       franchisor_id=franchisor_id,
                       retry_count=self.request.retries + 1,
                       task_id=self.request.id)
            raise self.retry(exc=exc)
        
        raise exc


async def _process_franchisor_brochure(
    franchisor_id: str,
    brochure_url: str,
    processing_options: Dict[str, Any]
) -> Dict[str, Any]:
    """Process franchisor brochure using Enhanced DocQA service"""
    try:
        # Try enhanced system first
        from app.services.enhanced_docqa_integration_service import get_enhanced_docqa_integration_service

        enhanced_service = get_enhanced_docqa_integration_service()

        # Process brochure with enhanced system
        result = await enhanced_service.process_franchisor_brochure_enhanced(
            franchisor_id=franchisor_id,
            brochure_url=brochure_url
        )
        
        if result and result.success:
            return {
                "success": True,
                "chunks_created": getattr(result, 'chunks_created', 0),
                "processing_time": getattr(result, 'processing_time', None),
                "franchisor_id": franchisor_id
            }
        else:
            return {
                "success": False,
                "error": "DocQA brochure processing failed or returned no result",
                "franchisor_id": franchisor_id
            }
            
    except Exception as e:
        logger.error("DocQA brochure processing failed",
                    franchisor_id=franchisor_id,
                    error=str(e))
        return {
            "success": False,
            "error": str(e),
            "franchisor_id": franchisor_id
        }
