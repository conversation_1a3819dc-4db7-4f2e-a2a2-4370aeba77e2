"""
Document Processing Celery Tasks
Background tasks for document upload and processing
"""

import asyncio
from datetime import datetime, timezone
from typing import Optional, Dict, Any

from celery import Task
import structlog

from app.core.celery_app import celery_app
from app.core.config.settings import settings

logger = structlog.get_logger(__name__)


class CallbackTask(Task):
    """Base task class with callback support"""
    
    def on_success(self, retval, task_id, args, kwargs):
        """Called when task succeeds"""
        logger.info("Task completed successfully", 
                   task_id=task_id, result=retval)
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """Called when task fails"""
        logger.error("Task failed", 
                    task_id=task_id, error=str(exc), traceback=str(einfo))
    
    def on_retry(self, exc, task_id, args, kwargs, einfo):
        """Called when task is retried"""
        logger.warning("Task retrying", 
                      task_id=task_id, error=str(exc), retry_count=self.request.retries)


@celery_app.task(
    bind=True,
    base=CallbackTask,
    name="app.tasks.document_processing.process_document_task",
    queue=settings.DOCUMENT_PROCESSING_QUEUE,
    max_retries=settings.DOCUMENT_PROCESSING_MAX_RETRIES,
    default_retry_delay=settings.DOCUMENT_PROCESSING_RETRY_DELAY,
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 3, 'countdown': 60}
)
def process_document_task(
    self,
    document_id: str,
    file_url: str,
    processing_options: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Process document in background thread
    
    Args:
        document_id: Document ID to process
        file_url: S3 URL of the document
        processing_options: Optional processing configuration
        
    Returns:
        Dict with processing results
    """
    import time
    start_time = time.time()

    try:
        logger.info("📋 CELERY TASK STARTED - Document Processing",
                   document_id=document_id,
                   file_url=file_url,
                   task_id=self.request.id,
                   processing_options=processing_options)

        # Check if document is already being processed
        current_status = _get_document_status_sync(document_id)
        if current_status and current_status.get("processing_status") == "processing":
            logger.info("Document is already being processed, skipping",
                       document_id=document_id,
                       current_status=current_status.get("processing_status"))
            return {
                "success": False,
                "error": "Document is already being processed",
                "document_id": document_id,
                "status": "already_processing"
            }

        # Update task status to processing
        _update_document_status_sync(
            document_id=document_id,
            status="processing",
            progress=0,
            message="Starting document processing...",
            task_id=self.request.id,
            started_at=datetime.now(timezone.utc)
        )
        
        # Process document with Enhanced DocQA service
        logger.info("🧠 Starting enhanced document processing",
                   document_id=document_id,
                   system="enhanced-docqa")

        result = asyncio.run(_process_document_with_docqa(
            document_id=document_id,
            file_url=file_url,
            processing_options=processing_options or {}
        ))

        processing_time = time.time() - start_time

        if result["success"]:
            # Update status to completed
            _update_document_status_sync(
                document_id=document_id,
                status="processed",
                progress=100,
                message=f"Successfully processed. Created {result.get('chunks_created', 0)} chunks.",
                completed_at=datetime.now(timezone.utc)
            )

            logger.info("🎉 CELERY TASK COMPLETED - Document Processing Success",
                       document_id=document_id,
                       task_id=self.request.id,
                       processing_time=f"{processing_time:.2f}s",
                       chunks_created=result.get('chunks_created', 0),
                       enhanced_system_used=result.get('enhanced_system_used', False),
                       system_version=result.get('system_version', 'unknown'))
        else:
            # Update status to failed
            error_msg = result.get("error", "Unknown processing error")
            _update_document_status_sync(
                document_id=document_id,
                status="failed",
                progress=100,
                message="Processing failed",
                error=error_msg,
                completed_at=datetime.now(timezone.utc)
            )
            
            logger.error("❌ CELERY TASK FAILED - Document Processing Error",
                        document_id=document_id,
                        task_id=self.request.id,
                        processing_time=f"{processing_time:.2f}s",
                        error=error_msg,
                        enhanced_system_used=result.get('enhanced_system_used', False))
        
        return result
        
    except Exception as exc:
        logger.error("Document processing task failed",
                    document_id=document_id,
                    error=str(exc),
                    task_id=self.request.id)
        
        # Update status to failed
        _update_document_status_sync(
            document_id=document_id,
            status="failed",
            progress=100,
            message="Processing failed due to error",
            error=str(exc),
            completed_at=datetime.now(timezone.utc)
        )
        
        # Retry if not max retries reached
        if self.request.retries < self.max_retries:
            logger.info("Retrying document processing task",
                       document_id=document_id,
                       retry_count=self.request.retries + 1,
                       task_id=self.request.id)
            raise self.retry(exc=exc)
        
        raise exc


def _get_document_status_sync(document_id: str) -> Optional[Dict[str, Any]]:
    """Get document processing status from database (synchronous version for Celery)"""
    try:
        from sqlalchemy import create_engine, text
        from sqlalchemy.orm import sessionmaker
        from app.core.config.settings import settings
        import uuid

        # Create a synchronous database connection for Celery workers
        sync_database_url = settings.DATABASE_URL.replace("postgresql+asyncpg://", "postgresql://")
        sync_engine = create_engine(sync_database_url)
        SyncSessionLocal = sessionmaker(bind=sync_engine)

        with SyncSessionLocal() as session:
            query = text("""
                SELECT processing_status, processing_progress, processing_message,
                       processing_error, processing_started_at, processing_completed_at
                FROM documents
                WHERE id = :document_id AND is_deleted = false
            """)

            result = session.execute(query, {"document_id": uuid.UUID(document_id)})
            row = result.first()

            if row:
                return {
                    "processing_status": row.processing_status,
                    "processing_progress": row.processing_progress,
                    "processing_message": row.processing_message,
                    "processing_error": row.processing_error,
                    "processing_started_at": row.processing_started_at,
                    "processing_completed_at": row.processing_completed_at
                }
            return None

    except Exception as e:
        logger.error("Failed to get document status",
                    document_id=document_id,
                    error=str(e))
        return None


def _update_document_status_sync(
    document_id: str,
    status: str,
    progress: int = None,
    message: str = None,
    error: str = None,
    task_id: str = None,
    started_at: datetime = None,
    completed_at: datetime = None
):
    """Update document processing status in database (synchronous version for Celery)"""
    try:
        from sqlalchemy import create_engine, text
        from sqlalchemy.orm import sessionmaker
        from app.core.config.settings import settings
        import uuid

        # Create a synchronous database connection for Celery workers
        sync_database_url = settings.DATABASE_URL.replace("postgresql+asyncpg://", "postgresql://")
        sync_engine = create_engine(sync_database_url)
        SyncSessionLocal = sessionmaker(bind=sync_engine)

        with SyncSessionLocal() as session:
            # Build the update query dynamically
            update_fields = ["updated_at = :updated_at", "processing_status = :processing_status"]
            params = {
                "updated_at": datetime.now(timezone.utc),
                "processing_status": status,
                "document_id": uuid.UUID(document_id)
            }

            if progress is not None:
                update_fields.append("processing_progress = :processing_progress")
                params["processing_progress"] = progress

            if message is not None:
                update_fields.append("processing_message = :processing_message")
                params["processing_message"] = message

            if error is not None:
                update_fields.append("processing_error = :processing_error")
                params["processing_error"] = error

            if started_at is not None:
                update_fields.append("processing_started_at = :processing_started_at")
                params["processing_started_at"] = started_at

            if completed_at is not None:
                update_fields.append("processing_completed_at = :processing_completed_at")
                params["processing_completed_at"] = completed_at

            # Execute the update query
            query = text(f"""
                UPDATE documents
                SET {', '.join(update_fields)}
                WHERE id = :document_id AND is_deleted = false
            """)

            result = session.execute(query, params)
            session.commit()

            logger.info("Document status updated successfully",
                       document_id=document_id,
                       status=status,
                       rows_affected=result.rowcount)

    except Exception as e:
        logger.error("Failed to update document status",
                    document_id=document_id,
                    error=str(e))


async def _process_document_with_docqa(
    document_id: str,
    file_url: str,
    processing_options: Dict[str, Any]
) -> Dict[str, Any]:
    """Process document using Enhanced DocQA service with fallback"""
    try:
        # Try enhanced system first
        from app.services.enhanced_docqa_integration_service import get_enhanced_docqa_integration_service

        enhanced_service = get_enhanced_docqa_integration_service()

        # Process document with enhanced system
        result = await enhanced_service.process_document_with_enhanced_pipeline(
            document_id=document_id,
            file_url=file_url
        )

        if result and result.get('success'):
            return {
                "success": True,
                "chunks_created": result.get('chunks_created', 0),
                "processing_time": result.get('processing_time'),
                "document_id": document_id,
                "enhanced_system_used": result.get('enhanced_system_used', False),
                "system_version": result.get('system_version', 'enhanced')
            }
        else:
            return {
                "success": False,
                "error": result.get('error', 'Enhanced DocQA processing failed'),
                "document_id": document_id,
                "enhanced_system_used": result.get('enhanced_system_used', False)
            }

    except Exception as e:
        logger.error(f"Enhanced DocQA processing failed, trying fallback: {e}")

        # Fallback to current system
        try:
            from app.services.docqa_integration_service import get_docqa_integration_service

            docqa_service = get_docqa_integration_service()

            # Process document with current system
            result = await docqa_service.process_document(
                document_id=document_id,
                file_url=file_url
            )

            if result and result.success:
                return {
                    "success": True,
                    "chunks_created": result.chunks_created,
                    "processing_time": getattr(result, 'processing_time', None),
                    "document_id": document_id,
                    "enhanced_system_used": False,
                    "system_version": "current",
                    "fallback_reason": str(e)
                }
            else:
                return {
                    "success": False,
                    "error": "Both enhanced and current DocQA processing failed",
                    "document_id": document_id,
                    "enhanced_system_used": False
                }

        except Exception as fallback_error:
            logger.error(f"Fallback DocQA processing also failed: {fallback_error}")
            return {
                "success": False,
                "error": f"All processing methods failed: {e}, {fallback_error}",
                "document_id": document_id,
                "enhanced_system_used": False
            }
            
    except Exception as e:
        logger.error("DocQA processing failed",
                    document_id=document_id,
                    error=str(e))
        return {
            "success": False,
            "error": str(e),
            "document_id": document_id
        }
