"""
Question Answering Agent
Handles questions about documents and franchise information using RAG
"""

from typing import Dict, Any, List
import structlog
from langchain_core.messages import AIMessage

from .base import BaseAgent, AgentState
from .tools.registry import tool_registry

logger = structlog.get_logger()


class QuestionAnsweringAgent(BaseAgent):
    """
    Agent responsible for answering questions based on ingested documents
    Uses RAG (Retrieval Augmented Generation) for accurate responses
    """
    
    def _initialize_tools(self):
        """Initialize QA-specific tools"""
        tool_names = ["search_documents", "retrieve_memory", "create_communication"]
        self.tools = tool_registry.get_tools_by_names(tool_names)
    
    async def process_state(self, state: AgentState) -> AgentState:
        """Process question answering requests"""
        try:
            user_input = state.get("user_input", "")
            context = state.get("context", {})
            lead_id = state.get("lead_id")
            
            # Search for relevant documents
            search_results = await self._search_documents(user_input, context)
            state["search_results"] = search_results
            
            # Generate answer using RAG
            answer = await self._generate_answer(user_input, search_results, context)
            
            # Check if answer is satisfactory or if escalation is needed
            needs_escalation = await self._check_escalation_needed(user_input, answer)
            
            if needs_escalation:
                answer = await self._handle_escalation(user_input, answer)
                state["next_action"] = "continue"  # May need to route to meeting booking
            else:
                state["next_action"] = "end"
            
            # Update state
            state["response"] = answer
            state["messages"] = state.get("messages", []) + [AIMessage(content=answer)]
            
            # Log communication if lead exists
            if lead_id:
                await self._log_communication(lead_id, user_input, answer)
            
            logger.info("Question answered successfully")
            return state
            
        except Exception as e:
            logger.error(f"Error in question answering agent: {str(e)}")
            state["error"] = str(e)
            return state
    
    async def _search_documents(self, question: str, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Search for relevant documents using DocQA system"""
        try:
            # Use DocQA central API directly for better integration
            from docqa.central_api import ask_question

            logger.info(f"Searching documents for question: {question[:100]}")

            # Get franchisor_id from context if available
            franchisor_id = context.get("franchisor_id")

            # Prepare request for DocQA
            request = {
                "question": question,
                "top_k": 5,
                "similarity_threshold": 0.7,
                "include_metadata": True
            }

            if franchisor_id:
                request["franchisor_id"] = franchisor_id

            # Use DocQA to search for relevant content
            results = await ask_question(request)

            if results and hasattr(results, 'sources') and results.sources:
                # Convert DocQA results to agent format
                search_results = []
                for source in results.sources:
                    search_results.append({
                        "content": source.get("content", ""),
                        "source": source.get("source", "Unknown"),
                        "similarity": source.get("similarity_score", 0.0),
                        "metadata": source.get("metadata", {})
                    })

                logger.info(f"Found {len(search_results)} relevant documents")
                return search_results
            else:
                logger.warning("No relevant documents found")
                return []

        except Exception as e:
            logger.error(f"Error searching documents: {str(e)}")
            return []
    
    async def _enhance_search_query(self, question: str, context: Dict[str, Any]) -> str:
        """Enhance search query with context and synonyms"""
        enhancement_prompt = f"""
        Original question: "{question}"
        Context: {context}
        
        Enhance this search query to better find relevant franchise information.
        Consider:
        - Franchise-specific terminology
        - Investment and financial terms
        - Business operation terms
        - Location and territory terms
        
        Return only the enhanced search query.
        """
        
        response = await self.llm.ainvoke(enhancement_prompt)
        return response.content.strip()
    
    async def _generate_answer(self, question: str, search_results: List[Dict[str, Any]], context: Dict[str, Any]) -> str:
        """Generate answer using RAG approach with Coochie Hydrogreen context"""
        if not search_results:
            return await self._handle_no_results(question)

        # Prepare context from search results
        context_text = "\n\n".join([
            f"Source: {result.get('source', 'Unknown')} (Similarity: {result.get('similarity', 0.0):.2f})\n{result.get('content', '')}"
            for result in search_results[:5]  # Use top 5 results for better context
        ])

        # Get franchisor name from context
        franchisor_name = context.get("franchisor_name", "Coochie Hydrogreen")

        rag_prompt = f"""
        You are a knowledgeable franchise consultant specializing in {franchisor_name}. Answer the user's question based on the provided context from the franchise documentation.

        Question: "{question}"

        Context from {franchisor_name} franchise documents:
        {context_text}

        Instructions:
        1. Answer based SPECIFICALLY on the provided context from {franchisor_name} documents
        2. Be detailed and specific - cite exact information, numbers, and requirements when available
        3. If the context contains specific fees, costs, or requirements, include them in your answer
        4. Reference the source documents when providing information
        5. If the context doesn't fully answer the question, acknowledge what information is available and what might need clarification
        6. Keep the response professional, informative, and helpful
        7. If investment amounts or fees are mentioned, provide the specific figures from the documents
        8. End with an offer to help with additional questions about {franchisor_name}

        Provide a comprehensive answer based on the {franchisor_name} documentation:
        """

        response = await self.llm.ainvoke(rag_prompt)
        return response.content.strip()
    
    async def _handle_no_results(self, question: str) -> str:
        """Handle cases where no relevant documents are found"""
        no_results_prompt = f"""
        The user asked: "{question}"
        
        No relevant documents were found to answer this question.
        
        Provide a helpful response that:
        1. Acknowledges you don't have specific information about their question
        2. Suggests they might want to speak with a franchise consultant
        3. Offers to help with other franchise-related questions
        4. Provides an option to schedule a consultation
        
        Be professional and helpful.
        """
        
        response = await self.llm.ainvoke(no_results_prompt)
        return response.content.strip()
    
    async def _check_escalation_needed(self, question: str, answer: str) -> bool:
        """Check if the question/answer requires escalation to human consultant"""
        escalation_prompt = f"""
        Question: "{question}"
        Answer provided: "{answer}"
        
        Determine if this interaction should be escalated to a human consultant.
        
        Escalate if:
        - Question involves specific investment amounts or financial details
        - User is asking about next steps or ready to proceed
        - Question is complex and answer may be insufficient
        - User seems highly interested and qualified
        
        Respond with only "YES" or "NO".
        """
        
        response = await self.llm.ainvoke(escalation_prompt)
        return response.content.strip().upper() == "YES"
    
    async def _handle_escalation(self, question: str, current_answer: str) -> str:
        """Handle escalation by suggesting consultation"""
        escalation_response = f"""
        {current_answer}
        
        For more detailed information and personalized guidance, I'd recommend speaking with one of our franchise consultants. They can provide specific details about investment requirements, territory availability, and help you determine if this opportunity is right for you.
        
        Would you like me to schedule a consultation for you?
        """
        
        return escalation_response
    
    async def _log_communication(self, lead_id: str, question: str, answer: str):
        """Log the Q&A interaction as communication"""
        try:
            comm_tool = tool_registry.get_tool("create_communication")
            if comm_tool:
                communication_data = {
                    "lead_id": lead_id,
                    "communication_type": "ai_interaction",
                    "subject": "Document Q&A",
                    "content": f"Q: {question}\nA: {answer}",
                    "direction": "inbound"
                }
                
                # Log communication (implementation depends on tool)
                # await comm_tool.arun(communication_data)
                
        except Exception as e:
            logger.warning(f"Failed to log communication: {str(e)}")
    
    def get_qa_capabilities(self) -> List[str]:
        """Get list of QA capabilities"""
        return [
            "Document-based question answering",
            "Franchise information retrieval",
            "Investment requirement queries",
            "Territory and location information",
            "Franchise support and training details",
            "Escalation to human consultants"
        ]
