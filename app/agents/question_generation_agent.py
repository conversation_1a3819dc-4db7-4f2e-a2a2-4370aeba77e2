"""
Question Generation Agent

AI agent responsible for generating and managing lead qualification questions
based on franchise documents and business requirements.
"""

import json
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path
import asyncio

from openai import AsyncOpenAI
from sqlalchemy.orm import Session

from app.models.lead_qualification import PreQualificationQuestion, QuestionTemplate
from app.core.config import settings

logger = logging.getLogger(__name__)


class QuestionGenerationAgent:
    """AI agent for generating lead qualification questions"""
    
    def __init__(self, openai_api_key: str = None):
        self.client = AsyncOpenAI(api_key=openai_api_key or settings.OPENAI_API_KEY)
        self.model = "gpt-4-turbo"
    
    async def generate_questions_from_document(
        self, 
        document_content: str, 
        franchise_type: str = "lawn_care",
        num_questions: int = 10
    ) -> List[Dict[str, Any]]:
        """Generate qualification questions from franchise document content"""
        
        prompt = self._create_question_generation_prompt(
            document_content, franchise_type, num_questions
        )
        
        try:
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert franchise consultant who creates lead qualification questions. Generate questions that effectively identify qualified franchise candidates."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.3,
                max_tokens=3000
            )
            
            questions_text = response.choices[0].message.content
            questions = self._parse_generated_questions(questions_text)
            
            logger.info(f"Generated {len(questions)} qualification questions for {franchise_type}")
            return questions
            
        except Exception as e:
            logger.error(f"Error generating questions: {e}")
            return []
    
    def _create_question_generation_prompt(
        self, 
        document_content: str, 
        franchise_type: str, 
        num_questions: int
    ) -> str:
        """Create prompt for question generation"""
        
        return f"""
Based on the following franchise information document, generate {num_questions} lead qualification questions for a {franchise_type} franchise opportunity.

DOCUMENT CONTENT:
{document_content[:8000]}  # Limit content to avoid token limits

REQUIREMENTS:
1. Generate questions that identify qualified franchise candidates
2. Focus on key qualification areas: financial capacity, experience, commitment, location, goals
3. Each question should have 2-4 specific expected answers
4. Assign appropriate score weights (5-15 points) based on importance
5. Questions should be clear, professional, and easy to understand

OUTPUT FORMAT (JSON):
{{
  "questions": [
    {{
      "question_id": "Q001",
      "category": "Financial",
      "question_text": "What is your available investment budget?",
      "expected_answers": ["$50,000-$100,000", "$100,000-$200,000", "$200,000+"],
      "score_weight": 15,
      "reasoning": "Financial capacity is critical for franchise success"
    }}
  ]
}}

CATEGORIES TO INCLUDE:
- Financial (investment capacity, ongoing fees)
- Experience (business background, relevant skills)
- Commitment (time availability, dedication level)
- Location (territory preferences, market understanding)
- Goals (business objectives, growth plans)
- Training (willingness to learn, certification requirements)

Generate questions that will effectively identify candidates who are:
- Financially qualified
- Committed to the business
- Have relevant experience or willingness to learn
- Understand the franchise model
- Have realistic expectations

Ensure questions are specific to the {franchise_type} industry and the information provided in the document.
"""
    
    def _parse_generated_questions(self, questions_text: str) -> List[Dict[str, Any]]:
        """Parse AI-generated questions from text response"""
        
        try:
            # Try to extract JSON from the response
            start_idx = questions_text.find('{')
            end_idx = questions_text.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_text = questions_text[start_idx:end_idx]
                data = json.loads(json_text)
                return data.get('questions', [])
            else:
                # Fallback: parse manually if JSON extraction fails
                return self._manual_parse_questions(questions_text)
                
        except json.JSONDecodeError:
            logger.warning("Failed to parse JSON, attempting manual parsing")
            return self._manual_parse_questions(questions_text)
    
    def _manual_parse_questions(self, text: str) -> List[Dict[str, Any]]:
        """Manually parse questions if JSON parsing fails"""
        
        questions = []
        lines = text.split('\n')
        current_question = {}
        
        for line in lines:
            line = line.strip()
            
            if line.startswith('Q') and ':' in line:
                if current_question:
                    questions.append(current_question)
                
                current_question = {
                    'question_id': line.split(':')[0].strip(),
                    'question_text': line.split(':', 1)[1].strip(),
                    'category': 'General',
                    'expected_answers': [],
                    'score_weight': 10
                }
            
            elif 'Expected:' in line or 'Answers:' in line:
                answers = line.split(':', 1)[1].strip()
                current_question['expected_answers'] = [a.strip() for a in answers.split(',')]
            
            elif 'Score:' in line or 'Weight:' in line:
                try:
                    score = int(''.join(filter(str.isdigit, line)))
                    current_question['score_weight'] = score
                except ValueError:
                    current_question['score_weight'] = 10
        
        if current_question:
            questions.append(current_question)
        
        return questions
    
    async def enhance_existing_questions(
        self, 
        questions: List[Dict[str, Any]], 
        document_content: str
    ) -> List[Dict[str, Any]]:
        """Enhance existing questions with AI insights"""
        
        prompt = f"""
Review and enhance the following qualification questions based on the franchise document content.

EXISTING QUESTIONS:
{json.dumps(questions, indent=2)}

DOCUMENT CONTENT:
{document_content[:6000]}

ENHANCEMENT TASKS:
1. Improve question clarity and specificity
2. Refine expected answers based on document content
3. Adjust score weights for optimal qualification
4. Add context or reasoning for each question
5. Ensure questions align with franchise requirements

Return the enhanced questions in the same JSON format with improvements.
"""
        
        try:
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert at refining franchise qualification questions for maximum effectiveness."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.2,
                max_tokens=2500
            )
            
            enhanced_text = response.choices[0].message.content
            enhanced_questions = self._parse_generated_questions(enhanced_text)
            
            logger.info(f"Enhanced {len(enhanced_questions)} qualification questions")
            return enhanced_questions
            
        except Exception as e:
            logger.error(f"Error enhancing questions: {e}")
            return questions  # Return original questions if enhancement fails
    
    async def create_question_template(
        self, 
        db: Session,
        template_name: str,
        franchise_type: str,
        questions: List[Dict[str, Any]],
        description: str = None
    ) -> QuestionTemplate:
        """Create a question template in the database"""
        
        # First, create/update the questions in the database
        question_ids = []
        total_score = 0
        
        for q_data in questions:
            question = PreQualificationQuestion(
                question_id=q_data['question_id'],
                category=q_data.get('category', 'General'),
                question_text=q_data['question_text'],
                expected_answers=q_data['expected_answers'],
                score_weight=q_data.get('score_weight', 10),
                context_info=q_data.get('context_info', {}),
                is_active=True
            )
            
            # Check if question already exists
            existing = db.query(PreQualificationQuestion).filter(
                PreQualificationQuestion.question_id == q_data['question_id']
            ).first()
            
            if existing:
                # Update existing question
                existing.question_text = question.question_text
                existing.expected_answers = question.expected_answers
                existing.score_weight = question.score_weight
                existing.context_info = question.context_info
                question_ids.append(str(existing.id))
            else:
                # Create new question
                db.add(question)
                db.flush()  # Get the ID
                question_ids.append(str(question.id))
            
            total_score += q_data.get('score_weight', 10)
        
        # Create the template
        template = QuestionTemplate(
            template_name=template_name,
            franchise_type=franchise_type,
            description=description or f"Qualification template for {franchise_type} franchise",
            question_ids=question_ids,
            total_possible_score=total_score,
            qualification_threshold=int(total_score * 0.8),  # 80% threshold
            is_active=True
        )
        
        db.add(template)
        db.commit()
        
        logger.info(f"Created question template '{template_name}' with {len(question_ids)} questions")
        return template
    
    async def analyze_question_performance(
        self, 
        db: Session,
        question_id: str,
        min_responses: int = 10
    ) -> Dict[str, Any]:
        """Analyze performance of a specific question"""
        
        from app.models.lead_qualification import LeadResponse
        
        # Get responses for this question
        responses = db.query(LeadResponse).join(PreQualificationQuestion).filter(
            PreQualificationQuestion.question_id == question_id
        ).all()
        
        if len(responses) < min_responses:
            return {
                'question_id': question_id,
                'status': 'insufficient_data',
                'response_count': len(responses)
            }
        
        # Calculate metrics
        total_responses = len(responses)
        qualified_responses = sum(1 for r in responses if r.is_qualified)
        qualification_rate = qualified_responses / total_responses
        
        avg_score = sum(r.score_awarded for r in responses) / total_responses
        avg_response_time = sum(r.response_time_seconds or 0 for r in responses) / total_responses
        
        # Analyze common answers
        answer_frequency = {}
        for response in responses:
            answer = response.lead_answer.lower().strip()
            answer_frequency[answer] = answer_frequency.get(answer, 0) + 1
        
        common_answers = sorted(answer_frequency.items(), key=lambda x: x[1], reverse=True)[:5]
        
        return {
            'question_id': question_id,
            'status': 'analyzed',
            'response_count': total_responses,
            'qualification_rate': qualification_rate,
            'average_score': avg_score,
            'average_response_time': avg_response_time,
            'common_answers': common_answers,
            'performance_rating': self._rate_question_performance(qualification_rate, avg_response_time)
        }
    
    def _rate_question_performance(self, qualification_rate: float, avg_response_time: float) -> str:
        """Rate question performance based on metrics"""
        
        if qualification_rate > 0.8:
            return "too_easy"
        elif qualification_rate < 0.2:
            return "too_difficult"
        elif avg_response_time > 120:  # 2 minutes
            return "too_complex"
        elif 0.4 <= qualification_rate <= 0.7 and avg_response_time <= 60:
            return "excellent"
        else:
            return "good"


# Utility functions for question management
async def load_questions_from_json(file_path: str) -> List[Dict[str, Any]]:
    """Load questions from JSON file"""
    
    try:
        with open(file_path, 'r') as f:
            data = json.load(f)
        
        if 'questions' in data:
            return data['questions']
        else:
            return data  # Assume the file contains a list of questions
            
    except Exception as e:
        logger.error(f"Error loading questions from {file_path}: {e}")
        return []


async def save_questions_to_json(questions: List[Dict[str, Any]], file_path: str) -> bool:
    """Save questions to JSON file"""
    
    try:
        data = {
            'questions': questions,
            'total_questions': len(questions),
            'total_possible_score': sum(q.get('score_weight', 10) for q in questions),
            'generated_at': str(asyncio.get_event_loop().time())
        }
        
        with open(file_path, 'w') as f:
            json.dump(data, f, indent=2)
        
        logger.info(f"Saved {len(questions)} questions to {file_path}")
        return True
        
    except Exception as e:
        logger.error(f"Error saving questions to {file_path}: {e}")
        return False
