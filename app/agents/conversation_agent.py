"""
Conversation Agent for SMS Lead Qualification

LangGraph-based agent system for managing SMS conversations through the complete
sales workflow: greeting → qualification → Q&A → scheduling.
"""

import json
import logging
from typing import Dict, Any, List, Optional, TypedDict
from datetime import datetime

from langgraph.graph import StateGraph, END
from langchain_core.messages import HumanMessage, AIMessage
from langchain_openai import ChatOpenAI
from sqlalchemy.orm import Session

from app.services.sales_script_service import script_renderer
from app.models.sales_script import ConversationState, ConversationMessage, SalesScript
from app.models.lead import Lead
from app.models.franchisor import Franchisor

logger = logging.getLogger(__name__)


class ConversationAgentState(TypedDict):
    """State for the conversation agent"""
    conversation_id: str
    lead_id: str
    franchisor_id: str
    phone_number: str
    current_stage: str
    messages: List[Dict[str, Any]]
    context: Dict[str, Any]
    last_message: str
    agent_response: str
    next_action: str
    qualification_session_id: Optional[str]
    current_question_index: int
    error_message: Optional[str]


class ConversationAgent:
    """AI Agent for managing SMS conversation flow"""
    
    def __init__(self, db_session: Session):
        self.db = db_session
        self.llm = ChatOpenAI(model="gpt-4", temperature=0.3)
        
        # Build the conversation graph
        self.graph = self._build_conversation_graph()
    
    def _build_conversation_graph(self) -> StateGraph:
        """Build the LangGraph conversation flow"""
        
        workflow = StateGraph(ConversationAgentState)
        
        # Add nodes for each conversation stage
        workflow.add_node("analyze_message", self._analyze_message)
        workflow.add_node("greeting_stage", self._handle_greeting)
        workflow.add_node("qualification_stage", self._handle_qualification)
        workflow.add_node("qa_stage", self._handle_qa)
        workflow.add_node("scheduling_stage", self._handle_scheduling)
        workflow.add_node("error_handler", self._handle_error)
        
        # Set entry point
        workflow.set_entry_point("analyze_message")
        
        # Add conditional edges based on conversation stage
        workflow.add_conditional_edges(
            "analyze_message",
            self._route_conversation,
            {
                "greeting": "greeting_stage",
                "qualification": "qualification_stage", 
                "qa": "qa_stage",
                "scheduling": "scheduling_stage",
                "error": "error_handler"
            }
        )
        
        # All stages can end or continue
        for stage in ["greeting_stage", "qualification_stage", "qa_stage", "scheduling_stage"]:
            workflow.add_edge(stage, END)
        
        workflow.add_edge("error_handler", END)
        
        return workflow.compile()
    
    def _analyze_message(self, state: ConversationAgentState) -> ConversationAgentState:
        """Analyze incoming message and determine conversation stage"""
        
        try:
            # Get conversation context
            conversation = self.db.query(ConversationState).filter(
                ConversationState.id == state["conversation_id"]
            ).first()
            
            if not conversation:
                state["error_message"] = "Conversation not found"
                state["next_action"] = "error"
                return state
            
            # Update state with current conversation info
            state["current_stage"] = conversation.current_stage
            state["qualification_session_id"] = str(conversation.qualification_session_id) if conversation.qualification_session_id else None
            state["current_question_index"] = conversation.current_question_index
            
            # Analyze message intent using LLM
            message_analysis = self._analyze_message_intent(state["last_message"], conversation.current_stage)
            
            # Determine next action based on analysis
            if conversation.current_stage == "greeting" and message_analysis.get("positive_response"):
                state["next_action"] = "qualification"
            elif conversation.current_stage == "qualification":
                state["next_action"] = "qualification"
            elif conversation.current_stage == "qa":
                state["next_action"] = "qa"
            elif conversation.current_stage == "scheduling":
                state["next_action"] = "scheduling"
            else:
                state["next_action"] = conversation.current_stage
            
            logger.info(f"Message analyzed: stage={conversation.current_stage}, next_action={state['next_action']}")
            
        except Exception as e:
            logger.error(f"Error analyzing message: {e}")
            state["error_message"] = str(e)
            state["next_action"] = "error"
        
        return state
    
    def _analyze_message_intent(self, message: str, current_stage: str) -> Dict[str, Any]:
        """Use LLM to analyze message intent"""
        
        prompt = f"""
        Analyze this SMS message in the context of a franchise sales conversation.
        
        Current conversation stage: {current_stage}
        Message: "{message}"
        
        Determine:
        1. Is this a positive response (yes, sure, okay, etc.)?
        2. Is this a negative response (no, not interested, etc.)?
        3. Is this a question about the franchise?
        4. Does the person want to schedule something?
        5. Overall sentiment (positive, negative, neutral)
        
        Respond in JSON format:
        {{
            "positive_response": boolean,
            "negative_response": boolean,
            "is_question": boolean,
            "wants_scheduling": boolean,
            "sentiment": "positive|negative|neutral",
            "intent": "brief description"
        }}
        """
        
        try:
            response = self.llm.invoke([HumanMessage(content=prompt)])
            return json.loads(response.content)
        except Exception as e:
            logger.error(f"Error analyzing message intent: {e}")
            return {"positive_response": True, "sentiment": "neutral", "intent": "continue conversation"}
    
    def _route_conversation(self, state: ConversationAgentState) -> str:
        """Route conversation to appropriate handler"""
        return state.get("next_action", "greeting")
    
    def _handle_greeting(self, state: ConversationAgentState) -> ConversationAgentState:
        """Handle greeting stage of conversation"""
        
        try:
            # Get sales script for greeting or qualification intro
            if state["last_message"] and any(word in state["last_message"].lower() for word in ["yes", "sure", "okay", "good time"]):
                # Positive response - move to qualification
                script = self._get_sales_script(state["franchisor_id"], "Qualification Introduction")
                new_stage = "qualification"
            else:
                # Send initial greeting
                script = self._get_sales_script(state["franchisor_id"], "Initial Greeting")
                new_stage = "greeting"
            
            if script:
                # Render script with context
                rendered_message = script_renderer.render_sales_script(script.script_body, state["context"])
                state["agent_response"] = rendered_message
                
                # Update conversation stage
                self._update_conversation_stage(state["conversation_id"], new_stage)
                
                logger.info(f"Greeting handled: stage={new_stage}")
            else:
                state["agent_response"] = "Hello! Thank you for your interest in our franchise opportunity. Is this a good time to chat?"
                
        except Exception as e:
            logger.error(f"Error handling greeting: {e}")
            state["error_message"] = str(e)
        
        return state
    
    def _handle_qualification(self, state: ConversationAgentState) -> ConversationAgentState:
        """Handle qualification stage of conversation"""
        
        try:
            # Check if we need to start qualification or process an answer
            if not state["qualification_session_id"]:
                # Start qualification process
                session_id = self._start_qualification_session(state["lead_id"])
                state["qualification_session_id"] = session_id
                
                # Get first question
                question = self._get_next_qualification_question(session_id, 0)
                if question:
                    state["agent_response"] = question["question_text"]
                    self._update_conversation_stage(state["conversation_id"], "qualification")
                else:
                    state["agent_response"] = "I'd like to ask you a few questions to better understand your background."
            else:
                # Process qualification answer
                if state["last_message"]:
                    # Store the answer and get next question
                    result = self._process_qualification_answer(
                        state["qualification_session_id"],
                        state["current_question_index"],
                        state["last_message"]
                    )
                    
                    if result["has_next_question"]:
                        state["agent_response"] = result["next_question"]
                        state["current_question_index"] += 1
                    else:
                        # Qualification complete
                        if result["is_qualified"]:
                            # Move to Q&A stage
                            script = self._get_sales_script(state["franchisor_id"], "Document Q&A Introduction")
                            if script:
                                state["agent_response"] = script_renderer.render_sales_script(script.script_body, state["context"])
                            else:
                                state["agent_response"] = "Great! You seem like a good fit. Do you have any questions about our franchise?"
                            self._update_conversation_stage(state["conversation_id"], "qa")
                        else:
                            state["agent_response"] = "Thank you for your time. Based on your responses, this opportunity may not be the best fit right now."
                            self._update_conversation_stage(state["conversation_id"], "completed")
                
        except Exception as e:
            logger.error(f"Error handling qualification: {e}")
            state["error_message"] = str(e)
        
        return state
    
    def _handle_qa(self, state: ConversationAgentState) -> ConversationAgentState:
        """Handle Q&A stage using document knowledge"""
        
        try:
            if state["last_message"]:
                # Use existing document Q&A system
                qa_response = self._get_document_answer(state["franchisor_id"], state["last_message"])
                
                if qa_response:
                    state["agent_response"] = qa_response
                else:
                    state["agent_response"] = "That's a great question. Let me connect you with our franchise development team who can provide detailed information."
                
                # Check if ready for scheduling
                if any(word in state["last_message"].lower() for word in ["schedule", "meeting", "call", "appointment", "next steps"]):
                    script = self._get_sales_script(state["franchisor_id"], "Appointment Scheduling")
                    if script:
                        state["agent_response"] += "\n\n" + script_renderer.render_sales_script(script.script_body, state["context"])
                    self._update_conversation_stage(state["conversation_id"], "scheduling")
                
        except Exception as e:
            logger.error(f"Error handling Q&A: {e}")
            state["error_message"] = str(e)
        
        return state
    
    def _handle_scheduling(self, state: ConversationAgentState) -> ConversationAgentState:
        """Handle appointment scheduling stage"""
        
        try:
            if state["last_message"]:
                # Process scheduling request
                scheduling_response = self._process_scheduling_request(state["last_message"], state["lead_id"])
                state["agent_response"] = scheduling_response
                
                # Mark conversation as completed
                self._update_conversation_stage(state["conversation_id"], "completed")
                
        except Exception as e:
            logger.error(f"Error handling scheduling: {e}")
            state["error_message"] = str(e)
        
        return state
    
    def _handle_error(self, state: ConversationAgentState) -> ConversationAgentState:
        """Handle errors in conversation"""
        
        state["agent_response"] = "I apologize, but I'm having trouble processing your message. A team member will follow up with you shortly."
        logger.error(f"Conversation error: {state.get('error_message', 'Unknown error')}")
        
        return state
    
    def _get_sales_script(self, franchisor_id: str, script_title: str) -> Optional[SalesScript]:
        """Get sales script from database"""
        
        return self.db.query(SalesScript).filter(
            SalesScript.franchisor_id == franchisor_id,
            SalesScript.script_title == script_title,
            SalesScript.is_active == True
        ).first()
    
    def _update_conversation_stage(self, conversation_id: str, new_stage: str):
        """Update conversation stage in database"""
        
        conversation = self.db.query(ConversationState).filter(
            ConversationState.id == conversation_id
        ).first()
        
        if conversation:
            conversation.current_stage = new_stage
            conversation.last_message_at = datetime.utcnow()
            self.db.commit()
    
    def _start_qualification_session(self, lead_id: str) -> str:
        """Start a new qualification session"""
        # This would integrate with your existing qualification system
        # For now, return a placeholder
        return "placeholder_session_id"
    
    def _get_next_qualification_question(self, session_id: str, question_index: int) -> Optional[Dict[str, Any]]:
        """Get next qualification question"""
        # This would integrate with your existing qualification system
        return {"question_text": "What is your available investment budget?"}
    
    def _process_qualification_answer(self, session_id: str, question_index: int, answer: str) -> Dict[str, Any]:
        """Process qualification answer and return next question or completion status"""
        # This would integrate with your existing qualification system
        return {
            "has_next_question": False,
            "next_question": None,
            "is_qualified": True
        }
    
    def _get_document_answer(self, franchisor_id: str, question: str) -> Optional[str]:
        """Get answer from document Q&A system"""
        # This would integrate with your existing document Q&A system
        return "Based on our franchise documentation, here's what I can tell you..."
    
    def _process_scheduling_request(self, message: str, lead_id: str) -> str:
        """Process scheduling request and return response"""
        # This would integrate with Zoho/Calendly
        return "Perfect! I'll send you a calendar link to schedule a call with our franchise development team."
    
    def process_message(self, conversation_id: str, message: str) -> str:
        """Process incoming SMS message and return agent response"""
        
        try:
            # Get conversation context
            conversation = self.db.query(ConversationState).filter(
                ConversationState.id == conversation_id
            ).first()
            
            if not conversation:
                raise ValueError("Conversation not found")
            
            # Prepare initial state
            initial_state = ConversationAgentState(
                conversation_id=conversation_id,
                lead_id=str(conversation.lead_id),
                franchisor_id=str(conversation.franchisor_id),
                phone_number=conversation.phone_number,
                current_stage=conversation.current_stage,
                messages=[],
                context=conversation.context_data or {},
                last_message=message,
                agent_response="",
                next_action="",
                qualification_session_id=str(conversation.qualification_session_id) if conversation.qualification_session_id else None,
                current_question_index=conversation.current_question_index,
                error_message=None
            )
            
            # Run the conversation graph
            result = self.graph.invoke(initial_state)
            
            # Store message in database
            self._store_conversation_message(conversation_id, message, result["agent_response"])
            
            return result["agent_response"]
            
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            return "I apologize, but I'm having trouble processing your message. A team member will follow up with you shortly."
    
    def _store_conversation_message(self, conversation_id: str, inbound_message: str, outbound_response: str):
        """Store conversation messages in database"""
        
        try:
            # Store inbound message
            inbound_msg = ConversationMessage(
                conversation_state_id=conversation_id,
                message_text=inbound_message,
                message_type="inbound",
                message_source="sms"
            )
            self.db.add(inbound_msg)
            
            # Store outbound response
            outbound_msg = ConversationMessage(
                conversation_state_id=conversation_id,
                message_text=outbound_response,
                message_type="outbound",
                message_source="agent",
                agent_response=outbound_response
            )
            self.db.add(outbound_msg)
            
            self.db.commit()
            
        except Exception as e:
            logger.error(f"Error storing conversation messages: {e}")


def create_conversation_agent(db_session: Session) -> ConversationAgent:
    """Factory function to create conversation agent"""
    return ConversationAgent(db_session)
