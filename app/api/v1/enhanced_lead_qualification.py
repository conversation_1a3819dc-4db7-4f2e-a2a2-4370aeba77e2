"""
Enhanced Lead Qualification API

API endpoints for the integrated enhanced lead qualification system.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
import logging

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import text
from pydantic import BaseModel

from app.core.database.connection import get_db

logger = logging.getLogger(__name__)
router = APIRouter()


# Pydantic models for API responses
class LeadQualificationResponse(BaseModel):
    id: str
    full_name: str
    email: str
    total_qualification_score: int
    max_possible_score: int
    qualification_percentage: float
    qualification_level: str
    is_fully_qualified: bool
    qualified_at: Optional[datetime]


class LeadResponseDetail(BaseModel):
    id: str
    response_text: str
    score_awarded: int
    is_qualified: bool
    confidence_score: float
    question_text: str
    category: str
    score_weight: int
    answered_at: datetime


class QualificationSummary(BaseModel):
    lead: LeadQualificationResponse
    responses: List[LeadResponseDetail]
    category_scores: Dict[str, Dict[str, Any]]


@router.get("/leads/qualified", response_model=List[LeadQualificationResponse])
async def get_qualified_leads(
    min_percentage: float = 80.0,
    limit: int = 50,
    db: Session = Depends(get_db)
):
    """Get all qualified leads with their qualification scores"""
    
    try:
        result = db.execute(text("""
            SELECT id, full_name, email, total_qualification_score, max_possible_score,
                   qualification_percentage, qualification_level, is_fully_qualified, qualified_at
            FROM leads 
            WHERE qualification_percentage >= :min_percentage 
            AND is_deleted = false
            ORDER BY qualification_percentage DESC, qualified_at DESC
            LIMIT :limit
        """), {
            "min_percentage": min_percentage,
            "limit": limit
        })
        
        leads = []
        for row in result.fetchall():
            leads.append(LeadQualificationResponse(
                id=str(row[0]),
                full_name=row[1],
                email=row[2],
                total_qualification_score=row[3] or 0,
                max_possible_score=row[4] or 0,
                qualification_percentage=row[5] or 0.0,
                qualification_level=row[6] or "unqualified",
                is_fully_qualified=row[7] or False,
                qualified_at=row[8]
            ))
        
        return leads
        
    except Exception as e:
        logger.error(f"Error fetching qualified leads: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error fetching qualified leads"
        )


@router.get("/leads/{lead_id}/qualification", response_model=QualificationSummary)
async def get_lead_qualification_details(
    lead_id: str,
    db: Session = Depends(get_db)
):
    """Get detailed qualification information for a specific lead"""
    
    try:
        # Get lead information
        lead_result = db.execute(text("""
            SELECT id, full_name, email, total_qualification_score, max_possible_score,
                   qualification_percentage, qualification_level, is_fully_qualified, qualified_at
            FROM leads 
            WHERE id = :lead_id AND is_deleted = false
        """), {"lead_id": lead_id})
        
        lead_row = lead_result.fetchone()
        if not lead_row:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Lead not found"
            )
        
        lead_info = LeadQualificationResponse(
            id=str(lead_row[0]),
            full_name=lead_row[1],
            email=lead_row[2],
            total_qualification_score=lead_row[3] or 0,
            max_possible_score=lead_row[4] or 0,
            qualification_percentage=lead_row[5] or 0.0,
            qualification_level=lead_row[6] or "unqualified",
            is_fully_qualified=lead_row[7] or False,
            qualified_at=lead_row[8]
        )
        
        # Get lead responses
        responses_result = db.execute(text("""
            SELECT lr.id, lr.response_text, lr.score_awarded, lr.is_qualified, 
                   lr.confidence_score, lr.answered_at,
                   q.question_text, q.category, q.score_weight
            FROM lead_responses lr
            JOIN questions q ON lr.question_id = q.id
            WHERE lr.lead_id = :lead_id
            ORDER BY q.order_sequence, lr.answered_at
        """), {"lead_id": lead_id})
        
        responses = []
        category_scores = {}
        
        for row in responses_result.fetchall():
            response = LeadResponseDetail(
                id=str(row[0]),
                response_text=row[1],
                score_awarded=row[2] or 0,
                is_qualified=row[3] or False,
                confidence_score=row[4] or 0.0,
                answered_at=row[5],
                question_text=row[6],
                category=row[7] or "General",
                score_weight=row[8] or 0
            )
            responses.append(response)
            
            # Calculate category scores
            category = response.category
            if category not in category_scores:
                category_scores[category] = {
                    "total_score": 0,
                    "max_score": 0,
                    "questions_count": 0,
                    "qualified_count": 0
                }
            
            category_scores[category]["total_score"] += response.score_awarded
            category_scores[category]["max_score"] += response.score_weight
            category_scores[category]["questions_count"] += 1
            if response.is_qualified:
                category_scores[category]["qualified_count"] += 1
        
        # Calculate category percentages
        for category in category_scores:
            cat_data = category_scores[category]
            cat_data["percentage"] = (cat_data["total_score"] / cat_data["max_score"] * 100) if cat_data["max_score"] > 0 else 0
            cat_data["qualification_rate"] = (cat_data["qualified_count"] / cat_data["questions_count"] * 100) if cat_data["questions_count"] > 0 else 0
        
        return QualificationSummary(
            lead=lead_info,
            responses=responses,
            category_scores=category_scores
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching lead qualification details: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error fetching lead qualification details"
        )


@router.get("/analytics/qualification-summary")
async def get_qualification_analytics(
    days: int = 30,
    db: Session = Depends(get_db)
):
    """Get qualification analytics summary"""
    
    try:
        # Get overall stats
        stats_result = db.execute(text("""
            SELECT 
                COUNT(*) as total_leads,
                COUNT(CASE WHEN qualification_percentage >= 80 THEN 1 END) as qualified_leads,
                COUNT(CASE WHEN qualification_percentage BETWEEN 60 AND 79.99 THEN 1 END) as partially_qualified,
                COUNT(CASE WHEN qualification_percentage < 60 AND qualification_percentage > 0 THEN 1 END) as unqualified,
                AVG(qualification_percentage) as avg_percentage,
                MAX(qualification_percentage) as max_percentage
            FROM leads 
            WHERE is_deleted = false 
            AND qualification_percentage IS NOT NULL
            AND created_at >= NOW() - INTERVAL '%s days'
        """ % days))
        
        stats_row = stats_result.fetchone()
        
        # Get category performance
        category_result = db.execute(text("""
            SELECT 
                q.category,
                COUNT(lr.id) as total_responses,
                COUNT(CASE WHEN lr.is_qualified THEN 1 END) as qualified_responses,
                AVG(lr.score_awarded) as avg_score,
                AVG(lr.confidence_score) as avg_confidence
            FROM lead_responses lr
            JOIN questions q ON lr.question_id = q.id
            JOIN leads l ON lr.lead_id = l.id
            WHERE l.is_deleted = false
            AND lr.created_at >= NOW() - INTERVAL '%s days'
            GROUP BY q.category
            ORDER BY avg_score DESC
        """ % days))
        
        category_performance = []
        for row in category_result.fetchall():
            category_performance.append({
                "category": row[0],
                "total_responses": row[1],
                "qualified_responses": row[2],
                "qualification_rate": (row[2] / row[1] * 100) if row[1] > 0 else 0,
                "average_score": float(row[3]) if row[3] else 0,
                "average_confidence": float(row[4]) if row[4] else 0
            })
        
        return {
            "period_days": days,
            "total_leads": stats_row[0] if stats_row else 0,
            "qualified_leads": stats_row[1] if stats_row else 0,
            "partially_qualified_leads": stats_row[2] if stats_row else 0,
            "unqualified_leads": stats_row[3] if stats_row else 0,
            "qualification_rate": (stats_row[1] / stats_row[0] * 100) if stats_row and stats_row[0] > 0 else 0,
            "average_qualification_percentage": float(stats_row[4]) if stats_row and stats_row[4] else 0,
            "highest_qualification_percentage": float(stats_row[5]) if stats_row and stats_row[5] else 0,
            "category_performance": category_performance
        }
        
    except Exception as e:
        logger.error(f"Error fetching qualification analytics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error fetching qualification analytics"
        )


@router.get("/questions/enhanced")
async def get_enhanced_questions(
    category: Optional[str] = None,
    active_only: bool = True,
    db: Session = Depends(get_db)
):
    """Get enhanced qualification questions with scoring information"""
    
    try:
        query = """
            SELECT id, question_text, question_type, category, expected_answers, 
                   score_weight, order_sequence, is_active
            FROM questions 
            WHERE expected_answers IS NOT NULL
        """
        
        params = {}
        
        if active_only:
            query += " AND is_active = true AND is_deleted = false"
        
        if category:
            query += " AND category = :category"
            params["category"] = category
        
        query += " ORDER BY order_sequence, category"
        
        result = db.execute(text(query), params)
        
        questions = []
        for row in result.fetchall():
            questions.append({
                "id": str(row[0]),
                "question_text": row[1],
                "question_type": row[2],
                "category": row[3],
                "expected_answers": row[4],  # Already JSON
                "score_weight": row[5],
                "order_sequence": row[6],
                "is_active": row[7]
            })
        
        return {
            "questions": questions,
            "total_questions": len(questions),
            "total_possible_score": sum(q["score_weight"] for q in questions),
            "categories": list(set(q["category"] for q in questions))
        }
        
    except Exception as e:
        logger.error(f"Error fetching enhanced questions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error fetching enhanced questions"
        )
