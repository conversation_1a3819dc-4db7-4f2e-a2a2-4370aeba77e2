"""
Sales Scripts API Endpoints

API for managing and rendering dynamic sales script templates.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
import logging

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import text
from pydantic import BaseModel, Field

from app.core.database.connection import get_db
from app.models.sales_script import SalesScript
from app.services.sales_script_service import script_renderer, SalesScriptTemplates

logger = logging.getLogger(__name__)
router = APIRouter()


# Pydantic models for API
class SalesScriptCreate(BaseModel):
    franchisor_id: str
    script_title: str
    script_body: str
    script_type: str = "greeting"
    is_active: bool = True


class SalesScriptUpdate(BaseModel):
    script_title: Optional[str] = None
    script_body: Optional[str] = None
    script_type: Optional[str] = None
    is_active: Optional[bool] = None


class SalesScriptResponse(BaseModel):
    id: str
    franchisor_id: str
    script_title: str
    script_body: str
    script_type: str
    is_active: bool
    created_at: datetime
    updated_at: datetime


class ScriptRenderRequest(BaseModel):
    franchisor_id: str
    script_title: str
    context: Dict[str, Any]


class ScriptRenderResponse(BaseModel):
    rendered_script: str
    context_used: Dict[str, Any]
    placeholders_found: List[str]
    validation_status: Dict[str, Any]


@router.post("/", response_model=SalesScriptResponse)
async def create_sales_script(
    script_data: SalesScriptCreate,
    db: Session = Depends(get_db)
):
    """Create a new sales script template"""
    
    try:
        # Validate script template
        validation = script_renderer.validate_context(script_data.script_body, {})
        logger.info(f"Creating script with {validation['total_placeholders']} placeholders")
        
        # Create script
        script = SalesScript(
            franchisor_id=script_data.franchisor_id,
            script_title=script_data.script_title,
            script_body=script_data.script_body,
            script_type=script_data.script_type,
            is_active=script_data.is_active
        )
        
        db.add(script)
        db.commit()
        db.refresh(script)
        
        return SalesScriptResponse(
            id=str(script.id),
            franchisor_id=str(script.franchisor_id),
            script_title=script.script_title,
            script_body=script.script_body,
            script_type=script.script_type,
            is_active=script.is_active,
            created_at=script.created_at,
            updated_at=script.updated_at
        )
        
    except Exception as e:
        logger.error(f"Error creating sales script: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create sales script"
        )


@router.get("/franchisor/{franchisor_id}", response_model=List[SalesScriptResponse])
async def get_franchisor_scripts(
    franchisor_id: str,
    active_only: bool = True,
    script_type: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """Get all sales scripts for a franchisor"""
    
    try:
        query = db.query(SalesScript).filter(SalesScript.franchisor_id == franchisor_id)
        
        if active_only:
            query = query.filter(SalesScript.is_active == True)
        
        if script_type:
            query = query.filter(SalesScript.script_type == script_type)
        
        scripts = query.order_by(SalesScript.created_at.desc()).all()
        
        return [
            SalesScriptResponse(
                id=str(script.id),
                franchisor_id=str(script.franchisor_id),
                script_title=script.script_title,
                script_body=script.script_body,
                script_type=script.script_type,
                is_active=script.is_active,
                created_at=script.created_at,
                updated_at=script.updated_at
            )
            for script in scripts
        ]
        
    except Exception as e:
        logger.error(f"Error fetching franchisor scripts: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch sales scripts"
        )


@router.post("/render", response_model=ScriptRenderResponse)
async def render_sales_script(
    render_request: ScriptRenderRequest,
    db: Session = Depends(get_db)
):
    """Render a sales script with dynamic context"""
    
    try:
        # Get the script
        script = db.query(SalesScript).filter(
            SalesScript.franchisor_id == render_request.franchisor_id,
            SalesScript.script_title == render_request.script_title,
            SalesScript.is_active == True
        ).first()
        
        if not script:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Active script '{render_request.script_title}' not found for franchisor"
            )
        
        # Get franchisor name for context
        franchisor_result = db.execute(text("""
            SELECT company_name FROM franchisors WHERE id = :franchisor_id
        """), {"franchisor_id": render_request.franchisor_id})
        
        franchisor_row = franchisor_result.fetchone()
        if franchisor_row:
            render_request.context["company_name"] = franchisor_row[0]
        
        # Validate context before rendering
        validation = script_renderer.validate_context(script.script_body, render_request.context)
        
        # Render the script
        rendered_script = script_renderer.render_sales_script(
            script.script_body, 
            render_request.context
        )
        
        # Prepare enhanced context for response
        enhanced_context = script_renderer.prepare_context(render_request.context)
        
        return ScriptRenderResponse(
            rendered_script=rendered_script,
            context_used=enhanced_context,
            placeholders_found=script_renderer.extract_placeholders(script.script_body),
            validation_status=validation
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error rendering sales script: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to render sales script: {str(e)}"
        )


@router.get("/templates")
async def get_predefined_templates():
    """Get predefined script templates"""
    
    return {
        "initial_greeting": SalesScriptTemplates.INITIAL_GREETING,
        "follow_up_message": SalesScriptTemplates.FOLLOW_UP_MESSAGE,
        "qualification_intro": SalesScriptTemplates.QUALIFICATION_INTRO,
        "appointment_scheduling": SalesScriptTemplates.APPOINTMENT_SCHEDULING,
        "document_qa_intro": SalesScriptTemplates.DOCUMENT_QA_INTRO
    }


@router.post("/validate")
async def validate_script_template(
    script_body: str,
    sample_context: Optional[Dict[str, Any]] = None
):
    """Validate a script template and show placeholders"""
    
    try:
        placeholders = script_renderer.extract_placeholders(script_body)
        
        validation_result = {
            "placeholders_found": placeholders,
            "total_placeholders": len(placeholders),
            "is_valid_template": len(placeholders) > 0
        }
        
        if sample_context:
            context_validation = script_renderer.validate_context(script_body, sample_context)
            validation_result["context_validation"] = context_validation
            
            # Try rendering with sample context
            try:
                sample_render = script_renderer.render_sales_script(script_body, sample_context)
                validation_result["sample_render"] = sample_render
            except Exception as e:
                validation_result["render_error"] = str(e)
        
        return validation_result
        
    except Exception as e:
        logger.error(f"Error validating script template: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to validate script template: {str(e)}"
        )


@router.put("/{script_id}", response_model=SalesScriptResponse)
async def update_sales_script(
    script_id: str,
    script_update: SalesScriptUpdate,
    db: Session = Depends(get_db)
):
    """Update an existing sales script"""
    
    try:
        script = db.query(SalesScript).filter(SalesScript.id == script_id).first()
        
        if not script:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Sales script not found"
            )
        
        # Update fields
        update_data = script_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(script, field, value)
        
        script.updated_at = datetime.utcnow()
        
        db.commit()
        db.refresh(script)
        
        return SalesScriptResponse(
            id=str(script.id),
            franchisor_id=str(script.franchisor_id),
            script_title=script.script_title,
            script_body=script.script_body,
            script_type=script.script_type,
            is_active=script.is_active,
            created_at=script.created_at,
            updated_at=script.updated_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating sales script: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update sales script"
        )
