"""
SMS Webhook Handler for Kudosity

Handles SMS_INBOUND events from Kudosity and manages conversation flow
through AI agents for lead qualification and sales process.
"""

import json
import logging
from typing import Dict, Any, Optional
from datetime import datetime
import uuid

from fastapi import APIRouter, Depends, HTTPException, status, Request, BackgroundTasks
from sqlalchemy.orm import Session
from sqlalchemy import text
from pydantic import BaseModel, Field

from app.core.database.connection import get_db
from app.models.sales_script import ConversationState, ConversationMessage
from app.models.lead import Lead
from app.models.franchisor import Franchisor
from app.agents.conversation_agent import create_conversation_agent
from app.services.sales_script_service import script_renderer

logger = logging.getLogger(__name__)
router = APIRouter()


# Pydantic models for webhook payloads
class KudositySMSInbound(BaseModel):
    """Kudosity SMS_INBOUND webhook payload"""
    event_type: str = Field(..., description="Event type (SMS_INBOUND)")
    message_id: str = Field(..., description="Unique message ID from Kudosity")
    from_number: str = Field(..., description="Sender phone number")
    to_number: str = Field(..., description="Recipient phone number")
    message_text: str = Field(..., description="SMS message content")
    timestamp: str = Field(..., description="Message timestamp")
    metadata: Optional[Dict[str, Any]] = Field(default=None, description="Additional metadata")


class ConversationInitRequest(BaseModel):
    """Request to initialize a new conversation"""
    phone_number: str
    franchisor_id: str
    lead_name: Optional[str] = None
    rep_name: Optional[str] = "Sales Representative"


class SMSResponse(BaseModel):
    """Response for SMS webhook"""
    success: bool
    message_id: str
    agent_response: Optional[str] = None
    conversation_stage: Optional[str] = None
    error: Optional[str] = None


class ConversationManager:
    """Manages SMS conversation state and routing"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_or_create_conversation(self, phone_number: str, franchisor_id: str = None) -> ConversationState:
        """Get existing conversation or create new one"""
        
        # Look for active conversation
        conversation = self.db.query(ConversationState).filter(
            ConversationState.phone_number == phone_number,
            ConversationState.is_active == True
        ).first()
        
        if conversation:
            return conversation
        
        # Create new conversation
        if not franchisor_id:
            # Default to first available franchisor
            franchisor = self.db.query(Franchisor).filter(Franchisor.is_active == True).first()
            if not franchisor:
                raise ValueError("No active franchisor found")
            franchisor_id = str(franchisor.id)
        
        # Get or create lead
        lead = self.get_or_create_lead(phone_number, franchisor_id)
        
        # Create conversation state
        conversation = ConversationState(
            lead_id=lead.id,
            phone_number=phone_number,
            franchisor_id=franchisor_id,
            current_stage="greeting",
            context_data={
                "rep_name": "Sales Representative",
                "company_name": self.get_franchisor_name(franchisor_id),
                "lead_first_name": self.extract_first_name(lead.full_name) if lead.full_name else "there"
            },
            is_active=True
        )
        
        self.db.add(conversation)
        self.db.commit()
        self.db.refresh(conversation)
        
        logger.info(f"Created new conversation for {phone_number}")
        return conversation
    
    def get_or_create_lead(self, phone_number: str, franchisor_id: str) -> Lead:
        """Get existing lead or create new one"""
        
        # Look for existing lead by phone
        lead = self.db.query(Lead).filter(
            Lead.phone == phone_number,
            Lead.is_deleted == False
        ).first()
        
        if lead:
            return lead
        
        # Create new lead
        lead = Lead(
            id=str(uuid.uuid4()),
            phone=phone_number,
            lead_source="sms_inbound",
            qualification_status="new",
            status="new",
            brand_preference=franchisor_id,
            is_active=True,
            is_deleted=False
        )
        
        self.db.add(lead)
        self.db.commit()
        self.db.refresh(lead)
        
        logger.info(f"Created new lead for {phone_number}")
        return lead
    
    def get_franchisor_name(self, franchisor_id: str) -> str:
        """Get franchisor name for context"""
        
        franchisor = self.db.query(Franchisor).filter(Franchisor.id == franchisor_id).first()
        return franchisor.name if franchisor else "Our Company"
    
    def extract_first_name(self, full_name: str) -> str:
        """Extract first name from full name"""
        
        if not full_name:
            return "there"
        
        parts = full_name.strip().split()
        return parts[0].title() if parts else "there"
    
    def send_initial_greeting(self, conversation: ConversationState) -> str:
        """Send initial greeting message"""
        
        try:
            # Get greeting script
            script_result = self.db.execute(text("""
                SELECT script_body FROM sales_scripts 
                WHERE franchisor_id = :franchisor_id 
                AND script_type = 'greeting' 
                AND is_active = true
                LIMIT 1
            """), {"franchisor_id": conversation.franchisor_id})
            
            script_row = script_result.fetchone()
            
            if script_row:
                # Render script with context
                rendered_message = script_renderer.render_sales_script(
                    script_row[0], 
                    conversation.context_data or {}
                )
            else:
                # Fallback greeting
                company_name = conversation.context_data.get("company_name", "our company")
                rep_name = conversation.context_data.get("rep_name", "Sales Representative")
                
                rendered_message = f"Hi, this is {rep_name}, calling you on behalf of {company_name}. You inquired about this opportunity. I want to give you information about this. Is this a good time to chat?"
            
            # Store outbound message
            outbound_msg = ConversationMessage(
                conversation_state_id=conversation.id,
                message_text=rendered_message,
                message_type="outbound",
                message_source="system"
            )
            self.db.add(outbound_msg)
            self.db.commit()
            
            return rendered_message
            
        except Exception as e:
            logger.error(f"Error sending initial greeting: {e}")
            return "Hello! Thank you for your interest in our franchise opportunity. Is this a good time to chat?"


@router.post("/kudosity/sms-inbound", response_model=SMSResponse)
async def handle_sms_inbound(
    webhook_data: KudositySMSInbound,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """Handle SMS_INBOUND webhook from Kudosity"""
    
    try:
        logger.info(f"Received SMS from {webhook_data.from_number}: {webhook_data.message_text}")
        
        # Initialize conversation manager
        conv_manager = ConversationManager(db)
        
        # Get or create conversation
        conversation = conv_manager.get_or_create_conversation(webhook_data.from_number)
        
        # Store inbound message
        inbound_msg = ConversationMessage(
            conversation_state_id=conversation.id,
            message_text=webhook_data.message_text,
            message_type="inbound",
            message_source="sms",
            kudosity_message_id=webhook_data.message_id
        )
        db.add(inbound_msg)
        db.commit()
        
        # Process message with AI agent
        agent = create_conversation_agent(db)
        agent_response = agent.process_message(str(conversation.id), webhook_data.message_text)
        
        # Update conversation timestamp
        conversation.last_message_at = datetime.utcnow()
        db.commit()
        
        # Schedule background task to send response via Kudosity
        background_tasks.add_task(
            send_sms_response,
            webhook_data.from_number,
            agent_response,
            webhook_data.message_id
        )
        
        return SMSResponse(
            success=True,
            message_id=webhook_data.message_id,
            agent_response=agent_response,
            conversation_stage=conversation.current_stage
        )
        
    except Exception as e:
        logger.error(f"Error handling SMS inbound: {e}")
        return SMSResponse(
            success=False,
            message_id=webhook_data.message_id,
            error=str(e)
        )


@router.post("/conversation/init")
async def initialize_conversation(
    init_request: ConversationInitRequest,
    db: Session = Depends(get_db)
):
    """Initialize a new conversation and send greeting"""
    
    try:
        conv_manager = ConversationManager(db)
        
        # Create conversation
        conversation = conv_manager.get_or_create_conversation(
            init_request.phone_number,
            init_request.franchisor_id
        )
        
        # Update context with provided info
        if init_request.lead_name or init_request.rep_name:
            context_data = conversation.context_data or {}
            if init_request.lead_name:
                context_data["lead_first_name"] = conv_manager.extract_first_name(init_request.lead_name)
            if init_request.rep_name:
                context_data["rep_name"] = init_request.rep_name
            
            conversation.context_data = context_data
            db.commit()
        
        # Send initial greeting
        greeting_message = conv_manager.send_initial_greeting(conversation)
        
        return {
            "success": True,
            "conversation_id": str(conversation.id),
            "greeting_message": greeting_message,
            "phone_number": init_request.phone_number
        }
        
    except Exception as e:
        logger.error(f"Error initializing conversation: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to initialize conversation: {str(e)}"
        )


@router.get("/conversation/{phone_number}")
async def get_conversation_status(
    phone_number: str,
    db: Session = Depends(get_db)
):
    """Get current conversation status for a phone number"""
    
    try:
        conversation = db.query(ConversationState).filter(
            ConversationState.phone_number == phone_number,
            ConversationState.is_active == True
        ).first()
        
        if not conversation:
            return {"exists": False}
        
        # Get recent messages
        recent_messages = db.query(ConversationMessage).filter(
            ConversationMessage.conversation_state_id == conversation.id
        ).order_by(ConversationMessage.created_at.desc()).limit(10).all()
        
        return {
            "exists": True,
            "conversation_id": str(conversation.id),
            "current_stage": conversation.current_stage,
            "last_message_at": conversation.last_message_at,
            "recent_messages": [
                {
                    "message_text": msg.message_text,
                    "message_type": msg.message_type,
                    "created_at": msg.created_at
                }
                for msg in reversed(recent_messages)
            ]
        }
        
    except Exception as e:
        logger.error(f"Error getting conversation status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get conversation status"
        )


async def send_sms_response(phone_number: str, message: str, original_message_id: str):
    """Background task to send SMS response via Kudosity API"""
    
    try:
        # This would integrate with Kudosity's outbound SMS API
        # For now, just log the response
        logger.info(f"Sending SMS to {phone_number}: {message}")
        
        # TODO: Implement actual Kudosity API call
        # kudosity_client.send_sms(
        #     to_number=phone_number,
        #     message=message,
        #     reference_id=original_message_id
        # )
        
    except Exception as e:
        logger.error(f"Error sending SMS response: {e}")


@router.post("/test/send-greeting")
async def test_send_greeting(
    phone_number: str,
    franchisor_id: str,
    db: Session = Depends(get_db)
):
    """Test endpoint to send greeting message"""
    
    try:
        conv_manager = ConversationManager(db)
        conversation = conv_manager.get_or_create_conversation(phone_number, franchisor_id)
        greeting = conv_manager.send_initial_greeting(conversation)
        
        return {
            "success": True,
            "phone_number": phone_number,
            "greeting_message": greeting,
            "conversation_id": str(conversation.id)
        }
        
    except Exception as e:
        logger.error(f"Error in test greeting: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
