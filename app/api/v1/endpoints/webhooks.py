"""
Webhook API endpoints for Kudosity webhook events
Handles all webhook event types and processes them according to business rules
"""

from datetime import datetime
from typing import Dict, Any
from fastapi import APIRouter, Depends, Request, status, HTTPException
from fastapi.responses import JSONResponse
from pydantic import ValidationError
import structlog
import json

from app.schemas.webhook import (
    WebhookRequest,
    WebhookEventType,
    WebhookReceiveResponse,
    WebhookHealthResponse
)
from app.core.security.enhanced_auth_middleware import get_current_user
from app.core.responses import create_success_response, create_error_response, ErrorCodes
from app.core.logging import logger
from docqa.central_api import ask_question
from app.services.franchisor_service import FranchisorService
from app.repositories.franchisor_repository import FranchisorRepository
from app.services.franchisor_detection_service import get_franchisor_detection_service
from app.db.session import async_session

# Additional imports for enhanced functionality
import uuid
import json
from datetime import datetime
from sqlalchemy import text

# Import QnA/RAG functionality
try:
    from docqa.central_api import ask_question
    QNA_AVAILABLE = True
    logger.info("QnA/RAG functionality loaded successfully")
except ImportError as e:
    QNA_AVAILABLE = False
    logger.warning(f"QnA/RAG functionality not available: {e}")

# Initialize structured logger
logger = structlog.get_logger(__name__)

router = APIRouter(tags=["Webhooks"])


class ConversationManager:
    """Manages SMS conversation state and routing for Kudosity webhooks"""

    def __init__(self, db):
        self.db = db

    async def get_or_create_conversation(self, phone_number: str, franchisor_id: str = None):
        """Get existing conversation or create new one"""

        # Look for active conversation
        result = await self.db.execute(text("""
            SELECT id, current_stage, context_data, franchisor_id, lead_id
            FROM conversation_states
            WHERE phone_number = :phone AND is_active = true
        """), {"phone": phone_number})

        existing_conv = result.fetchone()
        if existing_conv:
            # Create simple conversation object
            conversation = type('Conversation', (), {})()
            conversation.id = existing_conv[0]
            conversation.current_stage = existing_conv[1]
            conversation.context_data = existing_conv[2] or {}
            conversation.franchisor_id = existing_conv[3]
            conversation.lead_id = existing_conv[4]
            conversation.phone_number = phone_number
            conversation.is_active = True
            return conversation

        # Create new conversation
        if not franchisor_id:
            # Default to first available franchisor
            result = await self.db.execute(text("SELECT id, name FROM franchisors WHERE is_active = true LIMIT 1"))
            franchisor_row = result.fetchone()
            if not franchisor_row:
                raise ValueError("No active franchisor found")
            franchisor_id = str(franchisor_row[0])
            franchisor_name = franchisor_row[1]
        else:
            result = await self.db.execute(text("SELECT name FROM franchisors WHERE id = :id"), {"id": franchisor_id})
            franchisor_row = result.fetchone()
            franchisor_name = franchisor_row[0] if franchisor_row else "Our Company"

        # Get or create lead
        lead_id = await self.get_or_create_lead(phone_number, franchisor_id)

        # Create conversation state
        conversation_id = str(uuid.uuid4())
        context_data = {
            "rep_name": "Sales Representative",
            "company_name": franchisor_name,
            "lead_first_name": "there"
        }

        await self.db.execute(text("""
            INSERT INTO conversation_states
            (id, lead_id, phone_number, franchisor_id, current_stage, context_data, is_active, created_at, updated_at)
            VALUES (:id, :lead_id, :phone, :franchisor_id, :stage, :context, :is_active, :created_at, :updated_at)
        """), {
            "id": conversation_id,
            "lead_id": lead_id,
            "phone": phone_number,
            "franchisor_id": franchisor_id,
            "stage": "greeting",
            "context": json.dumps(context_data),
            "is_active": True,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        })

        await self.db.commit()

        # Create simple conversation object
        conversation = type('Conversation', (), {})()
        conversation.id = conversation_id
        conversation.lead_id = lead_id
        conversation.phone_number = phone_number
        conversation.franchisor_id = franchisor_id
        conversation.current_stage = "greeting"
        conversation.context_data = context_data
        conversation.is_active = True

        logger.info(f"Created new conversation for {phone_number}")
        return conversation

    async def get_or_create_lead(self, phone_number: str, franchisor_id: str) -> str:
        """Get existing lead or create new one"""

        # Check for existing lead
        result = await self.db.execute(text("""
            SELECT id FROM leads WHERE phone = :phone AND is_deleted = false
        """), {"phone": phone_number})

        existing_lead = result.fetchone()
        if existing_lead:
            return str(existing_lead[0])

        # Create new lead
        lead_id = str(uuid.uuid4())
        await self.db.execute(text("""
            INSERT INTO leads
            (id, full_name, phone, lead_source, qualification_status, status, brand_preference, is_active, is_deleted, created_at, updated_at)
            VALUES (:id, :full_name, :phone, :source, :qual_status, :status, :brand_pref, :is_active, :is_deleted, :created_at, :updated_at)
        """), {
            "id": lead_id,
            "full_name": f"SMS Lead {phone_number[-4:]}",
            "phone": phone_number,
            "source": "sms_inbound",
            "qual_status": "new",
            "status": "new",
            "brand_pref": franchisor_id,
            "is_active": True,
            "is_deleted": False,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        })

        await self.db.commit()
        logger.info(f"Created new lead: {lead_id[:8]}...")
        return lead_id


async def generate_ai_answer(question: str) -> Dict[str, Any]:
    """
    Generate AI-powered answer using QnA/RAG system with dynamic franchisor detection

    Args:
        question: The question to answer

    Returns:
        Dict containing answer and metadata
    """
    if not QNA_AVAILABLE:
        return {
            "success": False,
            "answer": "❌ QnA/RAG system is not available. Please check the system configuration.",
            "error": "QnA system not available"
        }

    try:
        # Prepare base request
        question_request = {
            "question": question,
            "top_k": 6,  # Number of relevant documents to retrieve
            "similarity_threshold": 0.7,  # Minimum similarity score
            "processing_options": {
                "priority": 5,  # Medium priority
                "triggered_via": "webhook"
            }
        }

        # 🆕 Add dynamic franchisor detection
        async with async_session() as session:
            try:
                detection_service = get_franchisor_detection_service(session)
                franchisor_id, detection_confidence = await detection_service.detect_franchisor_from_question(
                    question=question,
                    include_confidence=True,
                    use_embeddings=True
                )

                if franchisor_id:
                    question_request["franchisor_id"] = franchisor_id
                    logger.info(
                        "Franchisor detected for AI answer generation",
                        franchisor_id=franchisor_id,
                        confidence=detection_confidence
                    )

            except Exception as detection_error:
                logger.warning(
                    "Franchisor detection failed in generate_ai_answer, continuing with general search",
                    error=str(detection_error)
                )

        # Use the central RAG system to generate an answer
        result = await ask_question(question_request)

        return result

    except Exception as e:
        logger.error(f"Failed to generate AI answer for question: {question[:100]}... Error: {e}")
        return {
            "success": False,
            "answer": "❌ Sorry, I encountered an error while processing your question.",
            "error": str(e)
        }


async def process_sms_chatbot_message(phone_number: str, message_text: str, message_id: str, db) -> Dict[str, Any]:
    """
    Process SMS message through the chatbot conversation flow

    Args:
        phone_number: Sender's phone number
        message_text: SMS message content
        message_id: Unique message ID from Kudosity
        db: Database session

    Returns:
        Dict with chatbot response and conversation details
    """
    try:
        logger.info(f"Processing SMS chatbot message from {phone_number}: {message_text}")

        # Initialize conversation manager
        conv_manager = ConversationManager(db)

        # Get or create conversation
        conversation = await conv_manager.get_or_create_conversation(phone_number)

        # Store inbound message
        inbound_msg_id = str(uuid.uuid4())
        await db.execute(text("""
            INSERT INTO conversation_messages
            (id, conversation_state_id, message_text, message_type, message_source, kudosity_message_id, created_at)
            VALUES (:id, :conv_id, :text, :type, :source, :kudosity_id, :created_at)
        """), {
            "id": inbound_msg_id,
            "conv_id": conversation.id,
            "text": message_text,
            "type": "inbound",
            "source": "sms",
            "kudosity_id": message_id,
            "created_at": datetime.utcnow()
        })

        # Process message with LangGraph orchestrator and franchisor detection
        agent_response = await process_message_with_langgraph_agent(conversation, message_text, db)

        # Store outbound message
        outbound_msg_id = str(uuid.uuid4())
        await db.execute(text("""
            INSERT INTO conversation_messages
            (id, conversation_state_id, message_text, message_type, message_source, agent_response, created_at)
            VALUES (:id, :conv_id, :text, :type, :source, :agent_resp, :created_at)
        """), {
            "id": outbound_msg_id,
            "conv_id": conversation.id,
            "text": agent_response,
            "type": "outbound",
            "source": "agent",
            "agent_resp": agent_response,
            "created_at": datetime.utcnow()
        })

        # Update conversation timestamp
        await db.execute(text("""
            UPDATE conversation_states
            SET last_message_at = :last_msg, updated_at = :updated_at
            WHERE id = :conv_id
        """), {
            "last_msg": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
            "conv_id": conversation.id
        })

        await db.commit()

        return {
            "success": True,
            "agent_response": agent_response,
            "conversation_stage": conversation.current_stage,
            "conversation_id": conversation.id,
            "phone_number": phone_number
        }

    except Exception as e:
        logger.error(f"Error processing SMS chatbot message: {e}")
        await db.rollback()
        return {
            "success": False,
            "error": str(e),
            "agent_response": "I apologize, but I'm having trouble processing your message. A team member will follow up with you shortly."
        }


async def process_message_with_langgraph_agent(conversation, message_text: str, db) -> str:
    """
    Process message using LangGraph orchestrator with franchisor detection
    """
    try:
        from app.agents.orchestrator import AgentOrchestrator
        from app.services.franchisor_detection_service import get_franchisor_detection_service

        logger.info(f"Processing message with LangGraph agent: {message_text[:100]}")

        # Detect franchisor from the message
        franchisor_id = None
        franchisor_name = None
        detection_confidence = 0.0

        try:
            detection_service = get_franchisor_detection_service(db)
            franchisor_id, detection_confidence = await detection_service.detect_franchisor_from_question(
                question=message_text,
                include_confidence=True,
                use_embeddings=True
            )

            if franchisor_id:
                # Get franchisor name
                franchisor_result = await db.execute(text("""
                    SELECT name FROM franchisors WHERE id = :franchisor_id
                """), {"franchisor_id": franchisor_id})
                franchisor_row = franchisor_result.fetchone()
                if franchisor_row:
                    franchisor_name = franchisor_row[0]

                logger.info(f"Franchisor detected: {franchisor_name} (ID: {franchisor_id}, confidence: {detection_confidence})")

        except Exception as detection_error:
            logger.warning(f"Franchisor detection failed: {detection_error}")

        # Create orchestrator
        orchestrator = AgentOrchestrator()

        # Prepare context with franchisor information
        context = {
            "conversation_id": str(conversation.id),
            "phone_number": conversation.phone_number,
            "current_stage": conversation.current_stage,
            "franchisor_id": franchisor_id,
            "franchisor_name": franchisor_name,
            "detection_confidence": detection_confidence
        }

        # Process message through LangGraph
        result = await orchestrator.process_message(
            session_id=str(conversation.id),
            message=message_text,
            context=context
        )

        if result.get("success"):
            return result.get("response", "Thank you for your message. How can I help you today?")
        else:
            logger.error(f"LangGraph processing failed: {result.get('error')}")
            return "Thank you for your message. A team member will follow up with you shortly."

    except Exception as e:
        logger.error(f"Error in LangGraph agent processing: {e}")
        return "Thank you for your message. A team member will follow up with you shortly."


async def process_message_with_simple_agent(conversation, message_text: str, db) -> str:
    """
    Simple agent processing for async compatibility
    """
    try:
        # Simple intent analysis
        message_lower = message_text.lower()

        # Positive indicators
        positive_words = ["yes", "sure", "okay", "good time", "great", "absolutely", "definitely", "interested"]
        is_positive = any(word in message_lower for word in positive_words)

        # Question indicators
        question_words = ["what", "how", "when", "where", "why", "who", "can you", "do you"]
        is_question = any(word in message_lower for word in question_words) or "?" in message_text

        # Scheduling indicators
        scheduling_words = ["schedule", "meeting", "call", "appointment", "next steps", "when can"]
        wants_scheduling = any(word in message_lower for word in scheduling_words)

        # Get sales script based on stage and intent
        if conversation.current_stage == "greeting":
            if is_positive:
                script_body = "Great! I'd like to ask you a few quick questions to better understand your background and interests. This will help me provide you with the most relevant information about the {{company_name}} opportunity. Are you ready to get started?"
                new_stage = "qualification"
            elif is_question:
                script_body = "I'd be happy to answer any questions you have about {{company_name}}. What would you like to know more about?"
                new_stage = "qa"
            else:
                script_body = "I understand. When would be a better time to reach you about this {{company_name}} opportunity?"
                new_stage = "greeting"

        elif conversation.current_stage == "qualification":
            if wants_scheduling:
                script_body = "Based on our conversation, you seem like a great fit for the {{company_name}} opportunity! I'd like to schedule a more detailed discussion with our franchise development team. Would you prefer a call this week or next week?"
                new_stage = "scheduling"
            elif is_question:
                script_body = "That's a great question about {{company_name}}. Based on our franchise documentation, I can provide you with detailed information. Do you have any other questions?"
                new_stage = "qa"
            else:
                script_body = "Thank you for that information. Do you have any questions about the {{company_name}} opportunity?"
                new_stage = "qa"

        elif conversation.current_stage == "qa":
            if wants_scheduling:
                script_body = "Perfect! I'll send you a calendar link to schedule a detailed discussion with our franchise development team."
                new_stage = "scheduling"
            else:
                script_body = "That's a great question about {{company_name}}. Based on our franchise documentation, I can provide you with detailed information. Do you have any other questions?"
                new_stage = "qa"

        elif conversation.current_stage == "scheduling":
            script_body = "Perfect! I'll send you a calendar link to schedule a call with our franchise development team."
            new_stage = "completed"

        else:
            script_body = "Thank you for your continued interest in {{company_name}}. A team member will follow up with you shortly."
            new_stage = conversation.current_stage

        # Update conversation stage
        if new_stage != conversation.current_stage:
            await db.execute(text("""
                UPDATE conversation_states
                SET current_stage = :stage, updated_at = :updated_at
                WHERE id = :conv_id
            """), {
                "stage": new_stage,
                "updated_at": datetime.utcnow(),
                "conv_id": conversation.id
            })
            conversation.current_stage = new_stage

        # Render script with context
        rendered_message = script_body
        for key, value in conversation.context_data.items():
            placeholder = f"{{{{{key}}}}}"
            rendered_message = rendered_message.replace(placeholder, str(value))

        return rendered_message

    except Exception as e:
        logger.error(f"Error in simple agent processing: {e}")
        return "Thank you for your message. A team member will follow up with you shortly."


async def process_inbound_message(message: str) -> Dict[str, Any]:
    """
    Process inbound message using RAG-based QnA system with dynamic franchisor detection

    Args:
        message: The inbound message content

    Returns:
        Dict containing the answer and metadata
    """
    try:
        # Prepare question request for RAG system
        question_request = {
            "question": message,
            "top_k": 5,  # Number of relevant chunks to consider
            "similarity_threshold": 0.7,  # Minimum similarity score
            "include_metadata": True  # Include source information
        }

        # 🆕 Use advanced franchisor detection instead of hardcoded 'aditya'
        franchisor_id = None
        franchisor_name = None
        detection_confidence = 0.0

        async with async_session() as session:
            try:
                # Use the advanced franchisor detection service
                detection_service = get_franchisor_detection_service(session)
                franchisor_id, detection_confidence = await detection_service.detect_franchisor_from_question(
                    question=message,
                    include_confidence=True,
                    use_embeddings=True  # Enable all advanced features
                )

                if franchisor_id:
                    # Get franchisor name for logging
                    franchisor_service = FranchisorService(FranchisorRepository(session))
                    franchisor = await franchisor_service.get_franchisor_by_id(franchisor_id)
                    if franchisor:
                        franchisor_name = franchisor.name
                        question_request["franchisor_id"] = franchisor_id

                        logger.info(
                            "Dynamic franchisor detection successful",
                            franchisor_id=franchisor_id,
                            franchisor_name=franchisor_name,
                            confidence=detection_confidence,
                            question=message[:100]
                        )
                    else:
                        logger.warning(
                            "Detected franchisor ID not found in database",
                            franchisor_id=franchisor_id
                        )
                        franchisor_id = None
                else:
                    logger.info(
                        "No specific franchisor detected, using general search",
                        confidence=detection_confidence,
                        question=message[:100]
                    )

            except Exception as detection_error:
                logger.error(
                    "Error in franchisor detection, falling back to general search",
                    error=str(detection_error),
                    question=message[:100]
                )
                # Continue without franchisor-specific context

        # Get answer from RAG system
        answer_result = await ask_question(question_request)

        # Enhance the result with detection metadata
        if "metadata" not in answer_result:
            answer_result["metadata"] = {}

        answer_result["metadata"].update({
            "franchisor_detection": {
                "detected_franchisor_id": franchisor_id,
                "detected_franchisor_name": franchisor_name,
                "detection_confidence": detection_confidence,
                "detection_method": "advanced_openai_detection"
            }
        })

        # Log the interaction with enhanced details
        logger.info(
            "RAG answer generated with franchisor detection",
            question=message,
            answer=answer_result.get("answer", "No answer generated"),
            sources=answer_result.get("sources", []),
            processing_time=answer_result.get("processing_time_ms"),
            detected_franchisor=franchisor_name,
            detection_confidence=detection_confidence
        )

        return answer_result

    except Exception as e:
        logger.error(
            "Failed to process message with RAG",
            error=str(e),
            question=message,
            exc_info=True
        )
        raise


def print_webhook_beautifully(webhook_data: Dict[str, Any], answer_result: Dict[str, Any] = None, chatbot_result: Dict[str, Any] = None) -> None:
    """
    Print webhook data in a beautiful format to the terminal

    Args:
        webhook_data: The webhook payload dictionary
        answer_result: Optional answer result from RAG system
        chatbot_result: Optional result from SMS chatbot processing
    """
    print("\n" + "="*80)
    print("🔔 KUDOSITY WEBHOOK RECEIVED")
    print("="*80)
    
    # Print timestamp
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"📅 Received at: {current_time}")
    
    # Print event type prominently
    event_type = webhook_data.get("event_type", "UNKNOWN")
    print(f"📋 Event Type: {event_type}")
    
    # Print webhook timestamp if available
    webhook_timestamp = webhook_data.get("timestamp")
    if webhook_timestamp:
        print(f"⏰ Webhook Timestamp: {webhook_timestamp}")
    
    print("-" * 80)
    
    # Print specific details based on event type
    if event_type == WebhookEventType.SMS_INBOUND:
        mo = webhook_data.get("mo", {})
        print(f"📨 {event_type} DETAILS:")
        print(f"   Message ID: {mo.get('id', 'N/A')}")
        print(f"   From: {mo.get('sender', 'N/A')}")
        print(f"   To: {mo.get('recipient', 'N/A')}")
        print(f"   Message: {mo.get('message', 'N/A')}")
        
        # Print RAG answer if available
        if answer_result:
            print("\n🤖 AI ANSWER:")
            print("-" * 80)
            print(f"Question: {mo.get('message', 'N/A')}")
            print(f"Answer: {answer_result.get('answer', 'No answer generated')}")
            if answer_result.get('sources'):
                print("\nSources:")
                for source in answer_result.get('sources', []):
                    print(f"- {source}")
            print("-" * 80)

    # Print SMS Chatbot Results
    if chatbot_result:
        print("\n" + "🤖 SMS CHATBOT PROCESSING")
        print("-" * 80)

        if chatbot_result.get('success'):
            print(f"✅ Chatbot Status: SUCCESS")
            print(f"📱 Phone Number: {chatbot_result.get('phone_number', 'Unknown')}")
            print(f"💬 Agent Response: {chatbot_result.get('agent_response', 'No response')}")
            print(f"📊 Conversation Stage: {chatbot_result.get('conversation_stage', 'Unknown')}")
            print(f"🆔 Conversation ID: {chatbot_result.get('conversation_id', 'Unknown')}")
        else:
            print(f"❌ Chatbot Status: FAILED")
            print(f"🚨 Error: {chatbot_result.get('error', 'Unknown error')}")
            print(f"💬 Fallback Response: {chatbot_result.get('agent_response', 'No response')}")

        print("-" * 80)

    print("="*80)
    print("✅ Webhook processing completed")
    print("="*80 + "\n")


@router.post(
    "/kudosity",
    response_model=WebhookReceiveResponse,
    summary="Receive Kudosity Webhook",
    description="Endpoint for receiving and processing Kudosity webhook events",
    responses={
        200: {
            "description": "Webhook processed successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": {
                            "title": "Webhook Processed",
                            "description": "Webhook event processed successfully"
                        },
                        "data": {
                            "message": "Webhook received and processed successfully",
                            "webhook_id": "123e4567-e89b-12d3-a456-426614174000",
                            "event_type": "SMS_INBOUND"
                        }
                    }
                }
            }
        },
        400: {
            "description": "Invalid webhook payload",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Invalid Webhook",
                            "description": "The webhook payload is invalid or malformed"
                        },
                        "error_code": "VALIDATION_ERROR"
                    }
                }
            }
        },
        401: {
            "description": "Unauthorized - Invalid or missing authentication",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Authentication Required",
                            "description": "Valid authentication credentials are required"
                        },
                        "error_code": "UNAUTHORIZED"
                    }
                }
            }
        },
        500: {
            "description": "Internal server error",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Processing Error",
                            "description": "An error occurred while processing the webhook"
                        },
                        "error_code": "INTERNAL_SERVER_ERROR"
                    }
                }
            }
        }
    },
)
async def receive_webhook(
    request: Request
) -> JSONResponse:
    """
    Process incoming Kudosity webhook events

    Args:
        request: The incoming webhook request

    Returns:
        JSONResponse: Webhook processing result
    """
    try:
        # Get raw payload
        body = await request.body()
        try:
            payload = json.loads(body.decode('utf-8'))
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse webhook JSON: {e}")
            return create_error_response(
                error_code=ErrorCodes.VALIDATION_ERROR,
                message_title="Invalid JSON",
                message_description="Failed to parse webhook payload JSON",
                status_code=status.HTTP_400_BAD_REQUEST
            )
        
        # Validate webhook payload
        try:
            webhook_data = WebhookRequest(**payload)
        except ValidationError as e:
            logger.error(
                "Invalid webhook payload",
                error=str(e),
                payload=payload,
                exc_info=True
            )
            return create_error_response(
                error_code=ErrorCodes.VALIDATION_ERROR,
                message_title="Invalid Webhook",
                message_description="The webhook payload is invalid or malformed",
                status_code=status.HTTP_400_BAD_REQUEST
            )
        
        # Log webhook receipt
        logger.info(
            "Webhook received",
            event_type=webhook_data.event_type,
            timestamp=webhook_data.timestamp
        )
        
        # Process SMS inbound events
        if webhook_data.event_type == WebhookEventType.SMS_INBOUND:
            if not webhook_data.mo or not webhook_data.mo.message:
                return create_error_response(
                    error_code=ErrorCodes.VALIDATION_ERROR,
                    message_title="Invalid SMS Inbound",
                    message_description="Missing message content in SMS inbound webhook",
                    status_code=status.HTTP_400_BAD_REQUEST
                )

            # Process message with SMS Chatbot system
            chatbot_result = None
            try:
                # Get database session
                async with async_session() as db:
                    chatbot_result = await process_sms_chatbot_message(
                        phone_number=webhook_data.mo.sender,
                        message_text=webhook_data.mo.message,
                        message_id=webhook_data.mo.id,
                        db=db
                    )

                logger.info(f"SMS Chatbot processed message: {chatbot_result.get('success', False)}")

            except Exception as e:
                logger.error(
                    "Failed to process SMS chatbot message",
                    error=str(e),
                    phone=webhook_data.mo.sender,
                    message=webhook_data.mo.message,
                    exc_info=True
                )
                # Continue with fallback processing

            # Fallback: Process message with RAG system for Q&A
            try:
                answer_result = await process_inbound_message(webhook_data.mo.message)

                # Print beautifully to terminal (include chatbot result)
                print_webhook_beautifully(payload, answer_result, chatbot_result)

            except Exception as e:
                logger.error(
                    "Failed to process inbound message with RAG",
                    error=str(e),
                    message=webhook_data.mo.message,
                    exc_info=True
                )
                # Continue processing even if RAG fails
        
        return create_success_response(
            data=WebhookReceiveResponse(
                message="Webhook received and processed successfully",
                webhook_id=getattr(webhook_data, 'webhook_id', None),
                event_type=webhook_data.event_type
            ).model_dump(),
            message_title="Webhook Processed",
            message_description="Webhook event processed successfully",
            status_code=status.HTTP_200_OK
        )
        
    except Exception as e:
        logger.error(
            "Webhook processing failed",
            error=str(e),
            exc_info=True
        )
        return create_error_response(
            error_code=ErrorCodes.UNKNOWN_ERROR,
            message_title="Processing Error",
            message_description="An error occurred while processing the webhook",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get(
    "/health",
    response_model=WebhookHealthResponse,
    summary="Webhook Health Check",
    description="Check the health status of the webhook service and its dependencies",
    responses={
        200: {
            "description": "Service health status",
            "content": {
                "application/json": {
                    "example": {
                        "status": "healthy",
                        "qna_available": True,
                        "timestamp": "2024-03-20T10:00:00Z"
                    }
                }
            }
        }
    },
    dependencies=[Depends(get_current_user)]
)
async def webhook_health(
    current_user: dict = Depends(get_current_user)
) -> WebhookHealthResponse:
    """
    Check webhook service health status
    
    Args:
        current_user: The authenticated user making the request
        
    Returns:
        WebhookHealthResponse: Health check result
    """
    try:
        # Check QnA/RAG system availability
        qna_available = True
        try:
            # Simple test question to verify RAG system
            await ask_question({"question": "test", "top_k": 1})
        except Exception:
            qna_available = False
        
        return WebhookHealthResponse(
            status="healthy",
            qna_available=qna_available,
            timestamp=datetime.now()
        )
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail="Failed to determine service health status")
