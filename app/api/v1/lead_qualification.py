"""
Lead Qualification API Endpoints

FastAPI endpoints for managing lead qualification questions, sessions, and scoring.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import secrets
import logging

from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
from pydantic import BaseModel, EmailStr, Field

from app.core.database import get_db
from app.models.lead_qualification import (
    Lead, PreQualificationQuestion, LeadResponse, 
    LeadQualificationSummary, QualificationSession, QuestionTemplate
)
from app.agents.question_generation_agent import QuestionGenerationAgent
from app.services.lead_qualification_service import LeadQualificationService

logger = logging.getLogger(__name__)
router = APIRouter()


# Pydantic models for API
class LeadCreate(BaseModel):
    email: EmailStr
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    phone: Optional[str] = None
    location: Optional[str] = None
    source: Optional[str] = "website"


class LeadResponse(BaseModel):
    id: str
    email: str
    first_name: Optional[str]
    last_name: Optional[str]
    status: str
    created_at: datetime


class QuestionResponse(BaseModel):
    id: str
    question_id: str
    category: str
    question_text: str
    expected_answers: List[str]
    score_weight: int


class AnswerSubmission(BaseModel):
    session_token: str
    question_id: str
    answer: str
    response_time_seconds: Optional[int] = None


class QualificationResult(BaseModel):
    lead_id: str
    total_score: int
    max_possible_score: int
    percentage_score: float
    is_qualified: bool
    qualification_level: str
    questions_answered: int
    total_questions: int


class SessionStart(BaseModel):
    session_token: str
    lead_id: str
    questions: List[QuestionResponse]
    expires_at: datetime


# API Endpoints

@router.post("/leads", response_model=LeadResponse)
async def create_lead(
    lead_data: LeadCreate,
    db: Session = Depends(get_db)
):
    """Create a new lead"""
    
    # Check if lead already exists
    existing_lead = db.query(Lead).filter(Lead.email == lead_data.email).first()
    if existing_lead:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="Lead with this email already exists"
        )
    
    # Create new lead
    lead = Lead(
        email=lead_data.email,
        first_name=lead_data.first_name,
        last_name=lead_data.last_name,
        phone=lead_data.phone,
        location=lead_data.location,
        source=lead_data.source,
        status="new"
    )
    
    db.add(lead)
    db.commit()
    db.refresh(lead)
    
    logger.info(f"Created new lead: {lead.email}")
    
    return LeadResponse(
        id=str(lead.id),
        email=lead.email,
        first_name=lead.first_name,
        last_name=lead.last_name,
        status=lead.status,
        created_at=lead.created_at
    )


@router.post("/qualification/start", response_model=SessionStart)
async def start_qualification_session(
    lead_id: str,
    template_name: Optional[str] = "coochie_lawn_care",
    request: Request = None,
    db: Session = Depends(get_db)
):
    """Start a qualification session for a lead"""
    
    # Verify lead exists
    lead = db.query(Lead).filter(Lead.id == lead_id).first()
    if not lead:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Lead not found"
        )
    
    # Get question template
    template = db.query(QuestionTemplate).filter(
        QuestionTemplate.template_name == template_name,
        QuestionTemplate.is_active == True
    ).first()
    
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Question template '{template_name}' not found"
        )
    
    # Get questions for the template
    questions = db.query(PreQualificationQuestion).filter(
        PreQualificationQuestion.id.in_(template.question_ids),
        PreQualificationQuestion.is_active == True
    ).all()
    
    if not questions:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No active questions found for template"
        )
    
    # Create session
    session_token = secrets.token_urlsafe(32)
    expires_at = datetime.utcnow() + timedelta(hours=2)  # 2-hour session
    
    session = QualificationSession(
        lead_id=lead.id,
        session_token=session_token,
        status="active",
        current_question_index=0,
        questions_order=[str(q.id) for q in questions],
        expires_at=expires_at,
        user_agent=request.headers.get("User-Agent") if request else None,
        ip_address=request.client.host if request else None
    )
    
    db.add(session)
    
    # Update lead status
    lead.status = "qualifying"
    
    db.commit()
    db.refresh(session)
    
    logger.info(f"Started qualification session for lead {lead_id}")
    
    # Prepare question responses
    question_responses = [
        QuestionResponse(
            id=str(q.id),
            question_id=q.question_id,
            category=q.category,
            question_text=q.question_text,
            expected_answers=q.expected_answers,
            score_weight=q.score_weight
        )
        for q in questions
    ]
    
    return SessionStart(
        session_token=session_token,
        lead_id=str(lead.id),
        questions=question_responses,
        expires_at=expires_at
    )


@router.get("/qualification/question/{session_token}")
async def get_current_question(
    session_token: str,
    db: Session = Depends(get_db)
):
    """Get the current question for a qualification session"""
    
    # Get session
    session = db.query(QualificationSession).filter(
        QualificationSession.session_token == session_token,
        QualificationSession.status == "active"
    ).first()
    
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Session not found or expired"
        )
    
    # Check if session is expired
    if session.expires_at < datetime.utcnow():
        session.status = "expired"
        db.commit()
        raise HTTPException(
            status_code=status.HTTP_410_GONE,
            detail="Session has expired"
        )
    
    # Check if all questions are answered
    if session.current_question_index >= len(session.questions_order):
        return {"status": "completed", "message": "All questions answered"}
    
    # Get current question
    question_id = session.questions_order[session.current_question_index]
    question = db.query(PreQualificationQuestion).filter(
        PreQualificationQuestion.id == question_id
    ).first()
    
    if not question:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Question not found"
        )
    
    return {
        "session_token": session_token,
        "question_index": session.current_question_index + 1,
        "total_questions": len(session.questions_order),
        "question": QuestionResponse(
            id=str(question.id),
            question_id=question.question_id,
            category=question.category,
            question_text=question.question_text,
            expected_answers=question.expected_answers,
            score_weight=question.score_weight
        )
    }


@router.post("/qualification/answer")
async def submit_answer(
    answer_data: AnswerSubmission,
    db: Session = Depends(get_db)
):
    """Submit an answer to a qualification question"""
    
    service = LeadQualificationService(db)
    
    try:
        result = await service.process_answer(
            session_token=answer_data.session_token,
            question_id=answer_data.question_id,
            answer=answer_data.answer,
            response_time_seconds=answer_data.response_time_seconds
        )
        
        return result
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error processing answer: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error processing answer"
        )


@router.get("/qualification/results/{lead_id}", response_model=QualificationResult)
async def get_qualification_results(
    lead_id: str,
    db: Session = Depends(get_db)
):
    """Get qualification results for a lead"""
    
    # Get qualification summary
    summary = db.query(LeadQualificationSummary).filter(
        LeadQualificationSummary.lead_id == lead_id
    ).first()
    
    if not summary:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Qualification results not found"
        )
    
    return QualificationResult(
        lead_id=str(summary.lead_id),
        total_score=summary.total_score,
        max_possible_score=summary.max_possible_score,
        percentage_score=summary.percentage_score,
        is_qualified=summary.is_fully_qualified,
        qualification_level=summary.qualification_level,
        questions_answered=summary.questions_answered,
        total_questions=summary.total_questions
    )


@router.get("/questions")
async def get_questions(
    category: Optional[str] = None,
    active_only: bool = True,
    db: Session = Depends(get_db)
):
    """Get qualification questions"""
    
    query = db.query(PreQualificationQuestion)
    
    if active_only:
        query = query.filter(PreQualificationQuestion.is_active == True)
    
    if category:
        query = query.filter(PreQualificationQuestion.category == category)
    
    questions = query.all()
    
    return [
        QuestionResponse(
            id=str(q.id),
            question_id=q.question_id,
            category=q.category,
            question_text=q.question_text,
            expected_answers=q.expected_answers,
            score_weight=q.score_weight
        )
        for q in questions
    ]


@router.post("/questions/generate")
async def generate_questions_from_document(
    document_content: str = Field(..., description="Document content to analyze"),
    franchise_type: str = Field(default="lawn_care", description="Type of franchise"),
    num_questions: int = Field(default=10, ge=5, le=20, description="Number of questions to generate"),
    template_name: Optional[str] = Field(default=None, description="Template name to save questions"),
    db: Session = Depends(get_db)
):
    """Generate qualification questions from document content using AI"""
    
    agent = QuestionGenerationAgent()
    
    try:
        # Generate questions
        questions = await agent.generate_questions_from_document(
            document_content=document_content,
            franchise_type=franchise_type,
            num_questions=num_questions
        )
        
        if not questions:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to generate questions"
            )
        
        # Save as template if requested
        if template_name:
            template = await agent.create_question_template(
                db=db,
                template_name=template_name,
                franchise_type=franchise_type,
                questions=questions,
                description=f"AI-generated questions for {franchise_type} franchise"
            )
            
            return {
                "questions": questions,
                "template_created": True,
                "template_name": template_name,
                "template_id": str(template.id)
            }
        
        return {
            "questions": questions,
            "template_created": False
        }
        
    except Exception as e:
        logger.error(f"Error generating questions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error generating questions"
        )


@router.get("/templates")
async def get_question_templates(
    franchise_type: Optional[str] = None,
    active_only: bool = True,
    db: Session = Depends(get_db)
):
    """Get question templates"""
    
    query = db.query(QuestionTemplate)
    
    if active_only:
        query = query.filter(QuestionTemplate.is_active == True)
    
    if franchise_type:
        query = query.filter(QuestionTemplate.franchise_type == franchise_type)
    
    templates = query.all()
    
    return [
        {
            "id": str(t.id),
            "template_name": t.template_name,
            "franchise_type": t.franchise_type,
            "description": t.description,
            "total_questions": len(t.question_ids),
            "total_possible_score": t.total_possible_score,
            "qualification_threshold": t.qualification_threshold,
            "created_at": t.created_at
        }
        for t in templates
    ]


@router.get("/analytics/summary")
async def get_qualification_analytics(
    days: int = Field(default=30, ge=1, le=365, description="Number of days to analyze"),
    db: Session = Depends(get_db)
):
    """Get qualification analytics summary"""
    
    service = LeadQualificationService(db)
    analytics = await service.get_qualification_analytics(days=days)
    
    return analytics
