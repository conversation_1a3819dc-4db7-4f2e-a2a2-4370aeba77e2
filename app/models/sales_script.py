"""
Sales Script Database Models

Models for storing and managing dynamic sales script templates with Jinja2 placeholders.
"""

from sqlalchemy import Column, String, Text, Boolean, DateTime, ForeignKey, Integer, func
from sqlalchemy.dialects.postgresql import UUID, JSON
from sqlalchemy.orm import relationship
import uuid

from app.core.database.connection import Base


class SalesScript(Base):
    """Model for storing sales script templates with dynamic placeholders"""
    __tablename__ = "sales_scripts"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    franchisor_id = Column(UUID(as_uuid=True), ForeignKey("franchisors.id"), nullable=False)
    script_title = Column(String(255), nullable=False)  # e.g., "Initial Message", "Follow-Up"
    script_body = Column(Text, nullable=False)  # Contains Jinja2 placeholders like {{company_name}}
    script_type = Column(String(50), default="greeting")  # greeting, qualification, follow_up, etc.
    is_active = Column(Boolean, default=True, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)

    # Relationships
    franchisor = relationship("Franchisor", back_populates="sales_scripts")

    def __repr__(self):
        return f"<SalesScript(id={self.id}, title={self.script_title}, franchisor_id={self.franchisor_id})>"


class ConversationState(Base):
    """Model for tracking SMS conversation state and progress"""
    __tablename__ = "conversation_states"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    lead_id = Column(UUID(as_uuid=True), ForeignKey("leads.id"), nullable=False)
    phone_number = Column(String(20), nullable=False, index=True)
    franchisor_id = Column(UUID(as_uuid=True), ForeignKey("franchisors.id"), nullable=False)
    
    # Conversation flow tracking
    current_stage = Column(String(50), default="greeting")  # greeting, qualification, qa, scheduling, completed
    qualification_session_id = Column(UUID(as_uuid=True), nullable=True)  # Will add FK constraint later
    current_question_index = Column(Integer, default=0)
    
    # Context data for script rendering
    context_data = Column(JSON, nullable=True)  # Store dynamic context like rep_name, etc.
    
    # State management
    is_active = Column(Boolean, default=True)
    last_message_at = Column(DateTime(timezone=True), server_default=func.now())
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)

    # Relationships
    lead = relationship("Lead", back_populates="conversation_states")
    franchisor = relationship("Franchisor")
    # Note: QualificationSession relationship will be configured after all models are loaded

    def __repr__(self):
        return f"<ConversationState(id={self.id}, phone={self.phone_number}, stage={self.current_stage})>"


class ConversationMessage(Base):
    """Model for storing conversation message history"""
    __tablename__ = "conversation_messages"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    conversation_state_id = Column(UUID(as_uuid=True), ForeignKey("conversation_states.id"), nullable=False)
    
    # Message details
    message_text = Column(Text, nullable=False)
    message_type = Column(String(20), nullable=False)  # inbound, outbound
    message_source = Column(String(50), default="sms")  # sms, system, agent
    
    # Agent processing
    agent_response = Column(Text, nullable=True)  # AI agent's response
    processing_time_ms = Column(Integer, nullable=True)
    
    # Metadata
    kudosity_message_id = Column(String(100), nullable=True)  # External message ID
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)

    # Relationships
    conversation_state = relationship("ConversationState", back_populates="messages")


# Add missing relationships to ConversationState
ConversationState.messages = relationship("ConversationMessage", back_populates="conversation_state")
