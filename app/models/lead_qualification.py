"""
Lead Qualification Database Models

Models for managing lead qualification questions, responses, and scoring.
"""

from sqlalchemy import Column, String, Integer, Float, Boolean, DateTime, Text, ForeignKey, JSON
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid

from app.core.database.connection import Base
from app.models.lead import Lead


class PreQualificationQuestion(Base):
    """Model for storing pre-qualification questions"""
    __tablename__ = "pre_qualification_questions"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    question_id = Column(String(10), unique=True, nullable=False, index=True)  # Q001, Q002, etc.
    category = Column(String(50), nullable=False, index=True)  # Financial, Location, Training, etc.
    question_text = Column(Text, nullable=False)
    expected_answers = Column(JSON, nullable=False)  # List of acceptable answers
    score_weight = Column(Integer, nullable=False, default=10)  # Points for correct answer
    context_info = Column(JSON, nullable=True)  # Context from PDF extraction
    is_active = Column(Boolean, default=True, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    responses = relationship("LeadResponse", back_populates="question")

    def __repr__(self):
        return f"<PreQualificationQuestion {self.question_id}: {self.question_text[:50]}...>"


# Lead model is imported from app.models.lead


class LeadResponse(Base):
    """Model for storing lead responses to qualification questions"""
    __tablename__ = "lead_responses"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    lead_id = Column(UUID(as_uuid=True), ForeignKey("leads.id"), nullable=False)
    question_id = Column(UUID(as_uuid=True), ForeignKey("pre_qualification_questions.id"), nullable=False)
    question_text = Column(Text, nullable=False)  # Stored for audit trail
    lead_answer = Column(Text, nullable=False)
    is_qualified = Column(Boolean, nullable=False)  # True if answer matches expected
    score_awarded = Column(Integer, nullable=False, default=0)
    confidence_score = Column(Float, nullable=True)  # AI confidence in answer matching
    response_time_seconds = Column(Integer, nullable=True)  # Time taken to answer
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    lead = relationship("Lead", back_populates="responses")
    question = relationship("PreQualificationQuestion", back_populates="responses")

    def __repr__(self):
        return f"<LeadResponse {self.lead_id}: Q{self.question_id} - {self.score_awarded} pts>"


class LeadQualificationSummary(Base):
    """Model for storing lead qualification summary and final results"""
    __tablename__ = "lead_qualification_summaries"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    lead_id = Column(UUID(as_uuid=True), ForeignKey("leads.id"), nullable=False, unique=True)
    total_score = Column(Integer, nullable=False, default=0)
    max_possible_score = Column(Integer, nullable=False)
    percentage_score = Column(Float, nullable=False, default=0.0)
    is_fully_qualified = Column(Boolean, nullable=False, default=False)  # True if >= 80%
    qualification_level = Column(String(20), nullable=False, default="unqualified")  # unqualified, qualified, highly_qualified
    questions_answered = Column(Integer, nullable=False, default=0)
    total_questions = Column(Integer, nullable=False)
    average_response_time = Column(Float, nullable=True)  # Average time per question
    qualification_notes = Column(Text, nullable=True)  # Additional notes or AI insights
    qualified_at = Column(DateTime(timezone=True), nullable=True)  # When qualification was completed
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    lead = relationship("Lead", back_populates="qualification_summary")

    def __repr__(self):
        return f"<LeadQualificationSummary {self.lead_id}: {self.percentage_score:.1f}% - {self.qualification_level}>"


class QualificationSession(Base):
    """Model for tracking qualification sessions"""
    __tablename__ = "qualification_sessions"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    lead_id = Column(UUID(as_uuid=True), ForeignKey("leads.id"), nullable=False)
    session_token = Column(String(255), nullable=False, unique=True, index=True)
    status = Column(String(20), default="active", nullable=False)  # active, completed, abandoned
    current_question_index = Column(Integer, default=0, nullable=False)
    questions_order = Column(JSON, nullable=False)  # Ordered list of question IDs
    started_at = Column(DateTime(timezone=True), server_default=func.now())
    completed_at = Column(DateTime(timezone=True), nullable=True)
    expires_at = Column(DateTime(timezone=True), nullable=False)  # Session expiration
    user_agent = Column(String(500), nullable=True)
    ip_address = Column(String(45), nullable=True)

    def __repr__(self):
        return f"<QualificationSession {self.session_token}: {self.status}>"


class QuestionTemplate(Base):
    """Model for storing question templates for different franchise types"""
    __tablename__ = "question_templates"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    template_name = Column(String(100), nullable=False, unique=True)
    franchise_type = Column(String(100), nullable=False)  # e.g., "lawn_care", "food_service"
    description = Column(Text, nullable=True)
    question_ids = Column(JSON, nullable=False)  # List of question IDs in this template
    total_possible_score = Column(Integer, nullable=False)
    qualification_threshold = Column(Integer, nullable=False)  # Minimum score to qualify
    is_active = Column(Boolean, default=True, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def __repr__(self):
        return f"<QuestionTemplate {self.template_name}: {self.franchise_type}>"


class QualificationAnalytics(Base):
    """Model for storing qualification analytics and insights"""
    __tablename__ = "qualification_analytics"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    date = Column(DateTime(timezone=True), nullable=False, index=True)
    total_leads = Column(Integer, default=0)
    qualified_leads = Column(Integer, default=0)
    qualification_rate = Column(Float, default=0.0)  # Percentage of leads that qualify
    average_score = Column(Float, default=0.0)
    average_completion_time = Column(Float, default=0.0)  # Minutes
    most_difficult_question_id = Column(String(10), nullable=True)
    easiest_question_id = Column(String(10), nullable=True)
    common_failure_categories = Column(JSON, nullable=True)  # Categories where leads fail most
    insights = Column(JSON, nullable=True)  # AI-generated insights
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    def __repr__(self):
        return f"<QualificationAnalytics {self.date}: {self.qualification_rate:.1f}% qualified>"


# Indexes for performance
from sqlalchemy import Index

# Create indexes for common queries
Index('idx_lead_responses_lead_question', LeadResponse.lead_id, LeadResponse.question_id)
Index('idx_qualification_sessions_lead', QualificationSession.lead_id)
Index('idx_qualification_sessions_token', QualificationSession.session_token)
Index('idx_pre_qualification_questions_category', PreQualificationQuestion.category)
Index('idx_leads_status', Lead.status)
Index('idx_leads_email', Lead.email)
Index('idx_qualification_summaries_qualified', LeadQualificationSummary.is_fully_qualified)
Index('idx_qualification_analytics_date', QualificationAnalytics.date)
