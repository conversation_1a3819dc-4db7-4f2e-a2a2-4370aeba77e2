"""
Enhanced Vector Service Integration
Integrates the enhanced vector system with the existing FastAPI application
"""

import os
import asyncio
import logging
from typing import Dict, Any, Optional, List
from functools import lru_cache
import structlog

# Import enhanced system components
from enhanced_vector_system.integration_guide import EnhancedVectorSystem, EnhancedSystemConfig
from enhanced_vector_system.latest_embedding_service import EmbeddingModel
from enhanced_vector_system.advanced_reranker import RerankerModel
from enhanced_vector_system.smart_chunking_v2 import ChunkingStrategy
from enhanced_vector_system.hybrid_search_engine import SearchMode
from enhanced_vector_system.advanced_rag_system import RAGStrategy

logger = structlog.get_logger()


class EnhancedVectorServiceIntegration:
    """Integration service for enhanced vector system"""
    
    def __init__(self):
        self.enhanced_system: Optional[EnhancedVectorSystem] = None
        self.is_initialized = False
        self.fallback_to_current = False
        
    async def initialize(self):
        """Initialize the enhanced vector system"""
        try:
            # Check if enhanced system is enabled
            use_enhanced = os.getenv("USE_ENHANCED_VECTOR_SYSTEM", "false").lower() == "true"
            
            if not use_enhanced:
                logger.info("Enhanced vector system disabled, using current system")
                self.fallback_to_current = True
                return
            
            # Get configuration from environment
            config = self._get_enhanced_config()
            
            # Initialize enhanced system
            self.enhanced_system = EnhancedVectorSystem(config)
            await self.enhanced_system.initialize()
            
            self.is_initialized = True
            logger.info("Enhanced vector system initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize enhanced vector system: {e}")
            logger.info("Falling back to current system")
            self.fallback_to_current = True
    
    def _get_enhanced_config(self) -> EnhancedSystemConfig:
        """Get enhanced system configuration from environment"""
        return EnhancedSystemConfig(
            # API Keys
            openai_api_key=os.getenv("OPENAI_API_KEY"),
            cohere_api_key=os.getenv("COHERE_API_KEY"),
            
            # Database URLs
            database_url=os.getenv("DATABASE_URL"),
            redis_url=os.getenv("REDIS_URL"),
            
            # Model Selection
            embedding_model=self._get_embedding_model(),
            reranker_model=self._get_reranker_model(),
            
            # Strategy Selection
            chunking_strategy=self._get_chunking_strategy(),
            search_mode=self._get_search_mode(),
            rag_strategy=self._get_rag_strategy(),
            
            # Performance Settings
            enable_caching=os.getenv("ENABLE_VECTOR_CACHING", "true").lower() == "true",
            enable_gpu=False,  # Disable GPU for now
            max_workers=int(os.getenv("CONNECTION_POOL_SIZE", "4"))
        )
    
    def _get_embedding_model(self) -> EmbeddingModel:
        """Get embedding model from environment"""
        model_name = os.getenv("ENHANCED_EMBEDDING_MODEL", "text-embedding-3-large")
        
        model_mapping = {
            "text-embedding-3-large": EmbeddingModel.OPENAI_TEXT_EMBEDDING_3_LARGE,
            "text-embedding-3-small": EmbeddingModel.OPENAI_TEXT_EMBEDDING_3_SMALL,
            "embed-english-v3.0": EmbeddingModel.COHERE_EMBED_V3_ENGLISH,
            "embed-multilingual-v3.0": EmbeddingModel.COHERE_EMBED_V3_MULTILINGUAL
        }
        
        return model_mapping.get(model_name, EmbeddingModel.OPENAI_TEXT_EMBEDDING_3_LARGE)
    
    def _get_reranker_model(self) -> RerankerModel:
        """Get reranker model from environment"""
        model_name = os.getenv("ENHANCED_RERANKER_MODEL", "rerank-3")
        
        model_mapping = {
            "rerank-3": RerankerModel.COHERE_RERANK_3,
            "rerank-english-v3.0": RerankerModel.COHERE_RERANK_ENGLISH_V3,
            "bge-reranker-v2-m3": RerankerModel.BGE_RERANKER_V2_M3,
            "ms-marco-minilm": RerankerModel.MS_MARCO_MINILM
        }
        
        return model_mapping.get(model_name, RerankerModel.COHERE_RERANK_3)
    
    def _get_chunking_strategy(self) -> ChunkingStrategy:
        """Get chunking strategy from environment"""
        strategy_name = os.getenv("ENHANCED_CHUNKING_STRATEGY", "hybrid")
        
        strategy_mapping = {
            "fixed_size": ChunkingStrategy.FIXED_SIZE,
            "semantic": ChunkingStrategy.SEMANTIC,
            "hierarchical": ChunkingStrategy.HIERARCHICAL,
            "adaptive": ChunkingStrategy.ADAPTIVE,
            "hybrid": ChunkingStrategy.HYBRID
        }
        
        return strategy_mapping.get(strategy_name, ChunkingStrategy.HYBRID)
    
    def _get_search_mode(self) -> SearchMode:
        """Get search mode from environment"""
        mode_name = os.getenv("ENHANCED_SEARCH_MODE", "hybrid")
        
        mode_mapping = {
            "dense_only": SearchMode.DENSE_ONLY,
            "sparse_only": SearchMode.SPARSE_ONLY,
            "hybrid": SearchMode.HYBRID,
            "adaptive": SearchMode.ADAPTIVE
        }
        
        return mode_mapping.get(mode_name, SearchMode.HYBRID)
    
    def _get_rag_strategy(self) -> RAGStrategy:
        """Get RAG strategy from environment"""
        strategy_name = os.getenv("ENHANCED_RAG_STRATEGY", "fusion")
        
        strategy_mapping = {
            "basic": RAGStrategy.BASIC,
            "multi_query": RAGStrategy.MULTI_QUERY,
            "self_rag": RAGStrategy.SELF_RAG,
            "adaptive": RAGStrategy.ADAPTIVE,
            "fusion": RAGStrategy.FUSION
        }
        
        return strategy_mapping.get(strategy_name, RAGStrategy.FUSION)
    
    async def process_document(self, 
                             text: str, 
                             document_id: str,
                             document_type: str = "pdf",
                             metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Process document with enhanced or fallback system
        
        Args:
            text: Document text
            document_id: Document identifier
            document_type: Type of document
            metadata: Additional metadata
            
        Returns:
            Processing results
        """
        if self.is_initialized and self.enhanced_system:
            try:
                # Use enhanced system
                result = await self.enhanced_system.process_document(
                    text=text,
                    document_id=document_id,
                    document_type=document_type,
                    metadata=metadata
                )
                
                # Add enhanced system indicator
                result['enhanced_system_used'] = True
                result['system_version'] = '2024-2025'
                
                return result
                
            except Exception as e:
                logger.error(f"Enhanced system processing failed: {e}")
                logger.info("Falling back to current system")
        
        # Fallback to current system
        return await self._process_document_fallback(text, document_id, document_type, metadata)
    
    async def answer_question(self, 
                            question: str,
                            context: Optional[str] = None,
                            top_k: int = 6,
                            similarity_threshold: float = 0.7) -> Dict[str, Any]:
        """
        Answer question with enhanced or fallback system
        
        Args:
            question: User question
            context: Additional context
            top_k: Number of results to return
            similarity_threshold: Similarity threshold
            
        Returns:
            Answer with metadata
        """
        if self.is_initialized and self.enhanced_system:
            try:
                # Use enhanced system
                result = await self.enhanced_system.answer_question(
                    question=question,
                    context=context
                )
                
                # Add enhanced system indicator
                result['enhanced_system_used'] = True
                result['system_version'] = '2024-2025'
                
                return result
                
            except Exception as e:
                logger.error(f"Enhanced system QA failed: {e}")
                logger.info("Falling back to current system")
        
        # Fallback to current system
        return await self._answer_question_fallback(question, context, top_k, similarity_threshold)
    
    async def _process_document_fallback(self,
                                       text: str,
                                       document_id: str,
                                       document_type: str,
                                       metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Fallback to current document processing system"""
        try:
            # Import current system
            from docqa.ingest import DocumentIngestionService
            from docqa.types import IngestionResult

            ingestion_service = DocumentIngestionService()

            # Create a temporary file with the text content for processing
            import tempfile
            import os

            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as tmp_file:
                tmp_file.write(text)
                tmp_file_path = tmp_file.name

            try:
                # Process with current system using the temporary file
                result = ingestion_service.ingest_document(
                    source=tmp_file_path,
                    force_table="documents",
                    translate=False,
                    extract_charts=False,
                    document_id=document_id
                )

                # Clean up temporary file
                os.unlink(tmp_file_path)

                if result and result.success:
                    return {
                        'success': True,
                        'chunks_created': result.chunks_created,
                        'enhanced_system_used': False,
                        'system_version': 'current',
                        'fallback_reason': 'Enhanced system not available',
                        'document_id': document_id,
                        'table_name': result.table_name
                    }
                else:
                    return {
                        'success': False,
                        'error': result.error_message if result else 'Unknown error',
                        'enhanced_system_used': False,
                        'system_version': 'current'
                    }

            except Exception as e:
                # Clean up temporary file on error
                try:
                    os.unlink(tmp_file_path)
                except:
                    pass
                raise e

        except Exception as e:
            logger.error(f"Fallback processing failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'enhanced_system_used': False,
                'system_version': 'current'
            }
    
    async def _answer_question_fallback(self,
                                      question: str,
                                      context: Optional[str],
                                      top_k: int,
                                      similarity_threshold: float) -> Dict[str, Any]:
        """Fallback to current QA system"""
        try:
            # Import current system
            from docqa.serve import ask_question

            # Add context to question if provided
            enhanced_question = question
            if context:
                enhanced_question = f"Context: {context}\n\nQuestion: {question}"

            # Use current system
            answer = ask_question(
                question=enhanced_question,
                top_k=top_k,
                similarity_threshold=similarity_threshold,
                include_metadata=True
            )

            # Extract sources if available (current system may return metadata)
            sources = []
            if isinstance(answer, dict) and 'sources' in answer:
                sources = answer.get('sources', [])
                answer_text = answer.get('answer', str(answer))
            else:
                answer_text = str(answer)

            return {
                'answer': answer_text,
                'confidence': 0.7,  # Default confidence
                'sources': sources,
                'enhanced_system_used': False,
                'system_version': 'current',
                'fallback_reason': 'Enhanced system not available',
                'processing_time': 0.0,
                'context_length': len(context) if context else 0
            }

        except Exception as e:
            logger.error(f"Fallback QA failed: {e}")
            return {
                'answer': "I apologize, but I encountered an error while processing your question.",
                'confidence': 0.0,
                'sources': [],
                'enhanced_system_used': False,
                'system_version': 'current',
                'error': str(e)
            }
    
    async def get_system_status(self) -> Dict[str, Any]:
        """Get system status and performance metrics"""
        status = {
            'enhanced_system_available': self.is_initialized,
            'fallback_mode': self.fallback_to_current,
            'system_version': '2024-2025' if self.is_initialized else 'current'
        }
        
        if self.is_initialized and self.enhanced_system:
            try:
                # Get enhanced system metrics
                benchmark = await self.enhanced_system.benchmark_system()
                status.update(benchmark)
            except Exception as e:
                status['benchmark_error'] = str(e)
        
        return status


# Global instance
_enhanced_service: Optional[EnhancedVectorServiceIntegration] = None


@lru_cache()
def get_enhanced_vector_service() -> EnhancedVectorServiceIntegration:
    """Get enhanced vector service instance"""
    global _enhanced_service
    if _enhanced_service is None:
        _enhanced_service = EnhancedVectorServiceIntegration()
    return _enhanced_service


async def initialize_enhanced_vector_service():
    """Initialize enhanced vector service"""
    service = get_enhanced_vector_service()
    await service.initialize()
