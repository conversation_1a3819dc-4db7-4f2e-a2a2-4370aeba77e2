"""
Lead Qualification Service

Service layer for handling lead qualification logic, scoring, and evaluation.
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import re
from difflib import SequenceMatcher

from sqlalchemy.orm import Session
from sqlalchemy import func, and_

from app.models.lead_qualification import (
    Lead, PreQualificationQuestion, LeadResponse, 
    LeadQualificationSummary, QualificationSession, QualificationAnalytics
)

logger = logging.getLogger(__name__)


class LeadQualificationService:
    """Service for managing lead qualification process"""
    
    def __init__(self, db: Session):
        self.db = db
    
    async def process_answer(
        self, 
        session_token: str, 
        question_id: str, 
        answer: str,
        response_time_seconds: Optional[int] = None
    ) -> Dict[str, Any]:
        """Process a lead's answer to a qualification question"""
        
        # Get session
        session = self.db.query(QualificationSession).filter(
            QualificationSession.session_token == session_token,
            QualificationSession.status == "active"
        ).first()
        
        if not session:
            raise ValueError("Session not found or expired")
        
        # Check if session is expired
        if session.expires_at < datetime.utcnow():
            session.status = "expired"
            self.db.commit()
            raise ValueError("Session has expired")
        
        # Get question
        question = self.db.query(PreQualificationQuestion).filter(
            PreQualificationQuestion.id == question_id
        ).first()
        
        if not question:
            raise ValueError("Question not found")
        
        # Check if answer already exists
        existing_response = self.db.query(LeadResponse).filter(
            LeadResponse.lead_id == session.lead_id,
            LeadResponse.question_id == question.id
        ).first()
        
        if existing_response:
            raise ValueError("Question already answered")
        
        # Evaluate answer
        evaluation = self._evaluate_answer(answer, question.expected_answers, question.score_weight)
        
        # Create response record
        response = LeadResponse(
            lead_id=session.lead_id,
            question_id=question.id,
            question_text=question.question_text,
            lead_answer=answer,
            is_qualified=evaluation['is_qualified'],
            score_awarded=evaluation['score_awarded'],
            confidence_score=evaluation['confidence_score'],
            response_time_seconds=response_time_seconds
        )
        
        self.db.add(response)
        
        # Update session progress
        session.current_question_index += 1
        
        # Check if qualification is complete
        is_complete = session.current_question_index >= len(session.questions_order)
        
        if is_complete:
            session.status = "completed"
            session.completed_at = datetime.utcnow()
            
            # Calculate final qualification results
            qualification_result = await self._calculate_qualification_results(session.lead_id)
            
            self.db.commit()
            
            return {
                "status": "completed",
                "answer_evaluation": evaluation,
                "qualification_complete": True,
                "qualification_result": qualification_result
            }
        else:
            self.db.commit()
            
            return {
                "status": "continue",
                "answer_evaluation": evaluation,
                "next_question_index": session.current_question_index,
                "questions_remaining": len(session.questions_order) - session.current_question_index
            }
    
    def _evaluate_answer(
        self, 
        answer: str, 
        expected_answers: List[str], 
        max_score: int
    ) -> Dict[str, Any]:
        """Evaluate a lead's answer against expected answers"""
        
        answer_clean = answer.strip().lower()
        
        # Direct match check
        for expected in expected_answers:
            expected_clean = expected.strip().lower()
            
            # Exact match
            if answer_clean == expected_clean:
                return {
                    'is_qualified': True,
                    'score_awarded': max_score,
                    'confidence_score': 1.0,
                    'match_type': 'exact',
                    'matched_answer': expected
                }
            
            # Partial match using similarity
            similarity = SequenceMatcher(None, answer_clean, expected_clean).ratio()
            
            if similarity >= 0.8:  # 80% similarity threshold
                score = int(max_score * similarity)
                return {
                    'is_qualified': True,
                    'score_awarded': score,
                    'confidence_score': similarity,
                    'match_type': 'partial',
                    'matched_answer': expected
                }
        
        # Keyword-based matching for flexible answers
        keyword_match = self._check_keyword_match(answer_clean, expected_answers)
        if keyword_match['score'] > 0:
            return keyword_match
        
        # No match found
        return {
            'is_qualified': False,
            'score_awarded': 0,
            'confidence_score': 0.0,
            'match_type': 'no_match',
            'matched_answer': None
        }
    
    def _check_keyword_match(self, answer: str, expected_answers: List[str]) -> Dict[str, Any]:
        """Check for keyword-based matches in answers"""
        
        # Extract keywords from expected answers
        keywords = set()
        for expected in expected_answers:
            # Extract meaningful words (remove common words)
            words = re.findall(r'\b\w+\b', expected.lower())
            meaningful_words = [w for w in words if len(w) > 2 and w not in {
                'the', 'and', 'or', 'but', 'yes', 'no', 'can', 'will', 'have', 'has'
            }]
            keywords.update(meaningful_words)
        
        # Check how many keywords appear in the answer
        answer_words = set(re.findall(r'\b\w+\b', answer.lower()))
        matched_keywords = keywords.intersection(answer_words)
        
        if matched_keywords:
            match_ratio = len(matched_keywords) / len(keywords) if keywords else 0
            
            if match_ratio >= 0.5:  # At least 50% of keywords match
                score = int(10 * match_ratio)  # Base score of 10, scaled by match ratio
                return {
                    'is_qualified': True,
                    'score_awarded': score,
                    'confidence_score': match_ratio,
                    'match_type': 'keyword',
                    'matched_keywords': list(matched_keywords)
                }
        
        return {'score': 0}
    
    async def _calculate_qualification_results(self, lead_id: str) -> Dict[str, Any]:
        """Calculate final qualification results for a lead"""
        
        # Get all responses for this lead
        responses = self.db.query(LeadResponse).filter(
            LeadResponse.lead_id == lead_id
        ).all()
        
        if not responses:
            raise ValueError("No responses found for lead")
        
        # Calculate totals
        total_score = sum(r.score_awarded for r in responses)
        max_possible_score = sum(
            self.db.query(PreQualificationQuestion.score_weight).filter(
                PreQualificationQuestion.id == r.question_id
            ).scalar() for r in responses
        )
        
        percentage_score = (total_score / max_possible_score * 100) if max_possible_score > 0 else 0
        is_qualified = percentage_score >= 80.0  # 80% threshold
        
        # Determine qualification level
        if percentage_score >= 90:
            qualification_level = "highly_qualified"
        elif percentage_score >= 80:
            qualification_level = "qualified"
        elif percentage_score >= 60:
            qualification_level = "partially_qualified"
        else:
            qualification_level = "unqualified"
        
        # Calculate average response time
        response_times = [r.response_time_seconds for r in responses if r.response_time_seconds]
        avg_response_time = sum(response_times) / len(response_times) if response_times else None
        
        # Create or update qualification summary
        summary = self.db.query(LeadQualificationSummary).filter(
            LeadQualificationSummary.lead_id == lead_id
        ).first()
        
        if summary:
            # Update existing summary
            summary.total_score = total_score
            summary.max_possible_score = max_possible_score
            summary.percentage_score = percentage_score
            summary.is_fully_qualified = is_qualified
            summary.qualification_level = qualification_level
            summary.questions_answered = len(responses)
            summary.average_response_time = avg_response_time
            summary.qualified_at = datetime.utcnow()
            summary.updated_at = datetime.utcnow()
        else:
            # Create new summary
            summary = LeadQualificationSummary(
                lead_id=lead_id,
                total_score=total_score,
                max_possible_score=max_possible_score,
                percentage_score=percentage_score,
                is_fully_qualified=is_qualified,
                qualification_level=qualification_level,
                questions_answered=len(responses),
                total_questions=len(responses),  # Assuming all questions were answered
                average_response_time=avg_response_time,
                qualified_at=datetime.utcnow()
            )
            self.db.add(summary)
        
        # Update lead status
        lead = self.db.query(Lead).filter(Lead.id == lead_id).first()
        if lead:
            lead.status = "qualified" if is_qualified else "disqualified"
        
        self.db.commit()
        
        return {
            "lead_id": str(lead_id),
            "total_score": total_score,
            "max_possible_score": max_possible_score,
            "percentage_score": percentage_score,
            "is_qualified": is_qualified,
            "qualification_level": qualification_level,
            "questions_answered": len(responses),
            "average_response_time": avg_response_time
        }
    
    async def get_qualification_analytics(self, days: int = 30) -> Dict[str, Any]:
        """Get qualification analytics for the specified period"""
        
        start_date = datetime.utcnow() - timedelta(days=days)
        
        # Get leads created in the period
        total_leads = self.db.query(Lead).filter(
            Lead.created_at >= start_date
        ).count()
        
        # Get qualified leads
        qualified_leads = self.db.query(LeadQualificationSummary).filter(
            LeadQualificationSummary.created_at >= start_date,
            LeadQualificationSummary.is_fully_qualified == True
        ).count()
        
        # Calculate qualification rate
        qualification_rate = (qualified_leads / total_leads * 100) if total_leads > 0 else 0
        
        # Get average score
        avg_score_result = self.db.query(func.avg(LeadQualificationSummary.percentage_score)).filter(
            LeadQualificationSummary.created_at >= start_date
        ).scalar()
        avg_score = float(avg_score_result) if avg_score_result else 0
        
        # Get average completion time
        avg_time_result = self.db.query(func.avg(LeadQualificationSummary.average_response_time)).filter(
            LeadQualificationSummary.created_at >= start_date
        ).scalar()
        avg_completion_time = float(avg_time_result) if avg_time_result else 0
        
        # Get question performance
        question_performance = await self._get_question_performance(start_date)
        
        # Get category performance
        category_performance = await self._get_category_performance(start_date)
        
        return {
            "period_days": days,
            "total_leads": total_leads,
            "qualified_leads": qualified_leads,
            "qualification_rate": qualification_rate,
            "average_score": avg_score,
            "average_completion_time_minutes": avg_completion_time / 60 if avg_completion_time else 0,
            "question_performance": question_performance,
            "category_performance": category_performance
        }
    
    async def _get_question_performance(self, start_date: datetime) -> List[Dict[str, Any]]:
        """Get performance metrics for individual questions"""
        
        # Get question performance data
        question_stats = self.db.query(
            PreQualificationQuestion.question_id,
            PreQualificationQuestion.category,
            PreQualificationQuestion.question_text,
            func.count(LeadResponse.id).label('total_responses'),
            func.sum(func.cast(LeadResponse.is_qualified, 'integer')).label('qualified_responses'),
            func.avg(LeadResponse.score_awarded).label('avg_score'),
            func.avg(LeadResponse.response_time_seconds).label('avg_response_time')
        ).join(LeadResponse).filter(
            LeadResponse.created_at >= start_date
        ).group_by(
            PreQualificationQuestion.question_id,
            PreQualificationQuestion.category,
            PreQualificationQuestion.question_text
        ).all()
        
        performance = []
        for stat in question_stats:
            qualification_rate = (stat.qualified_responses / stat.total_responses * 100) if stat.total_responses > 0 else 0
            
            performance.append({
                "question_id": stat.question_id,
                "category": stat.category,
                "question_text": stat.question_text[:100] + "..." if len(stat.question_text) > 100 else stat.question_text,
                "total_responses": stat.total_responses,
                "qualification_rate": qualification_rate,
                "average_score": float(stat.avg_score) if stat.avg_score else 0,
                "average_response_time": float(stat.avg_response_time) if stat.avg_response_time else 0
            })
        
        return performance
    
    async def _get_category_performance(self, start_date: datetime) -> List[Dict[str, Any]]:
        """Get performance metrics by question category"""
        
        category_stats = self.db.query(
            PreQualificationQuestion.category,
            func.count(LeadResponse.id).label('total_responses'),
            func.sum(func.cast(LeadResponse.is_qualified, 'integer')).label('qualified_responses'),
            func.avg(LeadResponse.score_awarded).label('avg_score')
        ).join(LeadResponse).filter(
            LeadResponse.created_at >= start_date
        ).group_by(PreQualificationQuestion.category).all()
        
        performance = []
        for stat in category_stats:
            qualification_rate = (stat.qualified_responses / stat.total_responses * 100) if stat.total_responses > 0 else 0
            
            performance.append({
                "category": stat.category,
                "total_responses": stat.total_responses,
                "qualification_rate": qualification_rate,
                "average_score": float(stat.avg_score) if stat.avg_score else 0
            })
        
        return performance
