"""
Enhanced DocQA Integration Service
Integrates enhanced vector system with current document processing pipeline
"""

import logging
import os
import asyncio
from typing import Optional, Dict, Any, List
import structlog

from app.core.database.connection import get_db
from app.models.document import Document
from app.models.franchisor import Franchisor
from app.services.enhanced_vector_service import get_enhanced_vector_service
from app.services.docqa_integration_service import get_docqa_integration_service

# Import current DocQA components
from docqa.types import IngestionResult
from docqa.serve import ask_question

logger = structlog.get_logger()


class EnhancedDocQAIntegrationService:
    """Enhanced service for integrating DocQA with enhanced vector capabilities"""
    
    def __init__(self):
        self.enhanced_service = get_enhanced_vector_service()
        self.current_service = get_docqa_integration_service()
        logger.info("Enhanced DocQA Integration Service initialized")
    
    async def process_document_with_enhanced_pipeline(
        self,
        document_id: str,
        file_url: str,
        text_content: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Process document using enhanced pipeline with fallback to current system

        Args:
            document_id: ID of the document
            file_url: S3 URL of the uploaded document
            text_content: Pre-extracted text content (optional)

        Returns:
            Processing result with enhanced metadata
        """
        import time
        start_time = time.time()

        try:
            logger.info("🚀 DOCUMENT INGESTION STARTED",
                       document_id=document_id,
                       file_url=file_url,
                       system="enhanced")

            # Get document metadata from database
            logger.info("📋 Fetching document metadata", document_id=document_id)
            document_metadata = await self._get_document_metadata(document_id)
            if not document_metadata:
                logger.error("❌ DOCUMENT INGESTION FAILED - Document not found",
                           document_id=document_id)
                return {
                    "success": False,
                    "error": f"Document {document_id} not found in database",
                    "enhanced_system_used": False
                }
            
            logger.info("📄 Document metadata retrieved",
                       document_id=document_id,
                       name=document_metadata.get('name'),
                       file_type=document_metadata.get('file_type'),
                       file_size=document_metadata.get('file_size'))

            # Extract text content if not provided
            if not text_content:
                logger.info("📝 Extracting text content from document",
                           document_id=document_id, file_url=file_url)
                text_content = await self._extract_text_from_url(file_url)
                if not text_content:
                    logger.error("❌ DOCUMENT INGESTION FAILED - Text extraction failed",
                               document_id=document_id, file_url=file_url)
                    return {
                        "success": False,
                        "error": "Failed to extract text content from document",
                        "enhanced_system_used": False
                    }
                logger.info("✅ Text content extracted successfully",
                           document_id=document_id,
                           text_length=len(text_content),
                           preview=text_content[:100] + "..." if len(text_content) > 100 else text_content)

            # Process with enhanced system
            logger.info("🧠 Processing with enhanced vector system",
                       document_id=document_id,
                       system="enhanced-2024-2025")

            result = await self.enhanced_service.process_document(
                text=text_content,
                document_id=document_id,
                document_type=document_metadata.get('file_type', 'pdf'),
                metadata={
                    'name': document_metadata.get('name'),
                    'description': document_metadata.get('description'),
                    'franchisor_id': document_metadata.get('franchisor_id'),
                    'file_url': file_url,
                    'file_size': document_metadata.get('file_size'),
                    'source': 'api_upload'
                }
            )

            processing_time = time.time() - start_time

            # Update document processing status
            await self._update_document_processing_status(
                document_id,
                result.get('success', False),
                result.get('error')
            )

            if result.get('success'):
                logger.info("🎉 DOCUMENT INGESTION COMPLETED SUCCESSFULLY",
                           document_id=document_id,
                           chunks_created=result.get('chunks_created', 0),
                           processing_time=f"{processing_time:.2f}s",
                           enhanced_system_used=result.get('enhanced_system_used', False),
                           system_version=result.get('system_version', 'enhanced'))
            else:
                logger.error("❌ DOCUMENT INGESTION FAILED - Enhanced processing failed",
                           document_id=document_id,
                           error=result.get('error'),
                           processing_time=f"{processing_time:.2f}s")

            return result
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error("❌ DOCUMENT INGESTION FAILED - Enhanced system error",
                        document_id=document_id,
                        error=str(e),
                        processing_time=f"{processing_time:.2f}s")

            # Fallback to current system
            logger.info("🔄 FALLBACK: Attempting current system processing",
                       document_id=document_id,
                       fallback_reason=str(e))
            try:
                fallback_start = time.time()
                current_result = await self.current_service.process_document(document_id, file_url)
                fallback_time = time.time() - fallback_start

                if current_result:
                    logger.info("✅ DOCUMENT INGESTION COMPLETED (FALLBACK)",
                               document_id=document_id,
                               chunks_created=current_result.chunks_created,
                               processing_time=f"{fallback_time:.2f}s",
                               system_version="current",
                               fallback_reason=str(e))
                    return {
                        "success": current_result.success,
                        "chunks_created": current_result.chunks_created,
                        "enhanced_system_used": False,
                        "system_version": "current",
                        "fallback_reason": str(e),
                        "document_id": document_id
                    }
            except Exception as fallback_error:
                fallback_time = time.time() - start_time
                logger.error("❌ DOCUMENT INGESTION FAILED - Both systems failed",
                           document_id=document_id,
                           enhanced_error=str(e),
                           fallback_error=str(fallback_error),
                           total_processing_time=f"{fallback_time:.2f}s")

            return {
                "success": False,
                "error": str(e),
                "enhanced_system_used": False,
                "document_id": document_id
            }
    
    async def process_franchisor_brochure_enhanced(
        self,
        franchisor_id: str,
        brochure_url: str
    ) -> Dict[str, Any]:
        """
        Process franchisor brochure with enhanced capabilities

        Args:
            franchisor_id: ID of the franchisor
            brochure_url: S3 URL of the uploaded brochure

        Returns:
            Processing result with enhanced metadata
        """
        import time
        start_time = time.time()

        try:
            logger.info("🚀 FRANCHISOR BROCHURE INGESTION STARTED",
                       franchisor_id=franchisor_id,
                       brochure_url=brochure_url,
                       system="enhanced")

            # Get franchisor metadata
            logger.info("📋 Fetching franchisor metadata", franchisor_id=franchisor_id)
            franchisor_metadata = await self._get_franchisor_metadata(franchisor_id)
            if not franchisor_metadata:
                logger.error("❌ FRANCHISOR BROCHURE INGESTION FAILED - Franchisor not found",
                           franchisor_id=franchisor_id)
                return {
                    "success": False,
                    "error": f"Franchisor {franchisor_id} not found",
                    "enhanced_system_used": False
                }
            
            logger.info("📄 Franchisor metadata retrieved",
                       franchisor_id=franchisor_id,
                       name=franchisor_metadata.get('name'))

            # Extract text content from brochure
            logger.info("📝 Extracting text content from brochure",
                       franchisor_id=franchisor_id,
                       brochure_url=brochure_url)
            text_content = await self._extract_text_from_url(brochure_url)
            if not text_content:
                logger.error("❌ FRANCHISOR BROCHURE INGESTION FAILED - Text extraction failed",
                           franchisor_id=franchisor_id,
                           brochure_url=brochure_url)
                return {
                    "success": False,
                    "error": "Failed to extract text content from brochure",
                    "enhanced_system_used": False
                }

            logger.info("✅ Brochure text content extracted successfully",
                       franchisor_id=franchisor_id,
                       text_length=len(text_content),
                       preview=text_content[:100] + "..." if len(text_content) > 100 else text_content)

            # Process with enhanced system
            logger.info("🧠 Processing brochure with enhanced vector system",
                       franchisor_id=franchisor_id,
                       system="enhanced-2024-2025")

            result = await self.enhanced_service.process_document(
                text=text_content,
                document_id=f"franchisor_{franchisor_id}_brochure",
                document_type="pdf",
                metadata={
                    'franchisor_id': franchisor_id,
                    'franchisor_name': franchisor_metadata.get('name'),
                    'document_type': 'brochure',
                    'file_url': brochure_url,
                    'source': 'franchisor_upload'
                }
            )

            processing_time = time.time() - start_time

            # Update franchisor processing status
            await self._update_franchisor_processing_status(
                franchisor_id,
                result.get('success', False),
                result.get('error')
            )

            if result.get('success'):
                logger.info("🎉 FRANCHISOR BROCHURE INGESTION COMPLETED SUCCESSFULLY",
                           franchisor_id=franchisor_id,
                           chunks_created=result.get('chunks_created', 0),
                           processing_time=f"{processing_time:.2f}s",
                           enhanced_system_used=result.get('enhanced_system_used', False),
                           system_version=result.get('system_version', 'enhanced'))
            else:
                logger.error("❌ FRANCHISOR BROCHURE INGESTION FAILED - Enhanced processing failed",
                           franchisor_id=franchisor_id,
                           error=result.get('error'),
                           processing_time=f"{processing_time:.2f}s")

            return result
            
        except Exception as e:
            logger.error(f"Enhanced franchisor processing failed for {franchisor_id}: {e}")
            
            # Fallback to current system
            try:
                current_result = await self.current_service.process_franchisor_brochure(
                    franchisor_id, brochure_url
                )
                if current_result:
                    return {
                        "success": current_result.success,
                        "chunks_created": current_result.chunks_created,
                        "enhanced_system_used": False,
                        "system_version": "current",
                        "fallback_reason": str(e),
                        "franchisor_id": franchisor_id
                    }
            except Exception as fallback_error:
                logger.error(f"Fallback franchisor processing failed: {fallback_error}")
            
            return {
                "success": False,
                "error": str(e),
                "enhanced_system_used": False,
                "franchisor_id": franchisor_id
            }
    
    async def ask_question_enhanced(
        self,
        question: str,
        context: Optional[str] = None,
        document_id: Optional[str] = None,
        franchisor_id: Optional[str] = None,
        top_k: int = 6,
        similarity_threshold: float = 0.7
    ) -> Dict[str, Any]:
        """
        Ask question with enhanced capabilities and context awareness
        
        Args:
            question: Question to ask
            context: Additional context
            document_id: Specific document to query (optional)
            franchisor_id: Specific franchisor to query (optional)
            top_k: Number of results to retrieve
            similarity_threshold: Similarity threshold
            
        Returns:
            Enhanced answer with metadata
        """
        try:
            # Build enhanced context
            enhanced_context = context or ""
            
            # Add document-specific context
            if document_id:
                doc_metadata = await self._get_document_metadata(document_id)
                if doc_metadata:
                    enhanced_context += f"\nDocument context: {doc_metadata.get('name', '')} - {doc_metadata.get('description', '')}"
            
            # Add franchisor-specific context
            if franchisor_id:
                franchisor_metadata = await self._get_franchisor_metadata(franchisor_id)
                if franchisor_metadata:
                    enhanced_context += f"\nFranchisor context: {franchisor_metadata.get('name', '')} - {franchisor_metadata.get('description', '')}"
            
            # Use enhanced system
            result = await self.enhanced_service.answer_question(
                question=question,
                context=enhanced_context.strip(),
                top_k=top_k,
                similarity_threshold=similarity_threshold
            )
            
            # Add context metadata
            result['context_metadata'] = {
                'document_id': document_id,
                'franchisor_id': franchisor_id,
                'enhanced_context_used': bool(enhanced_context.strip())
            }
            
            return result
            
        except Exception as e:
            logger.error(f"Enhanced question answering failed: {e}")
            
            # Fallback to current system
            try:
                if document_id:
                    return await self.current_service.ask_question_from_document(
                        document_id, question, similarity_threshold
                    )
                elif franchisor_id:
                    return await self.current_service.ask_question_from_franchisor(
                        franchisor_id, question
                    )
                else:
                    # General question
                    answer = ask_question(
                        question=question,
                        top_k=top_k,
                        similarity_threshold=similarity_threshold
                    )
                    return {
                        "success": True,
                        "answer": str(answer),
                        "enhanced_system_used": False,
                        "system_version": "current",
                        "fallback_reason": str(e)
                    }
            except Exception as fallback_error:
                logger.error(f"Fallback QA failed: {fallback_error}")
                return {
                    "success": False,
                    "answer": "I apologize, but I encountered an error while processing your question.",
                    "error": str(e),
                    "enhanced_system_used": False
                }
    
    async def _extract_text_from_url(self, file_url: str) -> Optional[str]:
        """Extract text content from file URL using current system"""
        try:
            # Use the current ingestion system to extract text
            from docqa.ingest import DocumentIngestionService
            
            # Convert S3 filename to full URL if needed
            if not file_url.startswith(('http', 's3://')):
                bucket_name = os.getenv('S3_BUCKET_NAME', 'openxcell-development-public')
                s3_url = f"s3://{bucket_name}/{file_url}"
            else:
                s3_url = file_url
            
            # Use the file loader to extract text
            from ingest.file_loader import FileLoader
            file_loader = FileLoader()
            
            # Download and process file
            file_path = await asyncio.to_thread(file_loader._download_file, s3_url)
            if file_path:
                processing_result = await asyncio.to_thread(file_loader.load_file, file_path)
                if processing_result and processing_result.success:
                    return processing_result.text_content
            
            return None
            
        except Exception as e:
            logger.error(f"Text extraction failed for {file_url}: {e}")
            return None
    
    async def _get_document_metadata(self, document_id: str) -> Optional[Dict[str, Any]]:
        """Get document metadata from database"""
        try:
            async for session in get_db():
                document = await session.get(Document, document_id)
                if document:
                    return {
                        'name': document.name,
                        'description': document.description,
                        'file_type': document.file_type,
                        'file_size': document.file_size,
                        'franchisor_id': document.franchisor_id
                    }
            return None
        except Exception as e:
            logger.error(f"Error getting document metadata: {e}")
            return None
    
    async def _get_franchisor_metadata(self, franchisor_id: str) -> Optional[Dict[str, Any]]:
        """Get franchisor metadata from database"""
        try:
            async for session in get_db():
                franchisor = await session.get(Franchisor, franchisor_id)
                if franchisor:
                    return {
                        'name': franchisor.name,
                        'description': franchisor.description
                    }
            return None
        except Exception as e:
            logger.error(f"Error getting franchisor metadata: {e}")
            return None
    
    async def _update_document_processing_status(
        self, 
        document_id: str, 
        success: bool, 
        error_message: Optional[str] = None
    ):
        """Update document processing status"""
        try:
            async for session in get_db():
                document = await session.get(Document, document_id)
                if document:
                    # Add processing status fields if they exist in the model
                    logger.info(f"Document {document_id} enhanced processing: {'success' if success else 'failed'}")
                    if error_message:
                        logger.error(f"Document {document_id} processing error: {error_message}")
        except Exception as e:
            logger.error(f"Error updating document processing status: {e}")
    
    async def _update_franchisor_processing_status(
        self, 
        franchisor_id: str, 
        success: bool, 
        error_message: Optional[str] = None
    ):
        """Update franchisor processing status"""
        try:
            async for session in get_db():
                franchisor = await session.get(Franchisor, franchisor_id)
                if franchisor:
                    logger.info(f"Franchisor {franchisor_id} enhanced processing: {'success' if success else 'failed'}")
                    if error_message:
                        logger.error(f"Franchisor {franchisor_id} processing error: {error_message}")
        except Exception as e:
            logger.error(f"Error updating franchisor processing status: {e}")


# Global service instance
_enhanced_docqa_service: Optional[EnhancedDocQAIntegrationService] = None


def get_enhanced_docqa_integration_service() -> EnhancedDocQAIntegrationService:
    """Get enhanced DocQA integration service instance"""
    global _enhanced_docqa_service
    
    if _enhanced_docqa_service is None:
        _enhanced_docqa_service = EnhancedDocQAIntegrationService()
    
    return _enhanced_docqa_service
