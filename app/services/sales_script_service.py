"""
Sales Script Service

Service for rendering dynamic sales scripts with Jinja2 templates and context injection.
"""

import re
from datetime import datetime
from typing import Dict, Any, List, Optional
from jinja2 import Template, Environment, BaseLoader, TemplateError
import logging

logger = logging.getLogger(__name__)


class SalesScriptRenderer:
    """Service for rendering sales scripts with dynamic context"""
    
    def __init__(self):
        self.jinja_env = Environment(loader=BaseLoader())
        
    def get_time_based_greeting(self) -> str:
        """Generate time-based greeting"""
        current_hour = datetime.now().hour
        
        if current_hour < 12:
            return "Good morning"
        elif current_hour < 17:
            return "Good afternoon"
        else:
            return "Good evening"
    
    def extract_placeholders(self, script_body: str) -> List[str]:
        """
        Extract all Jinja2 placeholders from script body
        
        Args:
            script_body: Script template with {{placeholder}} syntax
            
        Returns:
            List of placeholder names
        """
        # Find all {{variable}} patterns
        pattern = r'\{\{\s*([^}]+)\s*\}\}'
        matches = re.findall(pattern, script_body)
        
        # Clean up variable names (remove filters, etc.)
        placeholders = []
        for match in matches:
            # Split on | to handle filters like {{name|title}}
            var_name = match.split('|')[0].strip()
            if var_name not in placeholders:
                placeholders.append(var_name)
        
        return placeholders
    
    def validate_context(self, script_body: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate that all required placeholders have values in context
        
        Args:
            script_body: Script template
            context: Context dictionary
            
        Returns:
            Dictionary with validation results
        """
        placeholders = self.extract_placeholders(script_body)
        missing_vars = []
        available_vars = []
        
        for placeholder in placeholders:
            if placeholder in context and context[placeholder] is not None:
                available_vars.append(placeholder)
            else:
                missing_vars.append(placeholder)
        
        return {
            "is_valid": len(missing_vars) == 0,
            "missing_variables": missing_vars,
            "available_variables": available_vars,
            "total_placeholders": len(placeholders)
        }
    
    def prepare_context(self, base_context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare context with automatic variables and formatting
        
        Args:
            base_context: Base context provided by caller
            
        Returns:
            Enhanced context with automatic variables
        """
        enhanced_context = base_context.copy()
        
        # Add automatic time-based greeting if not provided
        if "greeting" not in enhanced_context:
            enhanced_context["greeting"] = self.get_time_based_greeting()
        
        # Add current date/time variables
        now = datetime.now()
        enhanced_context.update({
            "current_date": now.strftime("%Y-%m-%d"),
            "current_time": now.strftime("%H:%M"),
            "current_day": now.strftime("%A"),
            "current_month": now.strftime("%B")
        })
        
        # Ensure string formatting for common variables
        if "lead_first_name" in enhanced_context and enhanced_context["lead_first_name"]:
            enhanced_context["lead_first_name"] = str(enhanced_context["lead_first_name"]).title()
        
        if "company_name" in enhanced_context and enhanced_context["company_name"]:
            enhanced_context["company_name"] = str(enhanced_context["company_name"])
        
        return enhanced_context
    
    def render_sales_script(self, script_body: str, context: Dict[str, Any]) -> str:
        """
        Render sales script with dynamic context injection
        
        Args:
            script_body: Script template with Jinja2 placeholders
            context: Dictionary with values for placeholders
            
        Returns:
            Rendered script text
            
        Raises:
            TemplateError: If template rendering fails
            ValueError: If required variables are missing
        """
        try:
            # Prepare enhanced context
            enhanced_context = self.prepare_context(context)
            
            # Validate context
            validation = self.validate_context(script_body, enhanced_context)
            
            if not validation["is_valid"]:
                logger.warning(f"Missing variables in script context: {validation['missing_variables']}")
                # For missing variables, provide default values
                for var in validation["missing_variables"]:
                    if var not in enhanced_context:
                        enhanced_context[var] = f"[{var}]"  # Placeholder for missing values
            
            # Create and render template
            template = Template(script_body)
            rendered_script = template.render(**enhanced_context)
            
            # Clean up extra whitespace
            rendered_script = re.sub(r'\n\s*\n', '\n\n', rendered_script.strip())
            
            logger.info(f"Successfully rendered script with {len(enhanced_context)} context variables")
            return rendered_script
            
        except TemplateError as e:
            logger.error(f"Template rendering error: {e}")
            raise TemplateError(f"Failed to render script template: {e}")
        except Exception as e:
            logger.error(f"Unexpected error rendering script: {e}")
            raise ValueError(f"Script rendering failed: {e}")
    
    def render_with_fallback(self, script_body: str, context: Dict[str, Any], fallback_script: str = None) -> str:
        """
        Render script with fallback option if rendering fails
        
        Args:
            script_body: Primary script template
            context: Context dictionary
            fallback_script: Fallback script if primary fails
            
        Returns:
            Rendered script or fallback
        """
        try:
            return self.render_sales_script(script_body, context)
        except Exception as e:
            logger.error(f"Primary script rendering failed: {e}")
            
            if fallback_script:
                try:
                    return self.render_sales_script(fallback_script, context)
                except Exception as fallback_error:
                    logger.error(f"Fallback script rendering also failed: {fallback_error}")
            
            # Ultimate fallback - return a basic message
            greeting = self.get_time_based_greeting()
            company_name = context.get("company_name", "our company")
            lead_name = context.get("lead_first_name", "there")
            
            return f"{greeting} {lead_name}, this is a representative calling on behalf of {company_name}. We'd like to discuss a business opportunity with you. Is this a good time to chat?"


class SalesScriptTemplates:
    """Predefined script templates for common scenarios"""
    
    INITIAL_GREETING = """
{{greeting}} {{lead_first_name}}, this is {{name_of_cust_rep}}, calling you on behalf of {{company_name}}. 

You inquired about this opportunity. I want to give you information about this. 

Is this a good time to chat?
""".strip()
    
    FOLLOW_UP_MESSAGE = """
Hi {{lead_first_name}}, this is {{name_of_cust_rep}} from {{company_name}}. 

I wanted to follow up on our previous conversation about the franchise opportunity. 

Do you have a few minutes to continue our discussion?
""".strip()
    
    QUALIFICATION_INTRO = """
Great! I'd like to ask you a few quick questions to better understand your background and interests. This will help me provide you with the most relevant information about the {{company_name}} opportunity.

Are you ready to get started?
""".strip()
    
    APPOINTMENT_SCHEDULING = """
Based on our conversation, you seem like a great fit for the {{company_name}} opportunity! 

I'd like to schedule a more detailed discussion with our franchise development team. This will give you a chance to learn more about the investment, training, and support we provide.

Would you prefer a call this week or next week?
""".strip()
    
    DOCUMENT_QA_INTRO = """
I'd be happy to answer any questions you have about {{company_name}}. 

I have detailed information about our franchise model, investment requirements, training programs, and ongoing support.

What would you like to know more about?
""".strip()


# Global instance for easy access
script_renderer = SalesScriptRenderer()


def render_sales_script(script_body: str, context: Dict[str, Any]) -> str:
    """
    Convenience function for rendering sales scripts
    
    Args:
        script_body: Script template with placeholders
        context: Context dictionary
        
    Returns:
        Rendered script
    """
    return script_renderer.render_sales_script(script_body, context)


def get_time_based_greeting() -> str:
    """Convenience function for getting time-based greeting"""
    return script_renderer.get_time_based_greeting()


def validate_script_context(script_body: str, context: Dict[str, Any]) -> Dict[str, Any]:
    """Convenience function for validating script context"""
    return script_renderer.validate_context(script_body, context)
