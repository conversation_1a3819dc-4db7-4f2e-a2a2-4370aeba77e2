#!/usr/bin/env python3
"""
SMS Lead Qualification Workflow Test

Simulates SMS-based lead qualification to populate pre-qualification tables
with realistic test data for AI agent and human interaction functionality.
"""

import os
import sys
import json
import uuid
import random
from datetime import datetime, timedelta
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import sqlalchemy as sa
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

def get_database_url():
    """Get database URL from environment"""
    db_url = os.getenv('DATABASE_URL')
    if db_url and 'asyncpg' in db_url:
        db_url = db_url.replace('postgresql+asyncpg://', 'postgresql://')
    return db_url or "postgresql://postgres:root@localhost:5432/growthhive"

class SMSQualificationTester:
    """Test SMS-based lead qualification workflow"""
    
    def __init__(self):
        self.engine = create_engine(get_database_url())
        self.SessionLocal = sessionmaker(bind=self.engine)
        
        # SMS conversation scenarios
        self.lead_scenarios = [
            {
                "name": "<PERSON>",
                "email": "<EMAIL>", 
                "phone": "+61-400-123-456",
                "location": "Melbourne, VIC",
                "source": "sms_campaign",
                "responses": [
                    "$200,000+",  # Financial - excellent
                    "Absolutely",  # Financial - excellent
                    "Yes, I have identified an area",  # Location - excellent
                    "Definitely",  # Location - excellent
                    "I can commit to that",  # Training - excellent
                    "Yes, extensive experience",  # Training - excellent
                    "Yes, I own/owned a business",  # Experience - excellent
                    "Yes, full-time commitment",  # Commitment - excellent
                    "Comprehensive ongoing support",  # Support - excellent
                    "Build a sustainable business"  # Goals - excellent
                ],
                "response_times": [45, 32, 28, 15, 67, 89, 43, 21, 55, 38]  # seconds
            },
            {
                "name": "Michael Chen",
                "email": "<EMAIL>",
                "phone": "+**************", 
                "location": "Sydney, NSW",
                "source": "sms_campaign",
                "responses": [
                    "I have about $120,000",  # Financial - partial match
                    "Yes, I understand",  # Financial - partial match
                    "I have some areas in mind",  # Location - exact match
                    "Yes",  # Location - exact match
                    "I can do the training",  # Training - partial match
                    "Some experience",  # Training - exact match
                    "Management experience",  # Experience - partial match
                    "This will be my primary focus",  # Commitment - partial match
                    "Training and marketing support",  # Support - exact match
                    "Achieve financial independence"  # Goals - exact match
                ],
                "response_times": [120, 85, 45, 12, 156, 78, 92, 67, 34, 89]
            },
            {
                "name": "Emma Thompson",
                "email": "<EMAIL>",
                "phone": "+61-400-345-678",
                "location": "Brisbane, QLD", 
                "source": "sms_campaign",
                "responses": [
                    "$75,000 available",  # Financial - partial match
                    "I think so",  # Financial - no match
                    "I need help selecting",  # Location - exact match
                    "Maybe suburban",  # Location - partial match
                    "I can try",  # Training - partial match
                    "No, but willing to learn",  # Training - exact match
                    "No, but eager to learn",  # Experience - exact match
                    "Part-time initially",  # Commitment - no match
                    "Regular check-ins",  # Support - partial match
                    "Build a business"  # Goals - partial match
                ],
                "response_times": [180, 145, 67, 89, 234, 123, 156, 98, 76, 134]
            },
            {
                "name": "David Wilson",
                "email": "<EMAIL>",
                "phone": "+**************",
                "location": "Perth, WA",
                "source": "sms_campaign", 
                "responses": [
                    "$30,000",  # Financial - no match
                    "Not really sure",  # Financial - no match
                    "Anywhere is fine",  # Location - no match
                    "Don't mind",  # Location - no match
                    "Maybe later",  # Training - no match
                    "Never done lawn care",  # Training - no match
                    "No business experience",  # Experience - no match
                    "Just weekends",  # Commitment - no match
                    "Minimal help needed",  # Support - no match
                    "Make some extra money"  # Goals - no match
                ],
                "response_times": [300, 245, 189, 167, 278, 234, 198, 156, 134, 201]
            },
            {
                "name": "Lisa Rodriguez",
                "email": "<EMAIL>",
                "phone": "+**************",
                "location": "Adelaide, SA",
                "source": "sms_campaign",
                "responses": [
                    "$180,000 ready to invest",  # Financial - excellent
                    "Yes, I accept the royalty fees",  # Financial - partial match
                    "I have identified several areas",  # Location - partial match
                    "Yes, suburban is perfect",  # Location - partial match
                    "Absolutely, I can commit",  # Training - partial match
                    "Extensive landscaping experience",  # Training - partial match
                    "I owned a restaurant",  # Experience - partial match
                    "Full-time is my plan",  # Commitment - partial match
                    "I want comprehensive support",  # Support - partial match
                    "Expand to multiple locations"  # Goals - partial match
                ],
                "response_times": [67, 45, 89, 34, 78, 123, 56, 43, 67, 89]
            }
        ]
    
    def evaluate_answer(self, answer: str, expected_answers: list, max_score: int) -> dict:
        """Evaluate SMS answer against expected responses"""
        
        answer_clean = answer.strip().lower()
        
        # Check for exact matches
        for expected in expected_answers:
            if answer_clean == expected.strip().lower():
                return {
                    "is_qualified": True,
                    "score_awarded": max_score,
                    "confidence_score": 1.0,
                    "match_type": "exact",
                    "matched_answer": expected
                }
        
        # Check for partial matches (keyword overlap)
        for expected in expected_answers:
            expected_words = set(expected.lower().split())
            answer_words = set(answer_clean.split())
            
            # Remove common words
            common_words = {"the", "and", "or", "but", "yes", "no", "can", "will", "have", "has", "i", "am", "is", "a", "an"}
            expected_words -= common_words
            answer_words -= common_words
            
            if expected_words and answer_words:
                overlap = len(expected_words.intersection(answer_words))
                if overlap > 0:
                    confidence = overlap / len(expected_words)
                    if confidence >= 0.3:  # Lower threshold for SMS responses
                        score = int(max_score * confidence)
                        return {
                            "is_qualified": True,
                            "score_awarded": score,
                            "confidence_score": confidence,
                            "match_type": "partial",
                            "matched_answer": expected,
                            "matched_keywords": list(expected_words.intersection(answer_words))
                        }
        
        # No match
        return {
            "is_qualified": False,
            "score_awarded": 0,
            "confidence_score": 0.0,
            "match_type": "no_match",
            "matched_answer": None
        }
    
    def create_qualification_session(self, db, lead_id: str) -> tuple[str, list]:
        """Create a qualification session and return session token and questions"""
        # Get active questions
        result = db.execute(text("""
            SELECT id, question_text, expected_answers, score_weight, category, order_sequence
            FROM questions
            WHERE is_active = true AND is_deleted = false
            AND expected_answers IS NOT NULL
            ORDER BY order_sequence
        """))

        questions = []
        for row in result.fetchall():
            questions.append({
                "id": str(row[0]),
                "question_text": row[1],
                "expected_answers": json.loads(row[2]) if isinstance(row[2], str) else row[2],
                "score_weight": row[3],
                "category": row[4],
                "order_sequence": row[5]
            })

        if not questions:
            raise ValueError("No active questions found")

        # Create session
        session_token = f"sms_session_{uuid.uuid4().hex[:12]}"
        expires_at = datetime.utcnow() + timedelta(hours=24)  # 24-hour session for SMS

        session_id = str(uuid.uuid4())
        db.execute(text("""
            INSERT INTO qualification_sessions
            (id, lead_id, session_token, status, current_question_index, questions_order,
             started_at, expires_at)
            VALUES (:id, :lead_id, :token, :status, :index, :order, :started, :expires)
        """), {
            "id": session_id,
            "lead_id": lead_id,
            "token": session_token,
            "status": "active",
            "index": 0,
            "order": json.dumps([q["id"] for q in questions]),
            "started": datetime.utcnow(),
            "expires": expires_at
        })

        return session_token, questions
    
    def simulate_sms_qualification(self, scenario: dict) -> dict:
        """Simulate complete SMS qualification workflow for a lead"""
        
        print(f"\n📱 SMS Qualification: {scenario['name']}")
        print("-" * 50)
        
        with self.SessionLocal() as db:
            # Create lead
            lead_id = str(uuid.uuid4())
            db.execute(text("""
                INSERT INTO leads 
                (id, full_name, email, phone, location, lead_source, qualification_status, status,
                 is_active, is_deleted, created_at, updated_at)
                VALUES (:id, :name, :email, :phone, :location, :source, :qual_status, :status,
                        :is_active, :is_deleted, :created_at, :updated_at)
            """), {
                "id": lead_id,
                "name": scenario["name"],
                "email": scenario["email"],
                "phone": scenario["phone"],
                "location": scenario["location"],
                "source": scenario["source"],
                "qual_status": "in_progress",
                "status": "qualifying",
                "is_active": True,
                "is_deleted": False,
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            })
            
            print(f"   📞 Lead created: {scenario['phone']}")
            
            # Create qualification session
            session_token, questions = self.create_qualification_session(db, lead_id)
            print(f"   🎯 Session started: {session_token}")
            
            # Process each SMS response
            total_score = 0
            max_possible_score = 0
            qualified_responses = 0
            
            for i, (question, answer, response_time) in enumerate(zip(questions, scenario["responses"], scenario["response_times"])):
                # Simulate SMS delay
                answered_at = datetime.utcnow() + timedelta(seconds=sum(scenario["response_times"][:i+1]))
                
                # Evaluate answer
                evaluation = self.evaluate_answer(answer, question["expected_answers"], question["score_weight"])
                
                # Store response
                response_id = str(uuid.uuid4())
                db.execute(text("""
                    INSERT INTO lead_responses 
                    (id, lead_id, question_id, response_text, is_qualified, score_awarded, 
                     confidence_score, response_time_seconds, answered_at, created_at, updated_at)
                    VALUES (:id, :lead_id, :q_id, :response, :qualified, :score, :confidence,
                            :response_time, :answered_at, :created_at, :updated_at)
                """), {
                    "id": response_id,
                    "lead_id": lead_id,
                    "q_id": question["id"],
                    "response": answer,
                    "qualified": evaluation["is_qualified"],
                    "score": evaluation["score_awarded"],
                    "confidence": evaluation["confidence_score"],
                    "response_time": response_time,
                    "answered_at": answered_at,
                    "created_at": datetime.utcnow(),
                    "updated_at": datetime.utcnow()
                })
                
                total_score += evaluation["score_awarded"]
                max_possible_score += question["score_weight"]
                if evaluation["is_qualified"]:
                    qualified_responses += 1
                
                status_icon = "✅" if evaluation["is_qualified"] else "❌"
                print(f"   {status_icon} Q{i+1} ({question['category']}): {evaluation['score_awarded']}/{question['score_weight']} pts")
                print(f"      SMS: \"{answer}\" ({response_time}s)")
                print(f"      Match: {evaluation['match_type']} (confidence: {evaluation['confidence_score']:.2f})")
            
            # Calculate final qualification
            percentage = (total_score / max_possible_score * 100) if max_possible_score > 0 else 0
            is_qualified = percentage >= 80
            
            if percentage >= 90:
                qualification_level = "highly_qualified"
            elif percentage >= 80:
                qualification_level = "qualified"
            elif percentage >= 60:
                qualification_level = "partially_qualified"
            else:
                qualification_level = "unqualified"
            
            # Update lead with final results
            db.execute(text("""
                UPDATE leads 
                SET total_qualification_score = :total_score,
                    max_possible_score = :max_score,
                    qualification_percentage = :percentage,
                    is_fully_qualified = :is_qualified,
                    qualification_level = :level,
                    qualification_status = :status,
                    qualified_at = :qualified_at,
                    updated_at = :updated_at
                WHERE id = :lead_id
            """), {
                "total_score": total_score,
                "max_score": max_possible_score,
                "percentage": percentage,
                "is_qualified": is_qualified,
                "level": qualification_level,
                "status": "qualified" if is_qualified else "disqualified",
                "qualified_at": datetime.utcnow(),
                "updated_at": datetime.utcnow(),
                "lead_id": lead_id
            })
            
            # Mark session as completed
            db.execute(text("""
                UPDATE qualification_sessions
                SET status = 'completed', completed_at = :completed_at
                WHERE session_token = :token
            """), {
                "completed_at": datetime.utcnow(),
                "token": session_token
            })
            
            db.commit()
            
            print(f"\n   📊 FINAL RESULT:")
            print(f"      Score: {total_score}/{max_possible_score} ({percentage:.1f}%)")
            print(f"      Status: {qualification_level.upper()}")
            print(f"      Qualified: {'✅ YES' if is_qualified else '❌ NO'}")
            print(f"      Responses: {qualified_responses}/{len(questions)} qualified")
            
            return {
                "lead_id": lead_id,
                "session_token": session_token,
                "total_score": total_score,
                "max_possible_score": max_possible_score,
                "percentage": percentage,
                "is_qualified": is_qualified,
                "qualification_level": qualification_level,
                "qualified_responses": qualified_responses,
                "total_responses": len(questions)
            }
    
    def run_sms_qualification_tests(self):
        """Run all SMS qualification test scenarios"""
        
        print("📱 SMS LEAD QUALIFICATION WORKFLOW TESTING")
        print("=" * 70)
        print("Simulating AI agent and human interaction via SMS")
        
        results = []
        
        for scenario in self.lead_scenarios:
            result = self.simulate_sms_qualification(scenario)
            results.append(result)
        
        # Display summary
        print(f"\n📊 SMS QUALIFICATION SUMMARY")
        print("=" * 50)
        
        total_leads = len(results)
        qualified_leads = len([r for r in results if r["is_qualified"]])
        avg_score = sum(r["percentage"] for r in results) / len(results)
        
        print(f"Total Leads Tested: {total_leads}")
        print(f"Qualified Leads: {qualified_leads} ({qualified_leads/total_leads*100:.1f}%)")
        print(f"Average Score: {avg_score:.1f}%")
        
        # Show qualification breakdown
        levels = {}
        for result in results:
            level = result["qualification_level"]
            levels[level] = levels.get(level, 0) + 1
        
        print(f"\nQualification Levels:")
        for level, count in levels.items():
            print(f"  • {level.replace('_', ' ').title()}: {count}")
        
        return results
    
    def verify_data_population(self):
        """Verify that data was properly populated in all tables"""
        
        print(f"\n🔍 VERIFYING DATA POPULATION")
        print("=" * 50)
        
        with self.SessionLocal() as db:
            # Check leads
            result = db.execute(text("SELECT COUNT(*) FROM leads WHERE lead_source = 'sms_campaign'"))
            sms_leads = result.fetchone()[0]
            
            # Check responses
            result = db.execute(text("SELECT COUNT(*) FROM lead_responses WHERE score_awarded IS NOT NULL"))
            scored_responses = result.fetchone()[0]
            
            # Check sessions
            result = db.execute(text("SELECT COUNT(*) FROM qualification_sessions WHERE session_token LIKE 'sms_session_%'"))
            sms_sessions = result.fetchone()[0]
            
            # Check qualified leads
            result = db.execute(text("SELECT COUNT(*) FROM leads WHERE is_fully_qualified = true"))
            qualified_count = result.fetchone()[0]
            
            print(f"✅ SMS Leads Created: {sms_leads}")
            print(f"✅ Scored Responses: {scored_responses}")
            print(f"✅ SMS Sessions: {sms_sessions}")
            print(f"✅ Qualified Leads: {qualified_count}")
            
            # Show sample data
            print(f"\n📋 Sample Lead Data:")
            result = db.execute(text("""
                SELECT full_name, phone, qualification_percentage, qualification_level
                FROM leads 
                WHERE lead_source = 'sms_campaign'
                ORDER BY qualification_percentage DESC
                LIMIT 3
            """))
            
            for row in result.fetchall():
                name, phone, percentage, level = row
                print(f"  • {name} ({phone}): {percentage:.1f}% - {level}")

def main():
    """Main test function"""
    
    print("🚀 SMS LEAD QUALIFICATION TESTING")
    print("=" * 70)
    
    try:
        tester = SMSQualificationTester()
        
        # Test database connection
        with tester.engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        print("✅ Database connection successful")
        
        # Run SMS qualification tests
        results = tester.run_sms_qualification_tests()
        
        # Verify data population
        tester.verify_data_population()
        
        print(f"\n🎉 SMS QUALIFICATION TESTING COMPLETED!")
        print(f"\n✅ Pre-qualification tables populated with:")
        print(f"   • {len(results)} SMS leads with complete qualification data")
        print(f"   • {sum(len(tester.lead_scenarios[0]['responses']) for _ in results)} lead responses with scoring")
        print(f"   • {len(results)} qualification sessions")
        print(f"   • Real SMS conversation simulation with response times")
        
        print(f"\n📱 SMS Integration Ready:")
        print(f"   • Database schema supports SMS workflows")
        print(f"   • Session management for multi-message conversations")
        print(f"   • Real-time scoring and qualification")
        print(f"   • Response time tracking for SMS analytics")
        
    except Exception as e:
        print(f"❌ Testing failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
