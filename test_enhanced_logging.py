#!/usr/bin/env python3
"""
Test script to verify enhanced logging is working
"""

import asyncio
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.append(str(project_root))

async def test_enhanced_logging():
    """Test the enhanced logging system"""
    print("🧪 Testing Enhanced Logging System...")
    
    try:
        # Test enhanced vector service logging
        from app.services.enhanced_vector_service import get_enhanced_vector_service
        
        service = get_enhanced_vector_service()
        await service.initialize()
        
        # Test document processing with logging
        test_text = """
        Coochie Hydrogreen is an innovative eco-friendly franchise opportunity 
        specializing in sustainable water treatment solutions. The initial 
        investment requirement ranges from $150,000 to $300,000, which includes 
        equipment, training, and initial marketing support.
        """
        
        print("\n📝 Testing document processing with enhanced logging...")
        result = await service.process_document(
            text=test_text,
            document_id="test_logging_doc_123",
            document_type="pdf",
            metadata={"test": True, "source": "logging_test"}
        )
        
        print(f"✅ Processing result: {result.get('success', False)}")
        print(f"📊 Enhanced system used: {result.get('enhanced_system_used', False)}")
        print(f"⏱️  Processing time: {result.get('processing_time', 0):.2f}s")
        
        # Test enhanced DocQA integration logging
        from app.services.enhanced_docqa_integration_service import get_enhanced_docqa_integration_service
        
        docqa_service = get_enhanced_docqa_integration_service()
        
        print("\n🤖 Testing enhanced DocQA integration logging...")
        qa_result = await docqa_service.ask_question_enhanced(
            question="What is the investment requirement for Coochie Hydrogreen?",
            context="Testing enhanced logging system"
        )
        
        print(f"✅ QA result success: {qa_result.get('success', False)}")
        print(f"📊 Enhanced system used: {qa_result.get('enhanced_system_used', False)}")
        print(f"💬 Answer preview: {qa_result.get('answer', '')[:100]}...")
        
        print("\n🎉 Enhanced logging test completed successfully!")
        print("\n📋 What to look for in logs:")
        print("   🚀 DOCUMENT INGESTION STARTED")
        print("   📝 Extracting text content")
        print("   🧠 Processing with enhanced vector system")
        print("   🎉 DOCUMENT INGESTION COMPLETED SUCCESSFULLY")
        print("   📊 Performance metrics and timing")
        print("   🔄 Fallback events (if any)")
        
        return True
        
    except Exception as e:
        print(f"❌ Enhanced logging test failed: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_enhanced_logging())
    if success:
        print("\n✅ Enhanced logging system is working correctly!")
    else:
        print("\n❌ Enhanced logging system test failed!")
