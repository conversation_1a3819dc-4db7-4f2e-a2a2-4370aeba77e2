#!/usr/bin/env python3
"""
Test Lead Responses Storage

Comprehensive test to ensure lead_responses are properly stored in the database
and accessible through the FastAPI application, compatible with HOST=0.0.0.0 configuration.
"""

import os
import sys
import json
import uuid
import asyncio
import requests
from datetime import datetime, timedelta
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import sqlalchemy as sa
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

def get_database_url():
    """Get database URL from environment"""
    db_url = os.getenv('DATABASE_URL')
    if db_url and 'asyncpg' in db_url:
        db_url = db_url.replace('postgresql+asyncpg://', 'postgresql://')
    return db_url or "postgresql://postgres:root@localhost:5432/growthhive"

class LeadResponseStorageTester:
    """Test lead response storage functionality"""
    
    def __init__(self):
        self.engine = create_engine(get_database_url())
        self.SessionLocal = sessionmaker(bind=self.engine)
        
        # Test scenarios for lead responses
        self.test_scenarios = [
            {
                "lead_name": "Test Lead 1",
                "email": "<EMAIL>",
                "phone": "+61-400-111-111",
                "responses": [
                    {
                        "question_type": "financial",
                        "response_text": "$150,000 available for investment",
                        "expected_score": 7,  # Partial match
                        "expected_qualified": False
                    },
                    {
                        "question_type": "financial", 
                        "response_text": "Yes, I accept the royalty payments",
                        "expected_score": 5,  # Partial match
                        "expected_qualified": True
                    },
                    {
                        "question_type": "location",
                        "response_text": "I have some areas in mind",
                        "expected_score": 8,  # Exact match
                        "expected_qualified": True
                    }
                ]
            },
            {
                "lead_name": "Test Lead 2", 
                "email": "<EMAIL>",
                "phone": "+61-400-222-222",
                "responses": [
                    {
                        "question_type": "training",
                        "response_text": "Yes, I can commit to the training",
                        "expected_score": 4,  # Partial match
                        "expected_qualified": True
                    },
                    {
                        "question_type": "experience",
                        "response_text": "I have management experience",
                        "expected_score": 5,  # Partial match
                        "expected_qualified": True
                    }
                ]
            }
        ]
    
    def create_test_lead(self, lead_data: dict) -> str:
        """Create a test lead and return lead_id"""
        
        with self.SessionLocal() as db:
            lead_id = str(uuid.uuid4())
            
            db.execute(text("""
                INSERT INTO leads 
                (id, full_name, email, phone, lead_source, qualification_status, status,
                 is_active, is_deleted, created_at, updated_at)
                VALUES (:id, :name, :email, :phone, :source, :qual_status, :status,
                        :is_active, :is_deleted, :created_at, :updated_at)
            """), {
                "id": lead_id,
                "name": lead_data["lead_name"],
                "email": lead_data["email"],
                "phone": lead_data["phone"],
                "source": "script_test",
                "qual_status": "in_progress",
                "status": "qualifying",
                "is_active": True,
                "is_deleted": False,
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            })
            
            db.commit()
            print(f"   ✅ Created test lead: {lead_data['lead_name']} ({lead_id[:8]}...)")
            return lead_id
    
    def get_question_by_type(self, question_type: str) -> dict:
        """Get a question by type for testing"""
        
        with self.SessionLocal() as db:
            result = db.execute(text("""
                SELECT id, question_text, expected_answers, score_weight, category
                FROM questions 
                WHERE question_type = :type AND is_active = true AND is_deleted = false
                LIMIT 1
            """), {"type": question_type})
            
            row = result.fetchone()
            if not row:
                raise ValueError(f"No question found for type: {question_type}")
            
            return {
                "id": str(row[0]),
                "question_text": row[1],
                "expected_answers": json.loads(row[2]) if isinstance(row[2], str) else row[2],
                "score_weight": row[3],
                "category": row[4]
            }
    
    def evaluate_and_store_response(self, lead_id: str, question: dict, response_data: dict) -> dict:
        """Evaluate and store a lead response"""
        
        response_text = response_data["response_text"]
        expected_answers = question["expected_answers"]
        max_score = question["score_weight"]
        
        # Simple evaluation logic
        answer_clean = response_text.strip().lower()
        
        # Check for exact matches
        for expected in expected_answers:
            if answer_clean == expected.strip().lower():
                evaluation = {
                    "is_qualified": True,
                    "score_awarded": max_score,
                    "confidence_score": 1.0,
                    "match_type": "exact"
                }
                break
        else:
            # Check for partial matches
            for expected in expected_answers:
                expected_words = set(expected.lower().split())
                answer_words = set(answer_clean.split())
                
                # Remove common words
                common_words = {"the", "and", "or", "but", "yes", "no", "can", "will", "have", "has", "i", "am", "is", "a", "an"}
                expected_words -= common_words
                answer_words -= common_words
                
                if expected_words and answer_words:
                    overlap = len(expected_words.intersection(answer_words))
                    if overlap > 0:
                        confidence = overlap / len(expected_words)
                        if confidence >= 0.3:
                            score = int(max_score * confidence)
                            evaluation = {
                                "is_qualified": True,
                                "score_awarded": score,
                                "confidence_score": confidence,
                                "match_type": "partial"
                            }
                            break
            else:
                # No match
                evaluation = {
                    "is_qualified": False,
                    "score_awarded": 0,
                    "confidence_score": 0.0,
                    "match_type": "no_match"
                }
        
        # Store response in database
        with self.SessionLocal() as db:
            response_id = str(uuid.uuid4())
            
            db.execute(text("""
                INSERT INTO lead_responses 
                (id, lead_id, question_id, response_text, is_qualified, score_awarded, 
                 confidence_score, answered_at, created_at, updated_at)
                VALUES (:id, :lead_id, :q_id, :response, :qualified, :score, :confidence,
                        :answered_at, :created_at, :updated_at)
            """), {
                "id": response_id,
                "lead_id": lead_id,
                "q_id": question["id"],
                "response": response_text,
                "qualified": evaluation["is_qualified"],
                "score": evaluation["score_awarded"],
                "confidence": evaluation["confidence_score"],
                "answered_at": datetime.utcnow(),
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            })
            
            db.commit()
            
            print(f"   📝 Stored response: \"{response_text[:30]}...\"")
            print(f"      Score: {evaluation['score_awarded']}/{max_score} | Qualified: {evaluation['is_qualified']}")
            
            return {
                "response_id": response_id,
                "evaluation": evaluation,
                "stored_successfully": True
            }
    
    def verify_stored_responses(self, lead_id: str) -> dict:
        """Verify that responses were stored correctly"""
        
        with self.SessionLocal() as db:
            result = db.execute(text("""
                SELECT 
                    lr.id,
                    lr.response_text,
                    lr.score_awarded,
                    lr.is_qualified,
                    lr.confidence_score,
                    lr.answered_at,
                    q.question_text,
                    q.category
                FROM lead_responses lr
                JOIN questions q ON lr.question_id = q.id
                WHERE lr.lead_id = :lead_id
                ORDER BY lr.created_at
            """), {"lead_id": lead_id})
            
            responses = []
            total_score = 0
            qualified_count = 0
            
            for row in result.fetchall():
                response_data = {
                    "id": str(row[0]),
                    "response_text": row[1],
                    "score_awarded": row[2],
                    "is_qualified": row[3],
                    "confidence_score": row[4],
                    "answered_at": row[5],
                    "question_text": row[6],
                    "category": row[7]
                }
                responses.append(response_data)
                total_score += row[2] or 0
                if row[3]:
                    qualified_count += 1
            
            return {
                "responses": responses,
                "total_responses": len(responses),
                "total_score": total_score,
                "qualified_responses": qualified_count,
                "verification_successful": len(responses) > 0
            }
    
    def test_api_access(self) -> bool:
        """Test if the API can access stored lead responses"""
        
        try:
            # Try to connect to the FastAPI server
            # Note: This assumes the server is running on localhost
            # Adjust the URL based on your HOST configuration
            
            host = os.getenv('HOST', '0.0.0.0')
            port = os.getenv('PORT', '8000')
            
            # If HOST is 0.0.0.0, use localhost for testing
            test_host = 'localhost' if host == '0.0.0.0' else host
            api_url = f"http://{test_host}:{port}"
            
            print(f"   🌐 Testing API access at: {api_url}")
            
            # Test basic API connectivity
            response = requests.get(f"{api_url}/api/v1/", timeout=5)
            
            if response.status_code == 200:
                print(f"   ✅ API is accessible")
                return True
            else:
                print(f"   ⚠️  API returned status: {response.status_code}")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"   ⚠️  API not accessible: {e}")
            print(f"   💡 Make sure your FastAPI server is running with: uvicorn app.main:app --host {host} --port {port}")
            return False
    
    def run_storage_tests(self):
        """Run comprehensive lead response storage tests"""
        
        print("🧪 LEAD RESPONSE STORAGE TESTING")
        print("=" * 70)
        print("Testing lead_responses storage with HOST=0.0.0.0 configuration")
        
        # Test database connectivity
        try:
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            print("✅ Database connection successful")
        except Exception as e:
            print(f"❌ Database connection failed: {e}")
            return
        
        # Test API accessibility
        api_accessible = self.test_api_access()
        
        total_responses_stored = 0
        total_leads_processed = 0
        
        # Process each test scenario
        for i, scenario in enumerate(self.test_scenarios, 1):
            print(f"\n📋 Test Scenario {i}: {scenario['lead_name']}")
            print("-" * 50)
            
            try:
                # Create test lead
                lead_id = self.create_test_lead(scenario)
                total_leads_processed += 1
                
                # Process each response
                for response_data in scenario["responses"]:
                    try:
                        # Get question for this response
                        question = self.get_question_by_type(response_data["question_type"])
                        
                        # Store response
                        result = self.evaluate_and_store_response(lead_id, question, response_data)
                        
                        if result["stored_successfully"]:
                            total_responses_stored += 1
                        
                    except Exception as e:
                        print(f"   ❌ Error storing response: {e}")
                
                # Verify stored responses
                verification = self.verify_stored_responses(lead_id)
                
                if verification["verification_successful"]:
                    print(f"   ✅ Verification: {verification['total_responses']} responses stored")
                    print(f"   📊 Total Score: {verification['total_score']} | Qualified: {verification['qualified_responses']}")
                else:
                    print(f"   ❌ Verification failed: No responses found")
                
            except Exception as e:
                print(f"   ❌ Error in scenario {i}: {e}")
        
        # Final summary
        print(f"\n📊 STORAGE TEST SUMMARY")
        print("=" * 50)
        print(f"✅ Leads Processed: {total_leads_processed}")
        print(f"✅ Responses Stored: {total_responses_stored}")
        print(f"🌐 API Accessible: {'Yes' if api_accessible else 'No'}")
        
        # Database verification
        with self.SessionLocal() as db:
            result = db.execute(text("""
                SELECT COUNT(*) FROM lead_responses 
                WHERE lead_id IN (
                    SELECT id FROM leads WHERE lead_source = 'script_test'
                )
            """))
            
            db_response_count = result.fetchone()[0]
            print(f"🗄️  Database Verification: {db_response_count} responses in database")
        
        # Show sample stored data
        print(f"\n📋 Sample Stored Responses:")
        with self.SessionLocal() as db:
            result = db.execute(text("""
                SELECT 
                    l.full_name,
                    lr.response_text,
                    lr.score_awarded,
                    lr.is_qualified,
                    q.category
                FROM lead_responses lr
                JOIN leads l ON lr.lead_id = l.id
                JOIN questions q ON lr.question_id = q.id
                WHERE l.lead_source = 'script_test'
                ORDER BY lr.created_at DESC
                LIMIT 5
            """))
            
            for row in result.fetchall():
                name, response, score, qualified, category = row
                status = "✅" if qualified else "❌"
                print(f"   {status} {name}: \"{response[:40]}...\" ({category}) - {score} pts")
        
        return {
            "leads_processed": total_leads_processed,
            "responses_stored": total_responses_stored,
            "api_accessible": api_accessible,
            "database_verified": db_response_count > 0
        }

def main():
    """Main test function"""
    
    print("🚀 LEAD RESPONSE STORAGE TESTING")
    print("=" * 70)
    print("Ensuring lead_responses are properly stored with HOST=0.0.0.0")
    
    try:
        tester = LeadResponseStorageTester()
        results = tester.run_storage_tests()
        
        print(f"\n🎉 STORAGE TESTING COMPLETED!")
        
        if all([
            results["leads_processed"] > 0,
            results["responses_stored"] > 0,
            results["database_verified"]
        ]):
            print(f"\n✅ ALL TESTS PASSED:")
            print(f"   • Lead responses are being stored correctly")
            print(f"   • Database integration is working")
            print(f"   • Compatible with HOST=0.0.0.0 configuration")
            
            if results["api_accessible"]:
                print(f"   • FastAPI server is accessible")
            else:
                print(f"   • FastAPI server not running (start with: uvicorn app.main:app --host 0.0.0.0 --port 8000)")
        else:
            print(f"\n⚠️  SOME TESTS FAILED:")
            print(f"   • Check database connection")
            print(f"   • Verify table schemas")
            print(f"   • Ensure proper permissions")
        
        print(f"\n📝 Next Steps:")
        print(f"   1. Start your FastAPI server: uvicorn app.main:app --host 0.0.0.0 --port 8000")
        print(f"   2. Test API endpoints for lead responses")
        print(f"   3. Integrate with SMS webhook for real-time storage")
        
    except Exception as e:
        print(f"❌ Storage testing failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
