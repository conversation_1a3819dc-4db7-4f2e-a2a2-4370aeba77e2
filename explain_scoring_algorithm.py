#!/usr/bin/env python3
"""
Lead Qualification Scoring Algorithm Explanation

Detailed explanation and examples of how score_awarded and confidence_score
are calculated in the lead qualification system.
"""

import json
from typing import List, Dict, Any

class ScoringExplainer:
    """Explains the scoring algorithm with examples"""
    
    def evaluate_answer_with_explanation(self, answer: str, expected_answers: List[str], max_score: int) -> Dict[str, Any]:
        """
        Evaluate an answer with detailed explanation of scoring logic
        
        Args:
            answer: The lead's response text
            expected_answers: List of acceptable answers for the question
            max_score: Maximum points possible for this question
            
        Returns:
            Dictionary with evaluation results and explanation
        """
        
        print(f"\n🔍 SCORING ANALYSIS")
        print("=" * 60)
        print(f"📝 Lead Answer: \"{answer}\"")
        print(f"✅ Expected Answers: {expected_answers}")
        print(f"🎯 Max Possible Score: {max_score}")
        print("-" * 60)
        
        answer_clean = answer.strip().lower()
        
        # STEP 1: Check for exact matches
        print(f"\n📍 STEP 1: Exact Match Check")
        for i, expected in enumerate(expected_answers, 1):
            expected_clean = expected.strip().lower()
            print(f"   {i}. Comparing \"{answer_clean}\" with \"{expected_clean}\"")
            
            if answer_clean == expected_clean:
                result = {
                    "is_qualified": True,
                    "score_awarded": max_score,
                    "confidence_score": 1.0,
                    "match_type": "exact",
                    "matched_answer": expected,
                    "explanation": f"Exact match found with '{expected}'"
                }
                
                print(f"   ✅ EXACT MATCH FOUND!")
                print(f"   🎯 Score Awarded: {max_score}/{max_score} (100%)")
                print(f"   📊 Confidence: 1.0 (100%)")
                print(f"   🔗 Matched Answer: \"{expected}\"")
                
                return result
        
        print(f"   ❌ No exact matches found")
        
        # STEP 2: Check for partial matches (keyword overlap)
        print(f"\n📍 STEP 2: Partial Match Check")
        
        # Remove common words that don't add meaning
        common_words = {"the", "and", "or", "but", "yes", "no", "can", "will", "have", "has", "i", "am", "is", "a", "an", "to", "for", "of", "in", "on", "at", "by", "with"}
        
        answer_words = set(answer_clean.split())
        answer_words_filtered = answer_words - common_words
        
        print(f"   📝 Answer words: {list(answer_words)}")
        print(f"   🔍 Filtered answer words: {list(answer_words_filtered)}")
        
        best_match = None
        best_confidence = 0
        
        for i, expected in enumerate(expected_answers, 1):
            expected_words = set(expected.lower().split())
            expected_words_filtered = expected_words - common_words
            
            print(f"\n   {i}. Analyzing \"{expected}\":")
            print(f"      Expected words: {list(expected_words)}")
            print(f"      Filtered expected words: {list(expected_words_filtered)}")
            
            if expected_words_filtered and answer_words_filtered:
                # Calculate overlap
                overlap = expected_words_filtered.intersection(answer_words_filtered)
                overlap_count = len(overlap)
                
                print(f"      Overlapping words: {list(overlap)} ({overlap_count} words)")
                
                if overlap_count > 0:
                    # Confidence = overlapping words / total expected words
                    confidence = overlap_count / len(expected_words_filtered)
                    print(f"      Confidence calculation: {overlap_count}/{len(expected_words_filtered)} = {confidence:.3f}")
                    
                    # Minimum threshold for partial match (30%)
                    if confidence >= 0.3:
                        print(f"      ✅ Meets threshold (≥30%)")
                        
                        if confidence > best_confidence:
                            best_confidence = confidence
                            best_match = expected
                            print(f"      🏆 New best match!")
                    else:
                        print(f"      ❌ Below threshold (<30%)")
                else:
                    print(f"      ❌ No word overlap")
            else:
                print(f"      ⚠️  No meaningful words to compare")
        
        # STEP 3: Calculate final score
        print(f"\n📍 STEP 3: Final Score Calculation")
        
        if best_match and best_confidence >= 0.3:
            # Score = max_score × confidence_percentage
            score_awarded = int(max_score * best_confidence)
            
            result = {
                "is_qualified": True,
                "score_awarded": score_awarded,
                "confidence_score": best_confidence,
                "match_type": "partial",
                "matched_answer": best_match,
                "matched_keywords": list(expected_words_filtered.intersection(answer_words_filtered)),
                "explanation": f"Partial match with '{best_match}' ({best_confidence:.1%} confidence)"
            }
            
            print(f"   ✅ PARTIAL MATCH FOUND!")
            print(f"   🎯 Score Calculation: {max_score} × {best_confidence:.3f} = {score_awarded}")
            print(f"   📊 Score Awarded: {score_awarded}/{max_score} ({score_awarded/max_score*100:.1f}%)")
            print(f"   📈 Confidence: {best_confidence:.3f} ({best_confidence*100:.1f}%)")
            print(f"   🔗 Best Match: \"{best_match}\"")
            
            return result
        else:
            result = {
                "is_qualified": False,
                "score_awarded": 0,
                "confidence_score": 0.0,
                "match_type": "no_match",
                "matched_answer": None,
                "explanation": "No acceptable match found (below 30% threshold)"
            }
            
            print(f"   ❌ NO MATCH FOUND")
            print(f"   🎯 Score Awarded: 0/{max_score} (0%)")
            print(f"   📊 Confidence: 0.0 (0%)")
            print(f"   💡 Reason: No match above 30% confidence threshold")
            
            return result
    
    def demonstrate_scoring_examples(self):
        """Demonstrate scoring with real examples"""
        
        print("🧮 LEAD QUALIFICATION SCORING ALGORITHM")
        print("=" * 80)
        print("Detailed explanation of score_awarded and confidence_score calculation")
        
        # Example scenarios
        examples = [
            {
                "question": "What is your available investment budget?",
                "expected_answers": ["$50,000-$100,000", "$100,000-$200,000", "$200,000+"],
                "max_score": 15,
                "test_answers": [
                    "$200,000+",  # Exact match
                    "I have $150,000 available",  # Partial match
                    "$30,000",  # No match
                    "Two hundred thousand dollars"  # No match (different format)
                ]
            },
            {
                "question": "Are you comfortable with royalty payments?",
                "expected_answers": ["Yes", "Absolutely", "I understand and accept"],
                "max_score": 10,
                "test_answers": [
                    "Yes",  # Exact match
                    "I understand",  # Partial match
                    "I accept the terms",  # Partial match
                    "Not sure"  # No match
                ]
            },
            {
                "question": "Can you commit to full-time operation?",
                "expected_answers": ["Yes, full-time commitment", "Yes, this will be my primary focus"],
                "max_score": 10,
                "test_answers": [
                    "Yes, full-time commitment",  # Exact match
                    "This will be my primary focus",  # Partial match
                    "Full-time operation",  # Partial match
                    "Part-time only"  # No match
                ]
            }
        ]
        
        for example_num, example in enumerate(examples, 1):
            print(f"\n🎯 EXAMPLE {example_num}: {example['question']}")
            print("=" * 80)
            
            for answer in example["test_answers"]:
                result = self.evaluate_answer_with_explanation(
                    answer, 
                    example["expected_answers"], 
                    example["max_score"]
                )
                
                print(f"\n📋 SUMMARY:")
                print(f"   Result: {result['match_type'].upper()}")
                print(f"   Score: {result['score_awarded']}/{example['max_score']}")
                print(f"   Confidence: {result['confidence_score']:.3f}")
                print(f"   Qualified: {'✅ YES' if result['is_qualified'] else '❌ NO'}")
                
                input(f"\n⏸️  Press Enter to continue to next example...")
    
    def explain_algorithm_details(self):
        """Explain the technical details of the algorithm"""
        
        print(f"\n🔬 ALGORITHM TECHNICAL DETAILS")
        print("=" * 80)
        
        print(f"\n📊 SCORING FORMULA:")
        print(f"   score_awarded = max_score × confidence_score")
        print(f"   confidence_score = overlapping_words / total_expected_words")
        
        print(f"\n🎯 MATCHING LOGIC:")
        print(f"   1. EXACT MATCH:")
        print(f"      • Direct string comparison (case-insensitive)")
        print(f"      • Score: 100% of max_score")
        print(f"      • Confidence: 1.0 (100%)")
        
        print(f"\n   2. PARTIAL MATCH:")
        print(f"      • Word-by-word comparison")
        print(f"      • Remove common words (the, and, or, etc.)")
        print(f"      • Calculate overlap percentage")
        print(f"      • Minimum threshold: 30%")
        print(f"      • Score: max_score × confidence_percentage")
        
        print(f"\n   3. NO MATCH:")
        print(f"      • No overlap or below 30% threshold")
        print(f"      • Score: 0")
        print(f"      • Confidence: 0.0")
        
        print(f"\n🔍 WORD FILTERING:")
        print(f"   Common words removed: the, and, or, but, yes, no, can, will,")
        print(f"   have, has, i, am, is, a, an, to, for, of, in, on, at, by, with")
        
        print(f"\n📈 CONFIDENCE CALCULATION EXAMPLES:")
        print(f"   Answer: 'I understand the royalty payments'")
        print(f"   Expected: 'I understand and accept'")
        print(f"   ")
        print(f"   Step 1: Remove common words")
        print(f"   Answer words: ['understand', 'royalty', 'payments']")
        print(f"   Expected words: ['understand', 'accept']")
        print(f"   ")
        print(f"   Step 2: Find overlap")
        print(f"   Overlapping: ['understand'] = 1 word")
        print(f"   ")
        print(f"   Step 3: Calculate confidence")
        print(f"   Confidence = 1 / 2 = 0.5 (50%)")
        print(f"   ")
        print(f"   Step 4: Calculate score")
        print(f"   Score = 10 × 0.5 = 5 points")
        
        print(f"\n⚖️  THRESHOLD RATIONALE:")
        print(f"   • 30% minimum prevents random word matches")
        print(f"   • Allows for natural language variations")
        print(f"   • Balances strictness with flexibility")
        print(f"   • Suitable for SMS/conversational responses")

def main():
    """Main demonstration function"""
    
    explainer = ScoringExplainer()
    
    print("🎓 LEAD QUALIFICATION SCORING EXPLAINED")
    print("=" * 80)
    print("Learn how score_awarded and confidence_score are calculated")
    
    choice = input(f"\nChoose demonstration:\n1. Algorithm details\n2. Interactive examples\n3. Both\n\nEnter choice (1-3): ")
    
    if choice in ["1", "3"]:
        explainer.explain_algorithm_details()
    
    if choice in ["2", "3"]:
        explainer.demonstrate_scoring_examples()
    
    print(f"\n🎉 SCORING EXPLANATION COMPLETED!")
    print(f"\n✅ Key Takeaways:")
    print(f"   • Exact matches get 100% score and confidence")
    print(f"   • Partial matches use word overlap percentage")
    print(f"   • 30% minimum threshold prevents false positives")
    print(f"   • Score = max_score × confidence_percentage")
    print(f"   • System handles natural language variations")

if __name__ == "__main__":
    main()
